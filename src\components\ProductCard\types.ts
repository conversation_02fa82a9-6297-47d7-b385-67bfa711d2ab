import { Product } from "@/data/products";

export interface ProductCardProps {
  product: Product;
}

export interface ProductImageProps {
  product: Product;
  isHovered: boolean;
}

export interface ProductOverlayProps {
  product: Product;
  isHovered: boolean;
  onAddToCart: (e?: React.MouseEvent) => void;
}

export interface ProductDialogProps {
  product: Product;
  onAddToCart: (e?: React.MouseEvent) => void;
}

export interface ProductPriceProps {
  price: number;
  className?: string;
}
