import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * A reusable error boundary component that catches JavaScript errors in its child component tree.
 * It logs the errors and displays a fallback UI instead of crashing the entire app.
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="p-6 max-w-md mx-auto my-8 bg-sabone-dark-olive/40 rounded-lg border border-sabone-gold">
          <h2 className="text-sabone-gold text-xl font-medium mb-4">Something went wrong</h2>
          <p className="text-sabone-beige mb-4">
            We've encountered an error rendering this section of the page. 
            Please try refreshing the page or contact support if the issue persists.
          </p>
          <details className="bg-sabone-dark-olive/60 p-3 rounded">
            <summary className="text-sabone-gold-accent cursor-pointer">Error details</summary>
            <p className="mt-2 text-sabone-beige font-mono text-sm overflow-auto">
              {this.state.error?.toString()}
            </p>
          </details>
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-4 px-4 py-2 bg-sabone-gold-accent text-sabone-dark-olive rounded hover:bg-sabone-gold transition-colors"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 