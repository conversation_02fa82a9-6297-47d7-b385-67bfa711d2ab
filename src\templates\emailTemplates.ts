import {
  OrderEmailData,
  WelcomeEmailData,
  PasswordResetEmailData,
  AccountUpdateEmailData,
  LowStockAlertData,
  NewOrderAdminData,
  SalesSummaryData,
  NewsletterData,
  PromotionalEmailData,
  ReceiptEmailData
} from '@/types/email';
import { formatDate } from '@/utils/formatDate';
import { formatCurrency as utilFormatCurrency } from '@/utils/formatCurrency'; // Renamed to avoid conflict

// Common email layout
const createEmailLayout = (content: string): string => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Sabone Store</title>
      <style>
        /* Base styles */
        body {
          font-family: 'Montserrat', Arial, sans-serif;
          line-height: 1.6;
          color: #E8D9B5;
          background-color: #1C1C1C;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #1C1C1C;
          background-image: linear-gradient(120deg, rgba(28, 28, 28, 0.97), rgba(42, 42, 31, 0.97), rgba(28, 28, 28, 0.97));
          border: 1px solid rgba(198, 168, 112, 0.3);
        }
        .header {
          text-align: center;
          padding: 20px 0;
          border-bottom: 1px solid rgba(198, 168, 112, 0.3);
        }
        .logo {
          max-width: 150px;
          height: auto;
        }
        .content {
          padding: 30px 20px;
        }
        .footer {
          text-align: center;
          padding: 20px 0;
          font-size: 12px;
          color: rgba(232, 217, 181, 0.7);
          border-top: 1px solid rgba(198, 168, 112, 0.3);
        }
        h1, h2, h3 {
          font-family: 'Playfair Display', Georgia, serif;
          color: #C6A870;
          margin-top: 0;
        }
        p {
          margin-bottom: 16px;
        }
        a {
          color: #C6A870;
          text-decoration: none;
        }
        a:hover {
          text-decoration: underline;
        }
        .button {
          display: inline-block;
          background-color: #C6A870;
          color: #1C1C1C !important;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 4px;
          font-weight: 500;
          margin: 16px 0;
        }
        .button:hover {
          background-color: rgba(198, 168, 112, 0.8);
          text-decoration: none;
        }
        .divider {
          height: 1px;
          background-color: rgba(198, 168, 112, 0.3);
          margin: 24px 0;
        }
        .product-item {
          display: flex;
          margin-bottom: 16px;
          border-bottom: 1px solid rgba(198, 168, 112, 0.2);
          padding-bottom: 16px;
        }
        .product-image {
          width: 80px;
          height: 80px;
          object-fit: cover;
          margin-right: 16px;
        }
        .product-details {
          flex: 1;
        }
        .product-name {
          font-weight: 500;
          color: #E8D9B5;
          margin: 0 0 4px 0;
        }
        .product-price {
          color: #C6A870;
          margin: 0 0 4px 0;
        }
        .product-quantity {
          color: rgba(232, 217, 181, 0.7);
          font-size: 14px;
          margin: 0;
        }
        .order-summary {
          background-color: rgba(42, 42, 31, 0.5);
          padding: 16px;
          border-radius: 4px;
          margin-top: 24px;
        }
        .summary-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        .summary-row.total {
          font-weight: 500;
          color: #C6A870;
          border-top: 1px solid rgba(198, 168, 112, 0.3);
          padding-top: 8px;
          margin-top: 8px;
        }
        .address-block {
          background-color: rgba(42, 42, 31, 0.5);
          padding: 16px;
          border-radius: 4px;
          margin-top: 16px;
        }
        .social-links {
          margin-top: 24px;
          text-align: center;
        }
        .social-link {
          display: inline-block;
          margin: 0 8px;
          color: #C6A870;
          font-size: 20px;
        }
        .unsubscribe {
          margin-top: 16px;
          font-size: 12px;
          color: rgba(232, 217, 181, 0.5);
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <img src="https://sabone.store/logo.png" alt="Sabone Store" class="logo">
        </div>
        <div class="content">
          ${content}
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} Sabone Store. All rights reserved.</p>
          <p>Crafted in Berlin. Rooted in Aleppo.</p>
          <div class="social-links">
            <a href="https://instagram.com/sabone.store" class="social-link">Instagram</a>
            <a href="https://facebook.com/sabone.store" class="social-link">Facebook</a>
            <a href="https://twitter.com/sabone.store" class="social-link">Twitter</a>
          </div>
          <p class="unsubscribe">
            If you no longer wish to receive these emails, you can <a href="https://sabone.store/account/preferences">unsubscribe</a>.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};

// Use the imported formatCurrency utility
const formatCurrency = utilFormatCurrency;

// Generate order confirmation email
export const generateOrderConfirmationEmail = (data: OrderEmailData) => {
  const { order, customerName, estimatedDelivery } = data;

  const productsList = order.items.map(item => `
    <div class="product-item">
      <img src="${item.image}" alt="${item.name}" class="product-image">
      <div class="product-details">
        <p class="product-name">${item.name}</p>
        <p class="product-price">${formatCurrency(item.price)}</p>
        <p class="product-quantity">Quantity: ${item.quantity}</p>
      </div>
    </div>
  `).join('');

  const content = `
    <h1>Thank You for Your Order!</h1>
    <p>Dear ${customerName},</p>
    <p>We're delighted to confirm that we've received your order. Your sacred ritual essentials are being prepared with care.</p>

    <p><strong>Order Number:</strong> ${order.id}</p>
    <p><strong>Order Date:</strong> ${formatDate(order.createdAt)}</p>
    <p><strong>Estimated Delivery:</strong> ${estimatedDelivery || '5-7 business days'}</p>

    <h2>Order Details</h2>
    ${productsList}

    <div class="order-summary">
      <div class="summary-row">
        <span>Subtotal:</span>
        <span>${formatCurrency(order.subtotal)}</span>
      </div>
      <div class="summary-row">
        <span>Shipping:</span>
        <span>${formatCurrency(order.shipping)}</span>
      </div>
      <div class="summary-row">
        <span>Tax:</span>
        <span>${formatCurrency(order.tax)}</span>
      </div>
      <div class="summary-row total">
        <span>Total:</span>
        <span>${formatCurrency(order.total)}</span>
      </div>
    </div>

    <h2>Shipping Address</h2>
    <div class="address-block">
      <p>${order.shippingAddress.fullName}<br>
      ${order.shippingAddress.addressLine1}<br>
      ${order.shippingAddress.addressLine2 ? order.shippingAddress.addressLine2 + '<br>' : ''}
      ${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.zipCode}<br>
      ${order.shippingAddress.country}</p>
    </div>

    <div class="divider"></div>

    <p>You can track your order status in your <a href="https://sabone.store/account/orders">account dashboard</a>.</p>

    <p>Thank you for choosing Sabone for your ritual cleansing journey.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: `Order Confirmation - Sabone Order #${order.id}`,
    html: createEmailLayout(content)
  };
};

// Generate order shipped email
export const generateOrderShippedEmail = (data: OrderEmailData) => {
  const { order, customerName, trackingNumber } = data;

  const content = `
    <h1>Your Order Has Been Shipped!</h1>
    <p>Dear ${customerName},</p>
    <p>Great news! Your Sabone order is on its way to you. Your sacred ritual essentials have been carefully packaged and are now in transit.</p>

    <p><strong>Order Number:</strong> ${order.id}</p>
    <p><strong>Shipping Date:</strong> ${formatDate(new Date().toISOString())}</p>
    ${trackingNumber ? `<p><strong>Tracking Number:</strong> ${trackingNumber}</p>` : ''}

    <a href="${trackingNumber ? `https://sabone.store/track/${trackingNumber}` : 'https://sabone.store/account/orders'}" class="button">Track Your Order</a>

    <div class="divider"></div>

    <p>We hope your Sabone products bring tranquility and renewal to your daily rituals.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: `Your Sabone Order Has Shipped - Order #${order.id}`,
    html: createEmailLayout(content)
  };
};

// Generate order delivered email
export const generateOrderDeliveredEmail = (data: OrderEmailData) => {
  const { order, customerName } = data;

  const content = `
    <h1>Your Order Has Been Delivered!</h1>
    <p>Dear ${customerName},</p>
    <p>We're pleased to inform you that your Sabone order has been delivered. Your sacred ritual essentials are now ready to transform your cleansing experience.</p>

    <p><strong>Order Number:</strong> ${order.id}</p>
    <p><strong>Delivery Date:</strong> ${formatDate(new Date().toISOString())}</p>

    <div class="divider"></div>

    <h2>How to Use Your Products</h2>
    <p>For optimal results, we recommend using our products as part of a mindful ritual:</p>
    <ol>
      <li>Begin with a moment of intention-setting</li>
      <li>Apply products to damp skin or hair</li>
      <li>Massage gently in circular motions</li>
      <li>Rinse thoroughly with warm water</li>
    </ol>

    <p>We'd love to hear about your experience with our products. Please consider leaving a review or sharing your ritual on social media using #SaboneRitual.</p>

    <a href="https://sabone.store/account/orders" class="button">View Order Details</a>

    <p>Thank you for choosing Sabone for your ritual cleansing journey.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: `Your Sabone Order Has Been Delivered - Order #${order.id}`,
    html: createEmailLayout(content)
  };
};

// Generate order cancelled email
export const generateOrderCancelledEmail = (data: OrderEmailData) => {
  const { order, customerName, cancellationReason } = data;

  const content = `
    <h1>Your Order Has Been Cancelled</h1>
    <p>Dear ${customerName},</p>
    <p>We're writing to confirm that your Sabone order has been cancelled as requested.</p>

    <p><strong>Order Number:</strong> ${order.id}</p>
    <p><strong>Cancellation Date:</strong> ${formatDate(new Date().toISOString())}</p>
    ${cancellationReason ? `<p><strong>Reason:</strong> ${cancellationReason}</p>` : ''}

    <div class="divider"></div>

    <p>If you've been charged for this order, a refund will be processed within 3-5 business days, depending on your payment method and financial institution.</p>

    <p>We hope to welcome you back to Sabone soon. If you have any questions or need assistance, please don't hesitate to contact our customer service team.</p>

    <a href="https://sabone.store" class="button">Continue Shopping</a>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: `Your Sabone Order Has Been Cancelled - Order #${order.id}`,
    html: createEmailLayout(content)
  };
};

// Generate welcome email
export const generateWelcomeEmail = (data: WelcomeEmailData) => {
  const { userName, verificationLink } = data;

  const content = `
    <h1>Welcome to the Sabone Family!</h1>
    <p>Dear ${userName},</p>
    <p>We're delighted to welcome you to Sabone, where ancient traditions meet modern luxury in our handcrafted soaps and shampoos.</p>

    <p>Your journey to ritual cleansing begins now. Here's what you can expect as a valued member of our community:</p>

    <ul>
      <li>Early access to new collections</li>
      <li>Exclusive offers and promotions</li>
      <li>Ritual guides and wisdom from ancient traditions</li>
      <li>A personalized shopping experience</li>
    </ul>

    ${verificationLink ? `
    <p>To complete your registration and start your journey, please verify your email address:</p>
    <a href="${verificationLink}" class="button">Verify Email Address</a>
    ` : ''}

    <div class="divider"></div>

    <h2>Discover Our Bestsellers</h2>
    <p>Explore our most beloved products and find the perfect addition to your cleansing ritual:</p>

    <a href="https://sabone.store/products" class="button">Shop Now</a>

    <p>Thank you for choosing Sabone for your ritual cleansing journey.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: 'Welcome to Sabone - Begin Your Ritual Cleansing Journey',
    html: createEmailLayout(content)
  };
};

// Generate password reset email
export const generatePasswordResetEmail = (data: PasswordResetEmailData) => {
  const { userName, resetLink } = data;

  const content = `
    <h1>Reset Your Password</h1>
    <p>Dear ${userName},</p>
    <p>We received a request to reset your password for your Sabone account. If you didn't make this request, you can safely ignore this email.</p>

    <p>To reset your password, please click the button below:</p>

    <a href="${resetLink}" class="button">Reset Password</a>

    <p>This link will expire in 24 hours for security reasons.</p>

    <p>If you're having trouble clicking the button, copy and paste the following URL into your web browser:</p>
    <p>${resetLink}</p>

    <div class="divider"></div>

    <p>If you need further assistance, please contact our customer service team.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: 'Reset Your Sabone Account Password',
    html: createEmailLayout(content)
  };
};

// Generate account update email
export const generateAccountUpdateEmail = (data: AccountUpdateEmailData) => {
  const { userName, updateType, updateDetails } = data;

  let updateTypeText = '';
  switch (updateType) {
    case 'profile':
      updateTypeText = 'profile information';
      break;
    case 'password':
      updateTypeText = 'password';
      break;
    case 'address':
      updateTypeText = 'address';
      break;
    case 'payment_method':
      updateTypeText = 'payment method';
      break;
  }

  const content = `
    <h1>Your Account Has Been Updated</h1>
    <p>Dear ${userName},</p>
    <p>We're writing to confirm that your ${updateTypeText} has been successfully updated.</p>

    ${updateDetails ? `<p><strong>Details:</strong> ${updateDetails}</p>` : ''}

    <p>If you did not make this change, please contact our customer service team immediately.</p>

    <a href="https://sabone.store/account" class="button">View Your Account</a>

    <div class="divider"></div>

    <p>Thank you for choosing Sabone for your ritual cleansing journey.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: `Your Sabone Account ${updateTypeText.charAt(0).toUpperCase() + updateTypeText.slice(1)} Has Been Updated`,
    html: createEmailLayout(content)
  };
};

// Generate low stock alert email
export const generateLowStockAlertEmail = (data: LowStockAlertData) => {
  const { items, productNames } = data;

  const lowStockList = items.map(item => `
    <div class="product-item">
      <div class="product-details">
        <p class="product-name">${productNames.find(name => name.includes(item.productId)) || item.productId}</p>
        <p class="product-quantity">Current Stock: ${item.stockQuantity}</p>
        <p class="product-price">Low Stock Threshold: ${item.lowStockThreshold}</p>
      </div>
    </div>
  `).join('');

  const content = `
    <h1>Low Stock Alert</h1>
    <p>Dear Admin,</p>
    <p>This is an automated notification to inform you that the following products are running low on stock and may need to be replenished soon:</p>

    ${lowStockList}

    <a href="https://sabone.store/admin/inventory" class="button">Manage Inventory</a>

    <div class="divider"></div>

    <p>Please take appropriate action to ensure continuous availability of these products.</p>

    <p>Sabone Inventory System</p>
  `;

  return {
    subject: `Low Stock Alert - ${items.length} Products Need Attention`,
    html: createEmailLayout(content)
  };
};

// Generate new order admin email
export const generateNewOrderAdminEmail = (data: NewOrderAdminData) => {
  const { order, customerName, customerEmail } = data;

  const productsList = order.items.map(item => `
    <div class="product-item">
      <div class="product-details">
        <p class="product-name">${item.name}</p>
        <p class="product-price">${formatCurrency(item.price)}</p>
        <p class="product-quantity">Quantity: ${item.quantity}</p>
      </div>
    </div>
  `).join('');

  const content = `
    <h1>New Order Received</h1>
    <p>Dear Admin,</p>
    <p>A new order has been placed on Sabone Store.</p>

    <p><strong>Order Number:</strong> ${order.id}</p>
    <p><strong>Order Date:</strong> ${formatDate(order.createdAt)}</p>
    <p><strong>Customer:</strong> ${customerName} (${customerEmail})</p>
    <p><strong>Payment Method:</strong> ${order.paymentMethod.replace('_', ' ').toUpperCase()}</p>
    <p><strong>Payment Status:</strong> ${order.paymentStatus.toUpperCase()}</p>

    <h2>Order Details</h2>
    ${productsList}

    <div class="order-summary">
      <div class="summary-row">
        <span>Subtotal:</span>
        <span>${formatCurrency(order.subtotal)}</span>
      </div>
      <div class="summary-row">
        <span>Shipping:</span>
        <span>${formatCurrency(order.shipping)}</span>
      </div>
      <div class="summary-row">
        <span>Tax:</span>
        <span>${formatCurrency(order.tax)}</span>
      </div>
      <div class="summary-row total">
        <span>Total:</span>
        <span>${formatCurrency(order.total)}</span>
      </div>
    </div>

    <a href="https://sabone.store/admin/orders/${order.id}" class="button">View Order Details</a>

    <div class="divider"></div>

    <p>Please process this order at your earliest convenience.</p>

    <p>Sabone Order System</p>
  `;

  return {
    subject: `New Order #${order.id} - ${formatCurrency(order.total)}`,
    html: createEmailLayout(content)
  };
};

// Generate sales summary email
export const generateSalesSummaryEmail = (data: SalesSummaryData) => {
  const { period, totalSales, orderCount, topProducts, date } = data;

  let periodText = '';
  switch (period) {
    case 'daily':
      periodText = 'Daily';
      break;
    case 'weekly':
      periodText = 'Weekly';
      break;
    case 'monthly':
      periodText = 'Monthly';
      break;
  }

  const topProductsList = topProducts.map((product, index) => `
    <div class="product-item">
      <div class="product-details">
        <p class="product-name">${index + 1}. ${product.name}</p>
        <p class="product-price">Revenue: ${formatCurrency(product.revenue)}</p>
        <p class="product-quantity">Units Sold: ${product.quantity}</p>
      </div>
    </div>
  `).join('');

  const content = `
    <h1>${periodText} Sales Summary</h1>
    <p>Dear Admin,</p>
    <p>Here is your ${periodText.toLowerCase()} sales summary for ${date}:</p>

    <div class="order-summary">
      <div class="summary-row">
        <span>Total Sales:</span>
        <span>${formatCurrency(totalSales)}</span>
      </div>
      <div class="summary-row">
        <span>Orders Processed:</span>
        <span>${orderCount}</span>
      </div>
      <div class="summary-row">
        <span>Average Order Value:</span>
        <span>${formatCurrency(orderCount > 0 ? totalSales / orderCount : 0)}</span>
      </div>
    </div>

    <h2>Top Performing Products</h2>
    ${topProductsList}

    <a href="https://sabone.store/admin/reports" class="button">View Detailed Reports</a>

    <div class="divider"></div>

    <p>For more detailed analytics and insights, please visit your admin dashboard.</p>

    <p>Sabone Analytics System</p>
  `;

  return {
    subject: `${periodText} Sales Summary - ${date}`,
    html: createEmailLayout(content)
  };
};

// Generate newsletter email
export const generateNewsletterEmail = (data: NewsletterData) => {
  const { recipientName, subject, content, featuredProducts } = data;

  let featuredProductsHtml = '';
  if (featuredProducts && featuredProducts.length > 0) {
    featuredProductsHtml = `
      <h2>Featured Products</h2>
      ${featuredProducts.map(product => `
        <div class="product-item">
          <img src="${product.image}" alt="${product.name}" class="product-image">
          <div class="product-details">
            <p class="product-name">${product.name}</p>
            <p class="product-price">${formatCurrency(product.price)}</p>
            <a href="${product.url}" class="button">Shop Now</a>
          </div>
        </div>
      `).join('')}
    `;
  }

  const emailContent = `
    <h1>${subject}</h1>
    ${recipientName ? `<p>Dear ${recipientName},</p>` : ''}

    ${content}

    ${featuredProductsHtml}

    <a href="https://sabone.store/products" class="button">Explore Our Collection</a>

    <div class="divider"></div>

    <p>Thank you for being part of our sacred circle of ritual enthusiasts.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject,
    html: createEmailLayout(emailContent)
  };
};

// Generate promotional email
export const generatePromotionalEmail = (data: PromotionalEmailData) => {
  const { recipientName, subject, promotionTitle, promotionDescription, discountCode, expiryDate, featuredProducts } = data;

  let featuredProductsHtml = '';
  if (featuredProducts && featuredProducts.length > 0) {
    featuredProductsHtml = `
      <h2>Featured Products</h2>
      ${featuredProducts.map(product => `
        <div class="product-item">
          <img src="${product.image}" alt="${product.name}" class="product-image">
          <div class="product-details">
            <p class="product-name">${product.name}</p>
            <p class="product-price">
              ${product.discountedPrice
                ? `<span style="text-decoration: line-through;">${formatCurrency(product.price)}</span> ${formatCurrency(product.discountedPrice)}`
                : formatCurrency(product.price)
              }
            </p>
            <a href="${product.url}" class="button">Shop Now</a>
          </div>
        </div>
      `).join('')}
    `;
  }

  const emailContent = `
    <h1>${promotionTitle}</h1>
    ${recipientName ? `<p>Dear ${recipientName},</p>` : ''}

    <p>${promotionDescription}</p>

    ${discountCode ? `
    <div style="text-align: center; margin: 30px 0;">
      <div style="background-color: rgba(42, 42, 31, 0.8); padding: 20px; border-radius: 4px; display: inline-block;">
        <p style="margin: 0 0 10px 0; font-size: 14px;">Use this code at checkout:</p>
        <p style="font-family: 'Courier New', monospace; font-size: 24px; letter-spacing: 2px; color: #C6A870; margin: 0;">${discountCode}</p>
        ${expiryDate ? `<p style="margin: 10px 0 0 0; font-size: 12px;">Valid until ${expiryDate}</p>` : ''}
      </div>
    </div>
    ` : ''}

    ${featuredProductsHtml}

    <a href="https://sabone.store/products" class="button">Shop Now</a>

    <div class="divider"></div>

    <p>Thank you for being part of our sacred circle of ritual enthusiasts.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject,
    html: createEmailLayout(emailContent)
  };
};

// Generate order receipt email
export const generateReceiptEmail = (data: ReceiptEmailData) => {
  const { order, customerName, receiptUrl } = data;

  const productsList = order.items.map(item => `
    <tr style="border-bottom: 1px solid rgba(198, 168, 112, 0.2);">
      <td style="padding: 12px 8px; text-align: left;">${item.name}</td>
      <td style="padding: 12px 8px; text-align: center;">${item.quantity}</td>
      <td style="padding: 12px 8px; text-align: right;">${formatCurrency(item.price)}</td>
      <td style="padding: 12px 8px; text-align: right;">${formatCurrency(item.price * item.quantity)}</td>
    </tr>
  `).join('');

  const content = `
    <h1>Your Order Receipt</h1>
    <p>Dear ${customerName},</p>
    <p>Thank you for your order. Please find your receipt attached below.</p>

    <p><strong>Order Number:</strong> ${order.id}</p>
    <p><strong>Order Date:</strong> ${formatDate(order.createdAt)}</p>

    <div style="background-color: rgba(42, 42, 31, 0.5); padding: 20px; border-radius: 4px; margin: 20px 0;">
      <h2 style="color: #C6A870; margin-top: 0;">Order Summary</h2>

      <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <thead>
          <tr style="border-bottom: 1px solid rgba(198, 168, 112, 0.3);">
            <th style="padding: 12px 8px; text-align: left; color: #C6A870;">Product</th>
            <th style="padding: 12px 8px; text-align: center; color: #C6A870;">Quantity</th>
            <th style="padding: 12px 8px; text-align: right; color: #C6A870;">Price</th>
            <th style="padding: 12px 8px; text-align: right; color: #C6A870;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${productsList}
        </tbody>
      </table>

      <div style="margin-top: 20px; border-top: 1px solid rgba(198, 168, 112, 0.3); padding-top: 20px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span style="color: rgba(232, 217, 181, 0.7);">Subtotal:</span>
          <span style="color: #E8D9B5;">${formatCurrency(order.subtotal)}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span style="color: rgba(232, 217, 181, 0.7);">Shipping:</span>
          <span style="color: #E8D9B5;">${formatCurrency(order.shipping)}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span style="color: rgba(232, 217, 181, 0.7);">Tax:</span>
          <span style="color: #E8D9B5;">${formatCurrency(order.tax)}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(198, 168, 112, 0.3);">
          <span style="color: #C6A870; font-weight: 500;">Total:</span>
          <span style="color: #C6A870; font-weight: 500;">${formatCurrency(order.total)}</span>
        </div>
      </div>
    </div>

    <div style="background-color: rgba(42, 42, 31, 0.5); padding: 20px; border-radius: 4px; margin: 20px 0;">
      <h2 style="color: #C6A870; margin-top: 0;">Shipping Address</h2>
      <p style="margin-bottom: 0;">
        ${order.shippingAddress.fullName}<br>
        ${order.shippingAddress.addressLine1}<br>
        ${order.shippingAddress.addressLine2 ? order.shippingAddress.addressLine2 + '<br>' : ''}
        ${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.zipCode}<br>
        ${order.shippingAddress.country}
      </p>
    </div>

    <div style="background-color: rgba(42, 42, 31, 0.5); padding: 20px; border-radius: 4px; margin: 20px 0;">
      <h2 style="color: #C6A870; margin-top: 0;">Payment Information</h2>
      <p><strong>Payment Method:</strong> ${
        order.paymentMethod === 'credit_card' ? 'Credit Card' :
        order.paymentMethod === 'paypal' ? 'PayPal' :
        'Cash on Delivery'
      }</p>
      <p><strong>Payment Status:</strong> ${
        order.paymentStatus === 'paid' ? 'Paid' :
        order.paymentStatus === 'pending' ? 'Pending' :
        order.paymentStatus === 'failed' ? 'Failed' :
        'Refunded'
      }</p>
    </div>

    ${receiptUrl ? `
    <p>You can download a PDF copy of your receipt by clicking the button below:</p>
    <a href="${receiptUrl}" class="button" style="display: inline-block; background-color: #C6A870; color: #1C1C1C !important; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: 500; margin: 16px 0;">Download Receipt</a>
    ` : ''}

    <div class="divider"></div>

    <p>You can view your order details and track your shipment in your <a href="https://sabone.store/account/orders" style="color: #C6A870; text-decoration: none;">account dashboard</a>.</p>

    <p>Thank you for choosing Sabone for your ritual cleansing journey.</p>

    <p>With gratitude,<br>The Sabone Team</p>
  `;

  return {
    subject: `Receipt for Your Sabone Order #${order.id}`,
    html: createEmailLayout(content)
  };
};
