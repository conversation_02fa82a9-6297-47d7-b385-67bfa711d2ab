import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { setDefaultAddress } from "@/services/userService";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Plus, Edit, Trash2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Dialog<PERSON>lose,
} from "@/components/ui/dialog";

// Form validation schema
const addressFormSchema = z.object({
  fullName: z.string().min(2, { message: "Name must be at least 2 characters." }),
  addressLine1: z.string().min(5, { message: "Address must be at least 5 characters." }),
  addressLine2: z.string().optional(),
  city: z.string().min(2, { message: "City is required." }),
  state: z.string().min(2, { message: "State is required." }),
  zipCode: z.string().min(5, { message: "Zip code is required." }),
  country: z.string().min(2, { message: "Country is required." }),
  phone: z.string().optional(),
  isDefault: z.boolean().default(false),
});

type AddressFormValues = z.infer<typeof addressFormSchema>;

// Mock address data - in a real application, this would come from an API
const mockAddresses = [
  {
    id: "addr-1",
    fullName: "John Doe",
    addressLine1: "123 Main St",
    addressLine2: "Apt 4B",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "United States",
    phone: "************",
    isDefault: true,
  },
];

const Addresses = () => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState(mockAddresses);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [addressTypes, setAddressTypes] = useState({
    shipping: addresses.find(addr => addr.isDefault)?.id || null,
    billing: null
  });

  // Initialize form
  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressFormSchema),
    defaultValues: {
      fullName: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
      phone: "",
      isDefault: false,
    },
  });

  // Load addresses from localStorage on component mount
  useEffect(() => {
    const savedAddresses = localStorage.getItem('sabone-addresses');
    if (savedAddresses && user?.sub) {
      try {
        const parsedAddresses = JSON.parse(savedAddresses);
        if (Array.isArray(parsedAddresses)) {
          setAddresses(parsedAddresses);

          // Set default addresses
          const defaultShipping = parsedAddresses.find(addr => addr.isDefault);
          if (defaultShipping) {
            setAddressTypes(prev => ({
              ...prev,
              shipping: defaultShipping.id
            }));
          }
        }
      } catch (error) {
        console.error('Error parsing saved addresses:', error);
      }
    }
  }, [user?.sub]);

  // Save addresses to localStorage whenever they change
  useEffect(() => {
    if (user?.sub && addresses.length > 0) {
      localStorage.setItem('sabone-addresses', JSON.stringify(addresses));
    }
  }, [addresses, user?.sub]);

  const onSubmit = (data: AddressFormValues) => {
    // Handle default address logic
    let updatedAddresses = [...addresses];

    if (data.isDefault) {
      // If this address is being set as default, remove default from other addresses
      updatedAddresses = updatedAddresses.map(addr => ({
        ...addr,
        isDefault: false
      }));
    }

    if (editingAddress) {
      // Update existing address
      updatedAddresses = updatedAddresses.map(addr =>
        addr.id === editingAddress.id ? { ...data, id: addr.id } : addr
      );
      toast.success("Address updated successfully!");
    } else {
      // Add new address
      const newAddress = {
        ...data,
        id: `addr-${Date.now()}`, // Use timestamp for unique ID
      };
      updatedAddresses = [...updatedAddresses, newAddress];
      toast.success("Address added successfully!");
    }

    // Update addresses state
    setAddresses(updatedAddresses);

    // If this is a default address, update the default shipping address
    if (data.isDefault && user?.sub) {
      const defaultAddrId = editingAddress ? editingAddress.id : `addr-${Date.now()}`;
      setAddressTypes(prev => ({
        ...prev,
        shipping: defaultAddrId
      }));

      // In a real app, this would update the user's profile
      setDefaultAddress(user.sub, defaultAddrId, 'shipping');
    }

    // Reset form and close dialog
    form.reset();
    setIsAddingAddress(false);
    setEditingAddress(null);
  };

  const handleEditAddress = (address) => {
    setEditingAddress(address);
    form.reset({
      fullName: address.fullName,
      addressLine1: address.addressLine1,
      addressLine2: address.addressLine2 || "",
      city: address.city,
      state: address.state,
      zipCode: address.zipCode,
      country: address.country,
      phone: address.phone || "",
      isDefault: address.isDefault,
    });
    setIsAddingAddress(true);
  };

  const handleDeleteAddress = (addressId) => {
    // In a real application, this would call an API to delete the address
    setAddresses(addresses.filter(addr => addr.id !== addressId));

    // If this was a default address, update the addressTypes state
    if (addressTypes.shipping === addressId) {
      setAddressTypes(prev => ({
        ...prev,
        shipping: null
      }));
    }

    if (addressTypes.billing === addressId) {
      setAddressTypes(prev => ({
        ...prev,
        billing: null
      }));
    }

    toast.success("Address removed successfully!");
  };

  const handleSetAsDefault = (addressId, type: 'shipping' | 'billing') => {
    if (!user?.sub) return;

    // Update the addressTypes state
    setAddressTypes(prev => ({
      ...prev,
      [type]: addressId
    }));

    // Update the addresses state
    if (type === 'shipping') {
      setAddresses(addresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      })));
    }

    // In a real app, this would update the user's profile
    setDefaultAddress(user.sub, addressId, type);

    toast.success(`Address set as default ${type} address`);
  };

  const handleDialogClose = () => {
    form.reset();
    setEditingAddress(null);
    setIsAddingAddress(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Saved Addresses</h2>
          <p className="text-sabone-cream/70 mt-1">Manage your shipping and billing addresses</p>
        </div>
        <Dialog open={isAddingAddress} onOpenChange={setIsAddingAddress}>
          <DialogTrigger asChild>
            <Button
              className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
            <DialogHeader>
              <DialogTitle className="text-sabone-gold">
                {editingAddress ? "Edit Address" : "Add New Address"}
              </DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Full Name</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="addressLine1"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Address Line 1</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="addressLine2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Address Line 2 (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sabone-cream">City</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sabone-cream">State/Province</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="zipCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sabone-cream">Zip/Postal Code</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sabone-cream">Country</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Phone Number (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isDefault"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4 bg-sabone-charcoal/30">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sabone-cream">
                          Set as default address
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <DialogFooter className="mt-6">
                  <DialogClose asChild>
                    <Button
                      type="button"
                      variant="outline"
                      className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                      onClick={handleDialogClose}
                    >
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    type="submit"
                    className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                  >
                    {editingAddress ? "Update Address" : "Save Address"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Separator className="bg-sabone-gold/20" />

      {addresses.length > 0 ? (
        <div className="grid md:grid-cols-2 gap-6">
          {addresses.map((address) => (
            <Card key={address.id} className="bg-sabone-charcoal/30 border-sabone-gold/30">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-sabone-gold">{address.fullName}</CardTitle>
                    {address.isDefault && (
                      <CardDescription className="text-sabone-gold/70">Default Address</CardDescription>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-sabone-gold hover:bg-sabone-gold/10 h-8 w-8 p-0"
                      onClick={() => handleEditAddress(address)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-400 hover:bg-red-500/10 hover:text-red-500 h-8 w-8 p-0"
                      onClick={() => handleDeleteAddress(address.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="text-sabone-cream">
                <div className="space-y-1">
                  <p>{address.addressLine1}</p>
                  {address.addressLine2 && <p>{address.addressLine2}</p>}
                  <p>{address.city}, {address.state} {address.zipCode}</p>
                  <p>{address.country}</p>
                  {address.phone && <p className="mt-2">Phone: {address.phone}</p>}
                </div>
              </CardContent>
              <CardFooter>
                {!address.isDefault && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10 mt-2"
                    onClick={() => handleSetAsDefault(address.id, 'shipping')}
                  >
                    Set as Default
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-sabone-cream/70 mb-4">You don't have any saved addresses yet.</p>
          <Button
            className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
            onClick={() => setIsAddingAddress(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Your First Address
          </Button>
        </div>
      )}
    </div>
  );
};

export default Addresses;
