import DOMPurify from 'dompurify';

/**
 * Input Sanitization Utilities
 *
 * This module provides comprehensive input sanitization and validation
 * utilities to protect against XSS, injection attacks, and malicious input.
 */

// Regular expressions for various input validation
const PATTERNS = {
  HTML_TAGS: /<[^>]*>/g,
  SCRIPT_TAGS: /<script[^>]*>.*?<\/script>/gi,
  SQL_INJECTION: /('|(\\)|(\|\|)|(\/\*)|(\*\/)|(\|\|)|(\||)|(\;)|(--|#|\/\*|\*\/|@@|@))/gi,
  XSS_PATTERNS: /(javascript:|vbscript:|onload|onerror|onclick|onmouseover|onfocus|onblur|onchange|onsubmit|alert\(|<script|<\/script|<svg|<img)/gi,
  EMAIL_VALIDATION: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  PHONE_VALIDATION: /^\+?[0-9\s\-\(\)\.]{10,15}$/,
  ALPHANUMERIC: /^[a-zA-Z0-9\s]*$/,
  FILENAME_SAFE: /^[a-zA-Z0-9\-_\.\s]+$/,
  URL_SAFE: /^[a-zA-Z0-9\-_\.\s\/\?\&\=\:\+\%\#]+$/,
};

// Configuration for different sanitization levels
export const SANITIZATION_LEVELS = {
  STRICT: 'strict',
  MODERATE: 'moderate',
  BASIC: 'basic',
} as const;

export type SanitizationLevel = keyof typeof SANITIZATION_LEVELS;

interface SanitizationOptions {
  level: SanitizationLevel;
  allowHtml: boolean;
  maxLength: number;
  trimWhitespace: boolean;
  removeControlChars: boolean;
  normalizeUnicode: boolean;
}

/**
 * Default sanitization options
 */
const DEFAULT_OPTIONS: SanitizationOptions = {
  level: 'MODERATE',
  allowHtml: false,
  maxLength: 10000,
  trimWhitespace: true,
  removeControlChars: true,
  normalizeUnicode: true,
};

/**
 * Sanitizes a string input to prevent XSS and injection attacks
 */
export const sanitizeString = (
  input: string | null | undefined,
  options: Partial<SanitizationOptions> = {}
): string => {
  if (!input || typeof input !== 'string') {
    return '';
  }

  const config = { ...DEFAULT_OPTIONS, ...options };
  let sanitized = input;

  // Trim whitespace if enabled
  if (config.trimWhitespace) {
    sanitized = sanitized.trim();
  }

  // Apply length limit
  if (sanitized.length > config.maxLength) {
    sanitized = sanitized.substring(0, config.maxLength);
  }

  // Remove control characters
  if (config.removeControlChars) {
    sanitized = sanitized.replace(/[\x00-\x1F\x7F]/g, '');
  }

  // Normalize unicode
  if (config.normalizeUnicode) {
    sanitized = sanitized.normalize('NFC');
  }

  // Apply sanitization based on level
  switch (config.level) {
    case 'STRICT':
      sanitized = sanitizeStrict(sanitized, config.allowHtml);
      break;
    case 'MODERATE':
      sanitized = sanitizeModerate(sanitized, config.allowHtml);
      break;
    case 'BASIC':
      sanitized = sanitizeBasic(sanitized, config.allowHtml);
      break;
  }

  return sanitized;
};

/**
 * Strict sanitization - removes all potentially dangerous content
 */
const sanitizeStrict = (input: string, allowHtml: boolean): string => {
  let sanitized = input;

  // Remove all HTML tags unless specifically allowed
  if (!allowHtml) {
    sanitized = sanitized.replace(PATTERNS.HTML_TAGS, '');
  } else {
    // Use DOMPurify for safe HTML sanitization
    sanitized = DOMPurify.sanitize(sanitized, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: [],
    });
  }

  // Remove script tags
  sanitized = sanitized.replace(PATTERNS.SCRIPT_TAGS, '');

  // Remove XSS patterns
  sanitized = sanitized.replace(PATTERNS.XSS_PATTERNS, '');

  // Remove SQL injection patterns
  sanitized = sanitized.replace(PATTERNS.SQL_INJECTION, '');

  // Encode remaining special characters
  sanitized = encodeSpecialCharacters(sanitized);

  return sanitized;
};

/**
 * Moderate sanitization - allows some formatting but removes dangerous content
 */
const sanitizeModerate = (input: string, allowHtml: boolean): string => {
  let sanitized = input;

  if (allowHtml) {
    // Use DOMPurify with more permissive settings
    sanitized = DOMPurify.sanitize(sanitized, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote'],
      ALLOWED_ATTR: ['class'],
    });
  } else {
    sanitized = sanitized.replace(PATTERNS.HTML_TAGS, '');
  }

  // Remove script tags
  sanitized = sanitized.replace(PATTERNS.SCRIPT_TAGS, '');

  // Remove XSS patterns
  sanitized = sanitized.replace(PATTERNS.XSS_PATTERNS, '');

  return sanitized;
};

/**
 * Basic sanitization - minimal cleaning
 */
const sanitizeBasic = (input: string, allowHtml: boolean): string => {
  let sanitized = input;

  if (!allowHtml) {
    sanitized = sanitized.replace(PATTERNS.HTML_TAGS, '');
  }

  // Only remove script tags
  sanitized = sanitized.replace(PATTERNS.SCRIPT_TAGS, '');

  return sanitized;
};

/**
 * Encodes special characters to prevent injection
 */
const encodeSpecialCharacters = (input: string): string => {
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Checks if input contains potential XSS patterns
 */
export const containsXssPatterns = (input: string | null | undefined): boolean => {
  if (!input || typeof input !== 'string') {
    return false;
  }
  // Create new regex instances to avoid global flag issues
  const xssPattern = /(javascript:|vbscript:|onload|onerror|onclick|onmouseover|onfocus|onblur|onchange|onsubmit|alert\(|<script|<\/script|<svg|<img)/i;
  const scriptPattern = /<script[^>]*>.*?<\/script>/i;
  return xssPattern.test(input) || scriptPattern.test(input);
};

/**
 * Checks if input contains potential SQL injection patterns
 */
export const containsSqlInjection = (input: string | null | undefined): boolean => {
  if (!input || typeof input !== 'string') {
    return false;
  }
  // More specific SQL injection patterns to reduce false positives
  const sqlPatterns = /('|(\\)|(\|\|)|(\/\*)|(\*\/)|(\;)|(--|#)|(union\s+select)|(drop\s+table)|(insert\s+into)|(delete\s+from)|(update\s+.*\s+set))/gi;
  return sqlPatterns.test(input);
};

/**
 * Sanitizes an object by applying appropriate sanitization to each field
 */
export const sanitizeObject = <T extends Record<string, unknown>>(
  obj: T,
  fieldConfig: Partial<Record<keyof T, Partial<SanitizationOptions>>> = {}
): T => {
  const sanitized = { ...obj };

  for (const [key, value] of Object.entries(sanitized)) {
    if (typeof value === 'string') {
      const config = fieldConfig[key as keyof T] || {};
      sanitized[key as keyof T] = sanitizeString(value, config) as T[keyof T];
    } else if (Array.isArray(value)) {
      sanitized[key as keyof T] = value.map(item =>
        typeof item === 'string' ? sanitizeString(item, fieldConfig[key as keyof T] || {}) : item
      ) as T[keyof T];
    }
  }

  return sanitized;
};

/**
 * Rate limiting validation for sensitive operations
 */
export const validateRateLimit = (
  identifier: string,
  maxAttempts: number = 5,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean => {
  const key = `rate_limit_${identifier}`;
  const now = Date.now();

  // Get existing attempts from localStorage (in production, use Redis or database)
  const attemptsJson = localStorage.getItem(key);
  let attempts: number[] = [];

  if (attemptsJson) {
    try {
      attempts = JSON.parse(attemptsJson);
    } catch {
      attempts = [];
    }
  }

  // Filter out old attempts
  attempts = attempts.filter(timestamp => now - timestamp < windowMs);

  // Check if limit exceeded
  if (attempts.length >= maxAttempts) {
    return false;
  }

  // Add current attempt
  attempts.push(now);
  localStorage.setItem(key, JSON.stringify(attempts));

  return true;
};

/**
 * Clears rate limit for an identifier
 */
export const clearRateLimit = (identifier: string): void => {
  const key = `rate_limit_${identifier}`;
  localStorage.removeItem(key);
};

export default {
  sanitizeString,
  sanitizeObject,
  containsXssPatterns,
  containsSqlInjection,
  validateRateLimit,
  clearRateLimit,
};