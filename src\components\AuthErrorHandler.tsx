import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, LogIn } from 'lucide-react';

const AuthErrorHandler: React.FC = () => {
  const { authError, clearAuthError, retryLogin, refreshAuth, isAuthenticated } = useAuth();

  if (!authError) return null;

  const handleRetry = async () => {
    if (isAuthenticated) {
      try {
        await refreshAuth();
        clearAuthError();
      } catch (error) {
        console.error('Failed to refresh auth:', error);
      }
    } else {
      retryLogin();
    }
  };

  const getErrorMessage = (error: Error): { title: string; description: string; actionLabel: string } => {
    const message = error.message?.toLowerCase() || '';

    if (message.includes('login_required') || message.includes('login required')) {
      return {
        title: 'Session Expired',
        description: 'Your session has expired. Please log in again to continue.',
        actionLabel: 'Log In'
      };
    }

    if (message.includes('consent_required')) {
      return {
        title: 'Additional Consent Required',
        description: 'Additional permissions are required. Please log in again.',
        actionLabel: 'Log In'
      };
    }

    if (message.includes('network') || message.includes('fetch')) {
      return {
        title: 'Connection Error',
        description: 'Unable to connect to authentication service. Please check your internet connection.',
        actionLabel: isAuthenticated ? 'Retry' : 'Try Again'
      };
    }

    if (message.includes('token')) {
      return {
        title: 'Authentication Token Error',
        description: 'There was an issue with your authentication token. Please try refreshing.',
        actionLabel: isAuthenticated ? 'Refresh' : 'Log In'
      };
    }

    return {
      title: 'Authentication Error',
      description: error.message || 'An unexpected authentication error occurred.',
      actionLabel: isAuthenticated ? 'Refresh' : 'Try Again'
    };
  };

  const { title, description, actionLabel } = getErrorMessage(authError);

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <Alert variant="destructive" className="border-red-500 bg-red-50 dark:bg-red-950">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle className="flex items-center gap-2">
          {title}
        </AlertTitle>
        <AlertDescription className="mt-2">
          {description}
        </AlertDescription>
        <div className="flex gap-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            className="flex items-center gap-2"
          >
            {isAuthenticated ? (
              <RefreshCw className="h-3 w-3" />
            ) : (
              <LogIn className="h-3 w-3" />
            )}
            {actionLabel}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAuthError}
          >
            Dismiss
          </Button>
        </div>
      </Alert>
    </div>
  );
};

export default AuthErrorHandler; 