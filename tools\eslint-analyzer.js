#!/usr/bin/env node

/**
 * ESLint Analyzer MCP Tool
 * Analyzes ESLint errors and provides fixes for Sabone project
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class ESLintAnalyzer {
  constructor() {
    this.projectRoot = process.cwd();
    this.errors = [];
    this.warnings = [];
    this.fixableIssues = [];
  }

  async analyzeProject() {
    console.log('🔍 Analyzing ESLint issues in Sabone project...');
    
    try {
      // Run ESLint to get issues
      const eslintOutput = await this.runESLintCheck();
      this.parseIssues(eslintOutput);
      
      // Generate fixes for common issues
      await this.generateFixes();
      
      // Create report
      await this.createReport();
      
      return {
        totalIssues: this.errors.length + this.warnings.length,
        errors: this.errors.length,
        warnings: this.warnings.length,
        fixableIssues: this.fixableIssues.length,
        report: './eslint-analysis-report.json'
      };
    } catch (error) {
      console.error('❌ ESLint analysis failed:', error.message);
      throw error;
    }
  }

  async runESLintCheck() {
    return new Promise((resolve, reject) => {
      const eslint = spawn('npx', ['eslint', '.', '--format', 'json'], {
        cwd: this.projectRoot,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      eslint.stdout.on('data', (data) => {
        output += data.toString();
      });

      eslint.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      eslint.on('close', (code) => {
        // ESLint returns non-zero exit code when there are issues
        // We want to capture the output regardless
        try {
          const result = JSON.parse(output);
          resolve(result);
        } catch (parseError) {
          // If JSON parsing fails, return raw output
          resolve(output + errorOutput);
        }
      });

      eslint.on('error', (error) => {
        reject(error);
      });
    });
  }

  parseIssues(eslintResults) {
    if (typeof eslintResults === 'string') {
      console.log('ESLint output (raw):', eslintResults);
      return;
    }

    for (const fileResult of eslintResults) {
      for (const message of fileResult.messages) {
        const issue = {
          file: path.relative(this.projectRoot, fileResult.filePath),
          line: message.line,
          column: message.column,
          severity: message.severity === 2 ? 'error' : 'warning',
          ruleId: message.ruleId,
          message: message.message,
          fixable: message.fix !== undefined || this.isAutoFixable(message.ruleId)
        };

        if (issue.severity === 'error') {
          this.errors.push(issue);
        } else {
          this.warnings.push(issue);
        }
      }
    }
  }

  isAutoFixable(ruleId) {
    // Common auto-fixable ESLint rules
    const autoFixableRules = [
      'semi',
      'quotes',
      'comma-dangle',
      'indent',
      'no-trailing-spaces',
      'eol-last',
      'no-multiple-empty-lines',
      'object-curly-spacing',
      'array-bracket-spacing',
      'space-before-blocks',
      'keyword-spacing',
      'space-infix-ops',
      'prefer-const',
      'no-var',
      '@typescript-eslint/no-unused-vars'
    ];
    return autoFixableRules.includes(ruleId);
  }

  async generateFixes() {
    const allIssues = [...this.errors, ...this.warnings];
    
    for (const issue of allIssues) {
      if (issue.fixable) {
        const fix = await this.generateFixForIssue(issue);
        if (fix) {
          this.fixableIssues.push({
            issue,
            fix,
            confidence: fix.confidence || 'medium'
          });
        }
      }
    }
  }

  async generateFixForIssue(issue) {
    const ruleId = issue.ruleId;
    
    switch (ruleId) {
      case 'semi':
        return {
          type: 'add-semicolon',
          description: 'Add missing semicolon',
          command: 'npx eslint --fix',
          confidence: 'high'
        };
      
      case 'quotes':
        return {
          type: 'fix-quotes',
          description: 'Fix quote style',
          command: 'npx eslint --fix',
          confidence: 'high'
        };
      
      case 'prefer-const':
        return {
          type: 'use-const',
          description: 'Use const instead of let',
          command: 'npx eslint --fix',
          confidence: 'high'
        };
      
      case '@typescript-eslint/no-unused-vars':
        return {
          type: 'remove-unused-vars',
          description: 'Remove unused variables',
          command: 'npx eslint --fix',
          confidence: 'medium'
        };
      
      case 'react-hooks/exhaustive-deps':
        return {
          type: 'fix-hook-deps',
          description: 'Fix React hook dependencies',
          command: 'manual-review-required',
          confidence: 'low'
        };
      
      default:
        if (this.isAutoFixable(ruleId)) {
          return {
            type: 'auto-fix',
            description: `Auto-fix ${ruleId}`,
            command: 'npx eslint --fix',
            confidence: 'medium'
          };
        }
        return null;
    }
  }

  async createReport() {
    const report = {
      timestamp: new Date().toISOString(),
      project: 'Sabone E-commerce',
      analysis: {
        totalIssues: this.errors.length + this.warnings.length,
        errors: this.errors.length,
        warnings: this.warnings.length,
        fixableIssues: this.fixableIssues.length,
        issuesByRule: this.groupIssuesByRule(),
        filesCovered: this.getFilesCovered()
      },
      errors: this.errors,
      warnings: this.warnings,
      fixableIssues: this.fixableIssues,
      recommendations: this.generateRecommendations()
    };

    await fs.writeFile(
      path.join(this.projectRoot, 'eslint-analysis-report.json'),
      JSON.stringify(report, null, 2)
    );

    console.log(`📊 ESLint Analysis Complete:`);
    console.log(`   Total Issues: ${report.analysis.totalIssues}`);
    console.log(`   Errors: ${report.analysis.errors}`);
    console.log(`   Warnings: ${report.analysis.warnings}`);
    console.log(`   Fixable Issues: ${report.analysis.fixableIssues}`);
    console.log(`   Report saved: eslint-analysis-report.json`);
  }

  groupIssuesByRule() {
    const groups = {};
    const allIssues = [...this.errors, ...this.warnings];
    
    for (const issue of allIssues) {
      groups[issue.ruleId] = (groups[issue.ruleId] || 0) + 1;
    }
    
    return Object.entries(groups)
      .sort(([,a], [,b]) => b - a)
      .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});
  }

  getFilesCovered() {
    const files = new Set();
    const allIssues = [...this.errors, ...this.warnings];
    
    for (const issue of allIssues) {
      files.add(issue.file);
    }
    
    return Array.from(files);
  }

  generateRecommendations() {
    const recommendations = [];
    
    // High priority recommendations
    if (this.errors.length > 50) {
      recommendations.push({
        priority: 'high',
        action: 'Fix critical ESLint errors first',
        description: `${this.errors.length} errors found. Focus on errors before warnings.`
      });
    }

    if (this.fixableIssues.length > 20) {
      recommendations.push({
        priority: 'medium',
        action: 'Run ESLint auto-fix',
        description: `${this.fixableIssues.length} issues can be auto-fixed with 'npm run lint:fix'`
      });
    }

    // Rule-specific recommendations
    const ruleGroups = this.groupIssuesByRule();
    const topRules = Object.entries(ruleGroups).slice(0, 3);
    
    for (const [rule, count] of topRules) {
      if (count > 10) {
        recommendations.push({
          priority: 'medium',
          action: `Address ${rule} violations`,
          description: `${count} violations of ${rule} rule found`
        });
      }
    }

    return recommendations;
  }
}

// CLI interface
if (require.main === module) {
  const analyzer = new ESLintAnalyzer();
  analyzer.analyzeProject()
    .then(result => {
      console.log('✅ Analysis complete:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Analysis failed:', error);
      process.exit(1);
    });
}

module.exports = ESLintAnalyzer;
