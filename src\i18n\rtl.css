/**
 * RTL (Right-to-Left) specific styles for Sabone.store
 * These styles are applied when the language is set to Arabic
 */

/* Base RTL styles */
.rtl {
  direction: rtl;
  text-align: right;
}

/* Flip icons that indicate direction */
.rtl .icon-arrow-right {
  transform: scaleX(-1);
}

.rtl .icon-arrow-left {
  transform: scaleX(-1);
}

.rtl .icon-chevron-right {
  transform: scaleX(-1);
}

.rtl .icon-chevron-left {
  transform: scaleX(-1);
}

/* Adjust margins and paddings for RTL */
.rtl .ml-1 { margin-left: 0; margin-right: 0.25rem; }
.rtl .ml-2 { margin-left: 0; margin-right: 0.5rem; }
.rtl .ml-3 { margin-left: 0; margin-right: 0.75rem; }
.rtl .ml-4 { margin-left: 0; margin-right: 1rem; }
.rtl .ml-5 { margin-left: 0; margin-right: 1.25rem; }
.rtl .ml-6 { margin-left: 0; margin-right: 1.5rem; }
.rtl .ml-8 { margin-left: 0; margin-right: 2rem; }
.rtl .ml-10 { margin-left: 0; margin-right: 2.5rem; }
.rtl .ml-12 { margin-left: 0; margin-right: 3rem; }

.rtl .mr-1 { margin-right: 0; margin-left: 0.25rem; }
.rtl .mr-2 { margin-right: 0; margin-left: 0.5rem; }
.rtl .mr-3 { margin-right: 0; margin-left: 0.75rem; }
.rtl .mr-4 { margin-right: 0; margin-left: 1rem; }
.rtl .mr-5 { margin-right: 0; margin-left: 1.25rem; }
.rtl .mr-6 { margin-right: 0; margin-left: 1.5rem; }
.rtl .mr-8 { margin-right: 0; margin-left: 2rem; }
.rtl .mr-10 { margin-right: 0; margin-left: 2.5rem; }
.rtl .mr-12 { margin-right: 0; margin-left: 3rem; }

.rtl .pl-1 { padding-left: 0; padding-right: 0.25rem; }
.rtl .pl-2 { padding-left: 0; padding-right: 0.5rem; }
.rtl .pl-3 { padding-left: 0; padding-right: 0.75rem; }
.rtl .pl-4 { padding-left: 0; padding-right: 1rem; }
.rtl .pl-5 { padding-left: 0; padding-right: 1.25rem; }
.rtl .pl-6 { padding-left: 0; padding-right: 1.5rem; }
.rtl .pl-8 { padding-left: 0; padding-right: 2rem; }
.rtl .pl-10 { padding-left: 0; padding-right: 2.5rem; }
.rtl .pl-12 { padding-left: 0; padding-right: 3rem; }

.rtl .pr-1 { padding-right: 0; padding-left: 0.25rem; }
.rtl .pr-2 { padding-right: 0; padding-left: 0.5rem; }
.rtl .pr-3 { padding-right: 0; padding-left: 0.75rem; }
.rtl .pr-4 { padding-right: 0; padding-left: 1rem; }
.rtl .pr-5 { padding-right: 0; padding-left: 1.25rem; }
.rtl .pr-6 { padding-right: 0; padding-left: 1.5rem; }
.rtl .pr-8 { padding-right: 0; padding-left: 2rem; }
.rtl .pr-10 { padding-right: 0; padding-left: 2.5rem; }
.rtl .pr-12 { padding-right: 0; padding-left: 3rem; }

/* Adjust borders for RTL */
.rtl .border-l { border-left: 0; border-right-width: 1px; }
.rtl .border-r { border-right: 0; border-left-width: 1px; }
.rtl .border-l-2 { border-left-width: 0; border-right-width: 2px; }
.rtl .border-r-2 { border-right-width: 0; border-left-width: 2px; }

/* Adjust rounded corners for RTL */
.rtl .rounded-l { border-top-left-radius: 0; border-bottom-left-radius: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; }
.rtl .rounded-r { border-top-right-radius: 0; border-bottom-right-radius: 0; border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; }
.rtl .rounded-tl { border-top-left-radius: 0; border-top-right-radius: 0.25rem; }
.rtl .rounded-tr { border-top-right-radius: 0; border-top-left-radius: 0.25rem; }
.rtl .rounded-bl { border-bottom-left-radius: 0; border-bottom-right-radius: 0.25rem; }
.rtl .rounded-br { border-bottom-right-radius: 0; border-bottom-left-radius: 0.25rem; }

/* Adjust text alignment for RTL */
.rtl .text-left { text-align: right; }
.rtl .text-right { text-align: left; }

/* Adjust flex direction for RTL */
.rtl .flex-row { flex-direction: row-reverse; }
.rtl .flex-row-reverse { flex-direction: row; }

/* Adjust grid for RTL */
.rtl .space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.rtl .space-x-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Adjust positioning for RTL */
.rtl .left-0 { left: auto; right: 0; }
.rtl .right-0 { right: auto; left: 0; }
.rtl .left-1 { left: auto; right: 0.25rem; }
.rtl .right-1 { right: auto; left: 0.25rem; }
.rtl .left-2 { left: auto; right: 0.5rem; }
.rtl .right-2 { right: auto; left: 0.5rem; }
.rtl .left-3 { left: auto; right: 0.75rem; }
.rtl .right-3 { right: auto; left: 0.75rem; }
.rtl .left-4 { left: auto; right: 1rem; }
.rtl .right-4 { right: auto; left: 1rem; }
.rtl .left-5 { left: auto; right: 1.25rem; }
.rtl .right-5 { right: auto; left: 1.25rem; }

/* Adjust transforms for RTL */
.rtl .translate-x-1 { --tw-translate-x: 0.25rem; }
.rtl .-translate-x-1 { --tw-translate-x: -0.25rem; }
.rtl .translate-x-2 { --tw-translate-x: 0.5rem; }
.rtl .-translate-x-2 { --tw-translate-x: -0.5rem; }
.rtl .translate-x-3 { --tw-translate-x: 0.75rem; }
.rtl .-translate-x-3 { --tw-translate-x: -0.75rem; }
.rtl .translate-x-4 { --tw-translate-x: 1rem; }
.rtl .-translate-x-4 { --tw-translate-x: -1rem; }
.rtl .translate-x-5 { --tw-translate-x: 1.25rem; }
.rtl .-translate-x-5 { --tw-translate-x: -1.25rem; }

/* RTL-specific component styles */
.rtl .navbar-inner-container {
  margin-left: auto !important;
  margin-right: auto !important;
  transform: translateX(0) !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  width: 100% !important;
}

.rtl .navbar-logo {
  margin-right: 0;
  margin-left: 1rem;
}

.rtl .product-card-overlay {
  left: auto;
  right: 0;
}

.rtl .product-details-gallery {
  padding-right: 0;
  padding-left: 2rem;
}

.rtl .review-avatar {
  margin-right: 0;
  margin-left: 1rem;
}

.rtl .cart-item-image {
  margin-right: 0;
  margin-left: 1rem;
}

.rtl .checkout-steps {
  flex-direction: row-reverse;
}

/* RTL-specific form styles */
.rtl .form-label {
  text-align: right;
}

.rtl .form-checkbox,
.rtl .form-radio {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* RTL-specific font adjustments */
.rtl {
  font-family: 'Tajawal', 'Playfair Display', serif;
}

.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
  font-family: 'Tajawal', 'Playfair Display', serif;
}

/* RTL-specific animations */
@keyframes rtl-slide-in {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.rtl .slide-in {
  animation-name: rtl-slide-in;
}
