import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UserPlus } from "lucide-react";

// Form schema for validation
const signUpFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(8, { 
    message: "Password must be at least 8 characters" 
  }).regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    { message: "Password must include uppercase, lowercase, number and special character" }
  ),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SignUpFormValues = z.infer<typeof signUpFormSchema>;

interface SignUpModalProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSignInClick?: () => void;
}

const SignUpModal = ({ trigger, open, onOpenChange, onSignInClick }: SignUpModalProps) => {
  const { register } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use the form hook for validation
  const form = useForm<SignUpFormValues>({
    resolver: zodResolver(signUpFormSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Handle dialog open state changes
  const handleOpenChange = (newOpenState: boolean) => {
    console.log('SignUpModal: handleOpenChange called with', newOpenState);
    if (onOpenChange) {
      onOpenChange(newOpenState);
    }
  };

  const onSubmit = async (data: SignUpFormValues) => {
    setIsSubmitting(true);
    try {
      // Call the register function from AuthContext
      await register(data.name, data.email, data.password);
      // Modal will be closed automatically when authentication state changes
    } catch (error) {
      console.error("Registration error:", error);
      form.setError("root", { 
        type: "manual",
        message: "Registration failed. Please try again."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignUp = () => {
    // Auth0 will handle the OAuth flow
    register();
  };

  return (
    <Dialog
      open={open}
      onOpenChange={handleOpenChange}
      modal={true}
    >
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-md bg-sabone-dark-olive/95 backdrop-blur-md border-sabone-gold/30 text-sabone-cream animate-fade-in">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl md:text-3xl text-sabone-gold font-playfair text-center">
            Create Account
          </DialogTitle>
          <DialogDescription className="text-sabone-cream/90 text-center">
            Join Sabone to enjoy exclusive benefits, track orders, and more.
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center space-x-2">
          <div className="flex-grow h-px bg-sabone-gold/20"></div>
          <span className="text-xs text-sabone-cream/60 uppercase tracking-wider">Sign up with</span>
          <div className="flex-grow h-px bg-sabone-gold/20"></div>
        </div>

        <div className="flex flex-col space-y-4">
          <Button
            variant="outline"
            onClick={handleGoogleSignUp}
            className="bg-sabone-charcoal-deep border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10 hover:border-sabone-gold/50 transition-all duration-300"
          >
            <svg className="mr-2 h-4 w-4" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="google" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 488 512">
              <path fill="currentColor" d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"></path>
            </svg>
            Continue with Google
          </Button>

          <div className="flex items-center space-x-2">
            <div className="flex-grow h-px bg-sabone-gold/20"></div>
            <span className="text-xs text-sabone-cream/60 uppercase tracking-wider">Or with email</span>
            <div className="flex-grow h-px bg-sabone-gold/20"></div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Full Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Your Name"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream focus-visible:ring-sabone-gold/50 focus-visible:border-sabone-gold/50"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="<EMAIL>"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream focus-visible:ring-sabone-gold/50 focus-visible:border-sabone-gold/50"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="••••••••"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream focus-visible:ring-sabone-gold/50 focus-visible:border-sabone-gold/50"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="••••••••"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream focus-visible:ring-sabone-gold/50 focus-visible:border-sabone-gold/50"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
              {form.formState.errors.root && (
                <p className="text-red-400 text-sm">{form.formState.errors.root.message}</p>
              )}
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-sabone-gold hover:bg-sabone-gold-rich text-sabone-charcoal font-medium transition-all duration-300"
              >
                <UserPlus className="mr-2 h-4 w-4" />
                {isSubmitting ? "Creating Account..." : "Create Account"}
              </Button>
            </form>
          </Form>
        </div>

        <div className="mt-4 text-center text-sm text-sabone-cream/70">
          <p>
            Already have an account?{" "}
            <DialogClose asChild>
              <Button
                variant="link"
                className="p-0 text-sabone-gold hover:text-sabone-gold-accent"
                onClick={onSignInClick}
              >
                Sign in
              </Button>
            </DialogClose>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SignUpModal;
