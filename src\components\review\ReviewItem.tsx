import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Review } from '@/types/review';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { StarRating } from '@/components/ui/star-rating';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import {
  voteOnReview,
  getReviewVoteCounts,
  getUserVoteForReview
} from '@/services/reviewVotingService';
import { toast } from 'sonner';
import {
  MessageSquare,
  Calendar,
  ShoppingBag,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Image as ImageIcon,
  Share2
} from 'lucide-react';

interface ReviewItemProps {
  review: Review;
  showAdminResponse?: boolean;
  showVoting?: boolean;
  showImageGallery?: boolean;
}

const ReviewItem: React.FC<ReviewItemProps> = ({
  review,
  showAdminResponse = true,
  showVoting = true,
  showImageGallery = true
}) => {
  const { user, isAuthenticated } = useAuth();
  const [voteCounts, setVoteCounts] = useState({ helpful: 0, unhelpful: 0 });
  const [userVote, setUserVote] = useState<'helpful' | 'unhelpful' | null>(null);
  const [isVoting, setIsVoting] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  const {
    id,
    userName,
    rating,
    title,
    content,
    createdAt,
    images,
    isVerifiedPurchase,
    adminResponse,
    sentiment
  } = review;

  // Load vote data on component mount
  useEffect(() => {
    const counts = getReviewVoteCounts(id);
    setVoteCounts(counts);

    if (isAuthenticated && user) {
      const vote = getUserVoteForReview(id, user.sub);
      setUserVote(vote);
    }
  }, [id, isAuthenticated, user]);

  // Get user's initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formattedDate = format(new Date(createdAt), 'MMM d, yyyy');

  // Handle voting
  const handleVote = async (voteType: 'helpful' | 'unhelpful') => {
    if (!isAuthenticated || !user) {
      toast.error('Please log in to vote on reviews');
      return;
    }

    if (isVoting) return;

    setIsVoting(true);
    try {
      const result = voteOnReview(id, user.sub, voteType);
      if (result.success) {
        setVoteCounts(result.newCounts);
        // Update user vote (toggle off if same vote, otherwise set new vote)
        setUserVote(userVote === voteType ? null : voteType);
        toast.success('Thank you for your feedback!');
      } else {
        toast.error('Failed to record your vote');
      }
    } catch (error) {
      console.error('Error voting on review:', error);
      toast.error('Failed to record your vote');
    } finally {
      setIsVoting(false);
    }
  };

  // Get sentiment badge
  const getSentimentBadge = () => {
    if (!sentiment) return null;

    const colors = {
      positive: 'bg-green-500/10 text-green-500 border-green-500/30',
      neutral: 'bg-yellow-500/10 text-yellow-500 border-yellow-500/30',
      negative: 'bg-red-500/10 text-red-500 border-red-500/30'
    };

    return (
      <Badge variant="outline" className={colors[sentiment.label]}>
        {sentiment.label}
      </Badge>
    );
  };

  return (
    <Card className="bg-sabone-dark-olive/40 border-sabone-gold/20 overflow-hidden">
      <CardContent className="p-4 space-y-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 bg-sabone-gold/20 text-sabone-gold">
              <AvatarFallback>{getInitials(userName)}</AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-2 flex-wrap">
                <p className="font-medium text-sabone-gold">{userName}</p>
                {isVerifiedPurchase && (
                  <Badge
                    variant="outline"
                    className="bg-sabone-gold/10 text-sabone-gold border-sabone-gold/30 flex items-center gap-1 text-xs"
                  >
                    <ShoppingBag className="h-3 w-3" />
                    Verified Purchase
                  </Badge>
                )}
                {getSentimentBadge()}
              </div>
              <div className="flex items-center gap-2 text-xs text-sabone-gold/60">
                <Calendar className="h-3 w-3" />
                <span>{formattedDate}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StarRating rating={rating} size="sm" />
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-sabone-gold">{title}</h4>
          <p className="text-sabone-gold/80 text-sm">{content}</p>
        </div>

        {showImageGallery && images && images.length > 0 && (
          <div className="mt-3">
            <div className="flex items-center gap-2 mb-2">
              <ImageIcon className="h-4 w-4 text-sabone-gold/60" />
              <span className="text-xs text-sabone-gold/60">
                {images.length} photo{images.length !== 1 ? 's' : ''}
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {images.map((image, index) => (
                <div
                  key={index}
                  className="relative h-20 w-20 rounded-md overflow-hidden border border-sabone-gold/20 cursor-pointer hover:border-sabone-gold/40 transition-colors"
                  onClick={() => setSelectedImageIndex(index)}
                >
                  <img
                    src={image}
                    alt={`Review image ${index + 1}`}
                    className="h-full w-full object-cover hover:scale-105 transition-transform"
                  />
                  <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors" />
                </div>
              ))}
            </div>
          </div>
        )}

        {showAdminResponse && adminResponse && (
          <div className="mt-4 pt-4 border-t border-sabone-gold/20">
            <div className="bg-sabone-gold/5 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <MessageSquare className="h-4 w-4 text-sabone-gold" />
                <span className="text-sm font-medium text-sabone-gold">
                  Response from Sabone Team
                </span>
              </div>
              <p className="text-sabone-cream/90 text-sm">
                {adminResponse.content}
              </p>
              <p className="text-xs text-sabone-gold/60 mt-2">
                {format(new Date(adminResponse.respondedAt), 'MMM d, yyyy')}
              </p>
            </div>
          </div>
        )}
      </CardContent>

      {/* Voting and Actions Footer */}
      {showVoting && (
        <CardFooter className="px-4 py-3 bg-sabone-charcoal/20 border-t border-sabone-gold/10">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-4">
              {/* Helpfulness Voting */}
              <div className="flex items-center gap-2">
                <span className="text-xs text-sabone-gold/60">Was this helpful?</span>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleVote('helpful')}
                    disabled={isVoting}
                    className={`h-8 px-2 ${
                      userVote === 'helpful'
                        ? 'bg-green-500/20 text-green-500 hover:bg-green-500/30'
                        : 'text-sabone-gold/60 hover:text-green-500 hover:bg-green-500/10'
                    }`}
                  >
                    <ThumbsUp className="h-3 w-3 mr-1" />
                    <span className="text-xs">{voteCounts.helpful}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleVote('unhelpful')}
                    disabled={isVoting}
                    className={`h-8 px-2 ${
                      userVote === 'unhelpful'
                        ? 'bg-red-500/20 text-red-500 hover:bg-red-500/30'
                        : 'text-sabone-gold/60 hover:text-red-500 hover:bg-red-500/10'
                    }`}
                  >
                    <ThumbsDown className="h-3 w-3 mr-1" />
                    <span className="text-xs">{voteCounts.unhelpful}</span>
                  </Button>
                </div>
              </div>

              {/* Helpfulness Ratio Display */}
              {(voteCounts.helpful + voteCounts.unhelpful) > 0 && (
                <div className="text-xs text-sabone-gold/60">
                  {Math.round((voteCounts.helpful / (voteCounts.helpful + voteCounts.unhelpful)) * 100)}% found helpful
                </div>
              )}
            </div>

            {/* Additional Actions */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-sabone-gold/60 hover:text-sabone-gold hover:bg-sabone-gold/10"
                onClick={() => {
                  // Share functionality could be implemented here
                  toast.success('Review link copied to clipboard!');
                }}
              >
                <Share2 className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-sabone-gold/60 hover:text-red-500 hover:bg-red-500/10"
                onClick={() => {
                  // Report functionality could be implemented here
                  toast.success('Review reported for moderation');
                }}
              >
                <Flag className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardFooter>
      )}

      {/* Image Gallery Modal */}
      {selectedImageIndex !== null && images && (
        <div
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImageIndex(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={images[selectedImageIndex]}
              alt={`Review image ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 bg-black/50 text-white hover:bg-black/70"
              onClick={() => setSelectedImageIndex(null)}
            >
              ✕
            </Button>
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : images.length - 1);
                  }}
                >
                  ‹
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedImageIndex(selectedImageIndex < images.length - 1 ? selectedImageIndex + 1 : 0);
                  }}
                >
                  ›
                </Button>
              </>
            )}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded text-sm">
              {selectedImageIndex + 1} of {images.length}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default ReviewItem;
