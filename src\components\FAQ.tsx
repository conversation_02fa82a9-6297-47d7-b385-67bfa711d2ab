import { useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import FAQSchema from "@/components/seo/FAQSchema";

// FAQ data
const faqItems = [
  {
    question: "What makes Sabone soaps different from commercial brands?",
    answer: "Sabone soaps are handcrafted using traditional Arabic methods with premium natural ingredients. Unlike commercial brands that often use synthetic detergents, our soaps maintain the natural glycerin and are free from harsh chemicals, making them gentler on your skin and the environment."
  },
  {
    question: "Are Sabone products suitable for sensitive skin?",
    answer: "Yes, many of our products are specifically formulated for sensitive skin. Our Sensitive Scalp Shampoo and Rose Clay Glow Bar are particularly gentle. We recommend checking the ingredients list for each product if you have specific sensitivities or allergies."
  },
  {
    question: "How long do your soap bars typically last?",
    answer: "With proper care, our soap bars typically last 4-6 weeks with daily use. To extend the life of your soap, we recommend keeping it on a well-draining soap dish and allowing it to dry between uses."
  },
  {
    question: "Are your products environmentally friendly?",
    answer: "Yes, sustainability is a core value at Sabone. Our products use biodegradable ingredients, and our packaging is either recyclable or compostable. We're committed to minimizing our environmental footprint throughout our production process."
  },
  {
    question: "Do you offer international shipping?",
    answer: "Yes, we ship to most countries worldwide. International shipping rates and delivery times vary by location. You can see the shipping options available to your country during checkout."
  },
  {
    question: "What is the shelf life of your products?",
    answer: "Our soap bars have a shelf life of approximately 12-18 months, while our liquid shampoos last about 12 months after opening. For optimal longevity, store products in a cool, dry place away from direct sunlight."
  }
];

const FAQ = () => {
  const [openItems, setOpenItems] = useState<string[]>([]);

  return (
    <section className="py-12 md:py-16">
      <h2 className="text-2xl md:text-3xl font-playfair font-bold text-sabone-gold text-center mb-8">
        Frequently Asked Questions
      </h2>
      
      {/* Add FAQ Schema for structured data */}
      <FAQSchema items={faqItems} />
      
      <div className="max-w-3xl mx-auto px-4">
        <Accordion type="multiple" value={openItems} onValueChange={setOpenItems} className="space-y-4">
          {faqItems.map((item, index) => (
            <AccordionItem 
              key={index} 
              value={`item-${index}`}
              className="border border-sabone-gold/20 rounded-md bg-sabone-dark-olive/30 backdrop-blur-sm overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-4 text-left font-medium text-sabone-gold hover:text-sabone-gold-accent transition-colors">
                {item.question}
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4 pt-0 text-sabone-cream/90">
                {item.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

export default FAQ;
