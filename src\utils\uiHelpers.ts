import { OrderStatus } from '@/types/order'; // Import OrderStatus

// Helper function to get status badge color
export const getStatusColor = (status: OrderStatus): string => {
  const statusColors = {
    pending: "bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/20",
    processing: "bg-blue-500/20 text-blue-500 hover:bg-blue-500/20",
    shipped: "bg-purple-500/20 text-purple-500 hover:bg-purple-500/20",
    delivered: "bg-green-500/20 text-green-500 hover:bg-green-500/20",
    cancelled: "bg-red-500/20 text-red-500 hover:bg-red-500/20"
  };
  
  return statusColors[status] || "bg-gray-500/20 text-gray-500";
};
