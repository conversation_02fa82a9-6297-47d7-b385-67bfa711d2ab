import { useState, useEffect } from 'react';

interface MobileCapabilities {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  screenWidth: number;
  screenHeight: number;
  orientation: 'portrait' | 'landscape';
  userAgent: string;
}

interface MobileInteractions {
  onTouchStart: (callback: (event: TouchEvent) => void) => void;
  onTouchEnd: (callback: (event: TouchEvent) => void) => void;
  onSwipe: (direction: 'left' | 'right' | 'up' | 'down', callback: () => void) => void;
  enablePullToRefresh: (callback: () => void) => void;
}

export const useMobile = (): MobileCapabilities & MobileInteractions => {
  const [capabilities, setCapabilities] = useState<MobileCapabilities>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
    screenWidth: 0,
    screenHeight: 0,
    orientation: 'landscape',
    userAgent: ''
  });

  useEffect(() => {
    const updateCapabilities = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const userAgent = navigator.userAgent;
      
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const orientation = width > height ? 'landscape' : 'portrait';

      setCapabilities({
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
        screenWidth: width,
        screenHeight: height,
        orientation,
        userAgent
      });
    };

    updateCapabilities();
    window.addEventListener('resize', updateCapabilities);
    window.addEventListener('orientationchange', updateCapabilities);

    return () => {
      window.removeEventListener('resize', updateCapabilities);
      window.removeEventListener('orientationchange', updateCapabilities);
    };
  }, []);

  const onTouchStart = (callback: (event: TouchEvent) => void) => {
    document.addEventListener('touchstart', callback, { passive: true });
    return () => document.removeEventListener('touchstart', callback);
  };

  const onTouchEnd = (callback: (event: TouchEvent) => void) => {
    document.addEventListener('touchend', callback, { passive: true });
    return () => document.removeEventListener('touchend', callback);
  };

  const onSwipe = (direction: 'left' | 'right' | 'up' | 'down', callback: () => void) => {
    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      endX = e.changedTouches[0].clientX;
      endY = e.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;
      const minSwipeDistance = 50;

      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (Math.abs(deltaX) > minSwipeDistance) {
          if (deltaX > 0 && direction === 'right') callback();
          if (deltaX < 0 && direction === 'left') callback();
        }
      } else {
        // Vertical swipe
        if (Math.abs(deltaY) > minSwipeDistance) {
          if (deltaY > 0 && direction === 'down') callback();
          if (deltaY < 0 && direction === 'up') callback();
        }
      }
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  };

  const enablePullToRefresh = (callback: () => void) => {
    let startY = 0;
    const pullThreshold = 60;
    let isRefreshing = false;

    const handleTouchStart = (e: TouchEvent) => {
      if (window.scrollY === 0) {
        startY = e.touches[0].clientY;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (window.scrollY === 0 && !isRefreshing) {
        const currentY = e.touches[0].clientY;
        const pullDistance = currentY - startY;

        if (pullDistance > pullThreshold) {
          e.preventDefault();
          // Add visual feedback here if needed
          document.body.style.transform = `translateY(${Math.min(pullDistance / 3, 30)}px)`;
        }
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (window.scrollY === 0 && !isRefreshing) {
        const endY = e.changedTouches[0].clientY;
        const pullDistance = endY - startY;

        document.body.style.transform = '';

        if (pullDistance > pullThreshold) {
          isRefreshing = true;
          callback();
          setTimeout(() => {
            isRefreshing = false;
          }, 1000);
        }
      }
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: false });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  };

  return {
    ...capabilities,
    onTouchStart,
    onTouchEnd,
    onSwipe,
    enablePullToRefresh
  };
};

export default useMobile; 