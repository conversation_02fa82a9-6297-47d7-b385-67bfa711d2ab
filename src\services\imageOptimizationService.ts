/**
 * Image Optimization Service
 * Provides utilities for optimizing images, generating responsive variants,
 * and handling modern image formats
 */

import { DEFAULT_RESPONSIVE_SIZES, type ResponsiveSize } from '../utils/imageUtils';

export interface ImageOptimizationOptions {
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
}

export interface ResponsiveImageSet {
  src: string;
  srcSet: string;
  sizes: string;
  sources: Array<{
    srcSet: string;
    type: string;
    sizes: string;
  }>;
}

/**
 * Browser support detection for modern image formats
 */
class ImageFormatSupport {
  private static cache = new Map<string, boolean>();

  static async checkSupport(format: 'webp' | 'avif'): Promise<boolean> {
    if (this.cache.has(format)) {
      return this.cache.get(format)!;
    }

    const supported = await this.testFormat(format);
    this.cache.set(format, supported);
    return supported;
  }

  private static testFormat(format: 'webp' | 'avif'): Promise<boolean> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      
      try {
        const dataURL = canvas.toDataURL(`image/${format}`);
        resolve(dataURL.startsWith(`data:image/${format}`));
      } catch {
        resolve(false);
      }
    });
  }

  static async getSupportedFormats(): Promise<string[]> {
    const formats = ['avif', 'webp'] as const;
    const supported = await Promise.all(
      formats.map(async (format) => ({
        format,
        supported: await this.checkSupport(format)
      }))
    );

    return supported
      .filter(({ supported }) => supported)
      .map(({ format }) => format);
  }
}

/**
 * Image URL generator for different formats and sizes
 */
export class ImageUrlGenerator {
  private baseUrl: string;
  private cdnUrl?: string;

  constructor(baseUrl: string = '', cdnUrl?: string) {
    this.baseUrl = baseUrl;
    this.cdnUrl = cdnUrl;
  }

  /**
   * Generate optimized image URL
   */
  generateUrl(
    imagePath: string,
    options: ImageOptimizationOptions = {}
  ): string {
    const {
      quality: _quality = 85,
      format: _format = 'webp',
      width: _width,
      height: _height,
      fit: _fit = 'cover'
    } = options;

    // If using a CDN, generate CDN URL with optimization parameters
    if (this.cdnUrl) {
      return this.generateCdnUrl(imagePath, options);
    }

    // For local images, generate responsive filename
    return this.generateResponsiveUrl(imagePath, options);
  }

  /**
   * Generate CDN URL with optimization parameters (e.g., Cloudinary, ImageKit)
   */
  private generateCdnUrl(
    imagePath: string,
    options: ImageOptimizationOptions
  ): string {
    const {
      quality = 85,
      format = 'webp',
      width,
      height,
      fit = 'cover'
    } = options;

    // Example for Cloudinary-style URL generation
    const transformations = [];
    
    if (width || height) {
      const dimensions = [
        width && `w_${width}`,
        height && `h_${height}`,
        `c_${fit}`
      ].filter(Boolean).join(',');
      transformations.push(dimensions);
    }

    transformations.push(`q_${quality}`);
    transformations.push(`f_${format}`);

    const transformString = transformations.join('/');
    return `${this.cdnUrl}/${transformString}/${imagePath}`;
  }

  /**
   * Generate responsive image URL for local images
   */
  private generateResponsiveUrl(
    imagePath: string,
    options: ImageOptimizationOptions
  ): string {
    const { width, format = 'webp' } = options;
    
    // Extract path without extension
    const pathParts = imagePath.split('.');
    const _extension = pathParts.pop();
    const basePath = pathParts.join('.');

    // Generate responsive filename
    if (width) {
      return `${this.baseUrl}${basePath}-${width}w.${format}`;
    }

    return `${this.baseUrl}${basePath}.${format}`;
  }

  /**
   * Generate responsive image set
   */
  async generateResponsiveSet(
    imagePath: string,
    options: {
      sizes?: Array<ResponsiveSize>;
      enableAVIF?: boolean;
      enableWebP?: boolean;
      quality?: number;
      sizesAttribute?: string;
    } = {}
  ): Promise<ResponsiveImageSet> {
    const {
      sizes = [...DEFAULT_RESPONSIVE_SIZES],
      enableAVIF = true,
      enableWebP = true,
      quality = 85,
      sizesAttribute = "(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
    } = options;

    const supportedFormats = await ImageFormatSupport.getSupportedFormats();
    const sources = [];

    // Generate AVIF sources if supported and enabled
    if (enableAVIF && supportedFormats.includes('avif')) {
      const avifSrcSet = sizes
        .map(({ width }) => {
          const url = this.generateUrl(imagePath, { width, format: 'avif', quality });
          return `${url} ${width}w`;
        })
        .join(', ');

      sources.push({
        srcSet: avifSrcSet,
        type: 'image/avif',
        sizes: sizesAttribute
      });
    }

    // Generate WebP sources if supported and enabled
    if (enableWebP && supportedFormats.includes('webp')) {
      const webpSrcSet = sizes
        .map(({ width }) => {
          const url = this.generateUrl(imagePath, { width, format: 'webp', quality });
          return `${url} ${width}w`;
        })
        .join(', ');

      sources.push({
        srcSet: webpSrcSet,
        type: 'image/webp',
        sizes: sizesAttribute
      });
    }

    // Generate fallback sources (original format)
    const originalFormat = imagePath.split('.').pop() as 'jpeg' | 'png' || 'jpeg';
    const fallbackSrcSet = sizes
      .map(({ width }) => {
        const url = this.generateUrl(imagePath, { width, format: originalFormat, quality });
        return `${url} ${width}w`;
      })
      .join(', ');

    sources.push({
      srcSet: fallbackSrcSet,
      type: originalFormat === 'png' ? 'image/png' : 'image/jpeg',
      sizes: sizesAttribute
    });

    // Default src (largest size)
    const defaultSrc = this.generateUrl(imagePath, {
      width: sizes[sizes.length - 1].width,
      format: originalFormat,
      quality
    });

    return {
      src: defaultSrc,
      srcSet: fallbackSrcSet,
      sizes: sizesAttribute,
      sources
    };
  }
}

/**
 * Lazy loading utilities
 */
export class LazyLoadingManager {
  private static observer?: IntersectionObserver;
  private static loadedImages = new Set<string>();

  static init() {
    if (typeof window === 'undefined' || this.observer) return;

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            this.loadImage(img);
            this.observer?.unobserve(img);
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    );
  }

  static observe(element: HTMLElement) {
    this.init();
    this.observer?.observe(element);
  }

  static unobserve(element: HTMLElement) {
    this.observer?.unobserve(element);
  }

  private static loadImage(img: HTMLImageElement) {
    const src = img.dataset.src;
    const srcSet = img.dataset.srcset;

    if (src && !this.loadedImages.has(src)) {
      img.src = src;
      if (srcSet) {
        img.srcset = srcSet;
      }
      this.loadedImages.add(src);
      
      // Remove data attributes
      delete img.dataset.src;
      delete img.dataset.srcset;
      
      // Add loaded class for animations
      img.classList.add('loaded');
    }
  }

  static preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.loadedImages.has(src)) {
        resolve();
        return;
      }

      const img = new Image();
      img.onload = () => {
        this.loadedImages.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }

  static preloadImages(sources: string[]): Promise<void[]> {
    return Promise.all(sources.map(src => this.preloadImage(src)));
  }
}

/**
 * Main image optimization service
 */
export class ImageOptimizationService {
  private urlGenerator: ImageUrlGenerator;

  constructor(baseUrl: string = '', cdnUrl?: string) {
    this.urlGenerator = new ImageUrlGenerator(baseUrl, cdnUrl);
    LazyLoadingManager.init();
  }

  /**
   * Get optimized image URL
   */
  getOptimizedUrl(imagePath: string, options: ImageOptimizationOptions = {}): string {
    return this.urlGenerator.generateUrl(imagePath, options);
  }

  /**
   * Get responsive image set
   */
  async getResponsiveImageSet(
    imagePath: string,
    options: Parameters<ImageUrlGenerator['generateResponsiveSet']>[1] = {}
  ): Promise<ResponsiveImageSet> {
    return this.urlGenerator.generateResponsiveSet(imagePath, options);
  }

  /**
   * Check browser support for image formats
   */
  async getSupportedFormats(): Promise<string[]> {
    return ImageFormatSupport.getSupportedFormats();
  }

  /**
   * Preload critical images
   */
  async preloadImages(sources: string[]): Promise<void> {
    try {
      await LazyLoadingManager.preloadImages(sources);
    } catch (error) {
      console.warn('Failed to preload some images:', error);
    }
  }

  /**
   * Setup lazy loading for an element
   */
  setupLazyLoading(element: HTMLElement): void {
    LazyLoadingManager.observe(element);
  }

  /**
   * Remove lazy loading for an element
   */
  removeLazyLoading(element: HTMLElement): void {
    LazyLoadingManager.unobserve(element);
  }
}

// Default instance
export const imageOptimizationService = new ImageOptimizationService();

// Export utilities
export { ImageFormatSupport, LazyLoadingManager };
