// Import Stripe with your secret key
import Stripe from 'stripe';
import express from 'express';
import dotenv from 'dotenv';

dotenv.config(); // Ensure environment variables are loaded

const stripe = new Stripe(process.env.VITE_STRIPE_SECRET_KEY);
const router = express.Router();

/**
 * Create a payment intent with <PERSON><PERSON>
 *
 * This endpoint creates a PaymentIntent, which is required to collect payment information
 * and process the payment on the client side.
 *
 * @route POST /api/create-payment-intent
 * @param {number} amount - The amount to charge in cents
 * @param {string} currency - The currency to use (default: usd)
 * @returns {Object} The client secret for the payment intent
 */
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency = 'usd' } = req.body;

    // Validate the amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount. Amount must be greater than 0.'
      });
    }

    // Create a payment intent with the specified amount and currency
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      // Payment method types to accept (card is the most common)
      payment_method_types: ['card'],
      // Metadata can be used to store additional information
      metadata: {
        integration_check: 'sabone_payment'
      },
      // Optional description for the payment
      description: 'Sabone luxury soaps and shampoos',
    });

    // Return the client secret to the client
    res.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);

    // Return an appropriate error message
    res.status(500).json({
      error: error.message || 'An error occurred while creating the payment intent.'
    });
  }
});

export default router;
