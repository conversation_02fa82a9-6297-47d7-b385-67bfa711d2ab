import { Helmet } from 'react-helmet-async';
import CanonicalUrl from './CanonicalUrl';

interface SEOProps {
  title: string;
  description: string;
  canonical?: string;
  type?: 'website' | 'product' | 'article';
  image?: string;
  noIndex?: boolean;
  keywords?: string[];
  schemaMarkup?: object | object[];
  children?: React.ReactNode;
}

/**
 * Comprehensive SEO component for consistent metadata across pages
 */
const SEO = ({
  title,
  description,
  canonical,
  type = 'website',
  image = '/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png',
  noIndex = false,
  keywords = [],
  schemaMarkup,
  children,
}: SEOProps) => {
  // Ensure title has brand name
  const fullTitle = title.includes('Sabone') 
    ? title 
    : `${title} | Sabone Natural Soaps & Shampoos`;
  
  // Ensure image has absolute URL
  const fullImage = image.startsWith('http') 
    ? image 
    : `https://sabone.store${image}`;

  return (
    <>
      <Helmet>
        {/* Basic Metadata */}
        <title>{fullTitle}</title>
        <meta name="description" content={description} />
        {keywords.length > 0 && (
          <meta name="keywords" content={keywords.join(', ')} />
        )}
        
        {/* Robots */}
        {noIndex && <meta name="robots" content="noindex, nofollow" />}
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content={type} />
        <meta property="og:title" content={fullTitle} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content={fullImage} />
        <meta property="og:url" content={`https://sabone.store${canonical ? `/${canonical}` : ''}`} />
        <meta property="og:site_name" content="Sabone" />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={fullTitle} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={fullImage} />
        
        {/* Schema.org markup */}
        {schemaMarkup && (
          <script type="application/ld+json">
            {JSON.stringify(schemaMarkup)}
          </script>
        )}
      </Helmet>
      
      {/* Canonical URL */}
      {canonical && <CanonicalUrl path={canonical} />}
      
      {/* Additional SEO elements */}
      {children}
    </>
  );
};

export default SEO;
