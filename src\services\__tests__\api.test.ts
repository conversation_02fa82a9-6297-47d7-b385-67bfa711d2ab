/**
 * API Service Tests
 * 
 * Comprehensive test suite for the centralized API service layer
 */

import { api, createApiError, apiCache } from '../api';
import { validateFormData, checkRateLimit } from '@/middleware/securityMiddleware';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/middleware/securityMiddleware');
jest.mock('sonner');

const mockValidateFormData = validateFormData as jest.MockedFunction<typeof validateFormData>;
const mockCheckRateLimit = checkRateLimit as jest.MockedFunction<typeof checkRateLimit>;
const mockToast = toast as jest.Mocked<typeof toast>;

// Mock fetch
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'mock-uuid-123'),
  },
});

describe('API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    apiCache.clear();
    
    // Default mock implementations
    mockCheckRateLimit.mockReturnValue(true);
    mockValidateFormData.mockReturnValue({
      isValid: true,
      sanitizedData: {},
      errors: [],
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('createApiError', () => {
    it('should create an API error with all properties', () => {
      const error = createApiError(
        'Test error',
        400,
        'TEST_ERROR',
        { detail: 'test' }
      );

      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Test error');
      expect(error.status).toBe(400);
      expect(error.code).toBe('TEST_ERROR');
      expect(error.details).toEqual({ detail: 'test' });
      expect(error.timestamp).toBeGreaterThan(0);
    });

    it('should create an API error with minimal properties', () => {
      const error = createApiError('Simple error');

      expect(error.message).toBe('Simple error');
      expect(error.status).toBeUndefined();
      expect(error.code).toBeUndefined();
      expect(error.details).toBeUndefined();
      expect(error.timestamp).toBeGreaterThan(0);
    });
  });

  describe('API Cache', () => {
    it('should store and retrieve cached data', () => {
      const testData = { test: 'data' };
      apiCache.set('test-key', testData, 1000);

      const retrieved = apiCache.get('test-key');
      expect(retrieved).toEqual(testData);
    });

    it('should return null for non-existent keys', () => {
      const result = apiCache.get('non-existent');
      expect(result).toBeNull();
    });

    it('should expire cached data after TTL', () => {
      const testData = { test: 'data' };
      apiCache.set('test-key', testData, -1); // Already expired

      const result = apiCache.get('test-key');
      expect(result).toBeNull();
    });

    it('should delete cached data', () => {
      apiCache.set('test-key', { test: 'data' });
      apiCache.delete('test-key');

      const result = apiCache.get('test-key');
      expect(result).toBeNull();
    });

    it('should clear all cached data', () => {
      apiCache.set('key1', { test: 'data1' });
      apiCache.set('key2', { test: 'data2' });
      apiCache.clear();

      expect(apiCache.get('key1')).toBeNull();
      expect(apiCache.get('key2')).toBeNull();
    });

    it('should provide cache statistics', () => {
      apiCache.set('key1', { test: 'data1' });
      apiCache.set('key2', { test: 'data2' });

      const stats = apiCache.getStats();
      expect(stats.size).toBe(2);
      expect(stats.entries).toBe(2);
    });
  });

  describe('GET requests', () => {
    it('should make successful GET request', async () => {
      const responseData = { message: 'success', data: { id: 1 } };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await api.get('/test');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.any(Headers),
        })
      );

      expect(result.data).toEqual(responseData);
      expect(result.success).toBe(true);
    });

    it('should use cached data for subsequent requests', async () => {
      const responseData = { message: 'success', data: { id: 1 } };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      // First request
      await api.get('/test');
      
      // Second request should use cache
      const result = await api.get('/test');

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.data).toEqual(responseData);
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new TypeError('Network error'));

      await expect(api.get('/test')).rejects.toThrow('Network error');
      expect(mockToast.error).toHaveBeenCalledWith('Network error. Please check your connection.');
    });

    it('should handle HTTP errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ message: 'Resource not found' }),
      } as Response);

      await expect(api.get('/test')).rejects.toThrow('Resource not found');
      expect(mockToast.error).toHaveBeenCalledWith('Resource not found.');
    });

    it('should handle rate limiting', async () => {
      mockCheckRateLimit.mockReturnValueOnce(false);

      await expect(api.get('/test')).rejects.toThrow('Rate limit exceeded');
    });
  });

  describe('POST requests', () => {
    it('should make successful POST request', async () => {
      const requestData = { name: 'test' };
      const responseData = { message: 'created', data: { id: 1, ...requestData } };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await api.post('/test', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(requestData),
          headers: expect.any(Headers),
        })
      );

      expect(result.data).toEqual(responseData);
    });

    it('should sanitize request data', async () => {
      const requestData = { name: 'test', malicious: '<script>alert("xss")</script>' };
      const sanitizedData = { name: 'test', malicious: 'alert("xss")' };
      
      mockValidateFormData.mockReturnValueOnce({
        isValid: true,
        sanitizedData,
        errors: [],
      });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: sanitizedData }),
      } as Response);

      await api.post('/test', requestData);

      expect(mockValidateFormData).toHaveBeenCalledWith(requestData);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test',
        expect.objectContaining({
          body: JSON.stringify(sanitizedData),
        })
      );
    });

    it('should reject invalid request data', async () => {
      const requestData = { malicious: '<script>alert("xss")</script>' };
      
      mockValidateFormData.mockReturnValueOnce({
        isValid: false,
        sanitizedData: {},
        errors: ['XSS detected'],
      });

      await expect(api.post('/test', requestData)).rejects.toThrow('Invalid request data');
    });

    it('should invalidate cache after successful POST', async () => {
      // Setup cache
      apiCache.set('/test', { data: 'old' });
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: 'new' }),
      } as Response);

      await api.post('/test', {});

      // Cache should be invalidated
      expect(apiCache.get('/test')).toBeNull();
    });
  });

  describe('PUT requests', () => {
    it('should make successful PUT request', async () => {
      const requestData = { id: 1, name: 'updated' };
      const responseData = { message: 'updated', data: requestData };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await api.put('/test/1', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(requestData),
        })
      );

      expect(result.data).toEqual(responseData);
    });
  });

  describe('DELETE requests', () => {
    it('should make successful DELETE request', async () => {
      const responseData = { message: 'deleted', data: { success: true } };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await api.delete('/test/1');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test/1',
        expect.objectContaining({
          method: 'DELETE',
        })
      );

      expect(result.data).toEqual(responseData);
    });
  });

  describe('File uploads', () => {
    it('should upload file with progress tracking', async () => {
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      const onProgress = jest.fn();
      
      // Mock XMLHttpRequest
      const mockXHR = {
        upload: { addEventListener: jest.fn() },
        addEventListener: jest.fn(),
        open: jest.fn(),
        setRequestHeader: jest.fn(),
        send: jest.fn(),
        status: 200,
        responseText: JSON.stringify({ data: { url: '/uploads/test.txt' } }),
      };

      // @ts-expect-error - Mocking XMLHttpRequest for testing
      global.XMLHttpRequest = jest.fn(() => mockXHR);

      const _uploadPromise = api.upload('/upload', file, { onProgress });

      // Simulate successful upload
      const loadHandler = mockXHR.addEventListener.mock.calls.find(
        call => call[0] === 'load'
      )[1];
      await loadHandler();

      expect(mockXHR.open).toHaveBeenCalledWith('POST', 'http://localhost:3001/api/upload');
      expect(mockXHR.send).toHaveBeenCalled();
    });

    it('should handle upload errors', async () => {
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      
      const mockXHR = {
        upload: { addEventListener: jest.fn() },
        addEventListener: jest.fn(),
        open: jest.fn(),
        setRequestHeader: jest.fn(),
        send: jest.fn(),
      };

      // @ts-expect-error - Mocking XMLHttpRequest for testing purposes
      global.XMLHttpRequest = jest.fn(() => mockXHR);

      const uploadPromise = api.upload('/upload', file);

      // Simulate error
      const errorHandler = mockXHR.addEventListener.mock.calls.find(
        call => call[0] === 'error'
      )[1];
      
      await expect(async () => {
        errorHandler();
        await uploadPromise;
      }).rejects.toThrow('Upload failed');
    });
  });

  describe('Retry mechanism', () => {
    it('should retry on network errors', async () => {
      mockFetch
        .mockRejectedValueOnce(new TypeError('Network error'))
        .mockRejectedValueOnce(new TypeError('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: () => Promise.resolve({ data: 'success' }),
        } as Response);

      const result = await api.get('/test', { retries: 2 });

      expect(mockFetch).toHaveBeenCalledTimes(3);
      expect(result.data).toEqual({ data: 'success' });
    });

    it('should retry on server errors', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: () => Promise.resolve({ message: 'Internal server error' }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: () => Promise.resolve({ data: 'success' }),
        } as Response);

      const result = await api.get('/test', { retries: 1 });

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(result.data).toEqual({ data: 'success' });
    });

    it('should not retry on client errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ message: 'Bad request' }),
      } as Response);

      await expect(api.get('/test', { retries: 2 })).rejects.toThrow('Bad request');
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should stop retrying after max attempts', async () => {
      mockFetch.mockRejectedValue(new TypeError('Network error'));

      await expect(api.get('/test', { retries: 2 })).rejects.toThrow('Network error');
      expect(mockFetch).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });

  describe('Request headers', () => {
    it('should add security headers', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: 'success' }),
      } as Response);

      await api.get('/test');

      const [, requestOptions] = mockFetch.mock.calls[0];
      const headers = requestOptions?.headers as Headers;
      
      expect(headers.get('Content-Type')).toBe('application/json');
      expect(headers.get('Accept')).toBe('application/json');
      expect(headers.get('X-Client-Version')).toBe('1.0.0');
      expect(headers.get('X-Request-ID')).toBe('mock-uuid-123');
    });

    it('should add auth token in development', async () => {
      // Mock development environment
      const originalEnv = import.meta.env.DEV;
      // @ts-expect-error - Mocking environment variable for testing
      import.meta.env.DEV = true;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: 'success' }),
      } as Response);

      await api.get('/test');

      const [, requestOptions] = mockFetch.mock.calls[0];
      const headers = requestOptions?.headers as Headers;
      
      expect(headers.get('Authorization')).toBe('Bearer dev-token-123456');

      // Restore environment
      // @ts-expect-error - Restoring environment variable after testing
      import.meta.env.DEV = originalEnv;
    });

    it('should skip auth when specified', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ data: 'success' }),
      } as Response);

      await api.get('/test', { skipAuth: true });

      const [, requestOptions] = mockFetch.mock.calls[0];
      const headers = requestOptions?.headers as Headers;
      
      expect(headers.get('Authorization')).toBeNull();
    });
  });

  describe('Error handling', () => {
    it('should show appropriate error messages for different status codes', async () => {
      const testCases = [
        { status: 401, expectedMessage: 'Authentication required. Please log in.' },
        { status: 403, expectedMessage: 'Access denied. You don\'t have permission for this action.' },
        { status: 404, expectedMessage: 'Resource not found.' },
        { status: 429, expectedMessage: 'Too many requests. Please try again later.' },
        { status: 500, expectedMessage: 'Server error. Please try again later.' },
      ];

      for (const testCase of testCases) {
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: testCase.status,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: () => Promise.resolve({ message: `HTTP ${testCase.status}` }),
        } as Response);

        await expect(api.get('/test')).rejects.toThrow();
        expect(mockToast.error).toHaveBeenCalledWith(testCase.expectedMessage);

        mockToast.error.mockClear();
      }
    });

    it('should handle timeout errors', async () => {
      const abortError = new Error('Request timeout');
      abortError.name = 'AbortError';
      
      mockFetch.mockRejectedValueOnce(abortError);

      await expect(api.get('/test')).rejects.toThrow('Request timeout');
      expect(mockToast.error).toHaveBeenCalledWith('Request timeout. Please check your connection.');
    });
  });

  describe('Cache management', () => {
    it('should clear all cache', () => {
      apiCache.set('key1', { data: 'test1' });
      apiCache.set('key2', { data: 'test2' });
      
      api.clearCache();
      
      expect(apiCache.get('key1')).toBeNull();
      expect(apiCache.get('key2')).toBeNull();
    });

    it('should provide cache statistics', () => {
      apiCache.set('key1', { data: 'test1' });
      
      const stats = api.getCacheStats();
      
      expect(stats.size).toBe(1);
      expect(stats.entries).toBe(1);
    });
  });
});
