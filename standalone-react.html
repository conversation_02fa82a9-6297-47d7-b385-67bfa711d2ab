<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Standalone React Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #1c1c1c;
      color: #e5dcc5;
      margin: 0;
      padding: 0;
    }
    #root {
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background-color: #2a2a1f;
      border-radius: 8px;
      border: 1px solid #c6a870;
    }
    h1, h2 {
      color: #c6a870;
    }
    button {
      background-color: #c6a870;
      color: #1c1c1c;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    .section {
      margin-top: 20px;
      padding: 15px;
      background-color: #333328;
      border-radius: 4px;
    }
    pre {
      background-color: #1c1c1c;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <div id="root">
    <!-- <PERSON>act will render here -->
    <div style="text-align: center; padding: 40px;">
      <p>Loading React application...</p>
    </div>
  </div>

  <!-- Load React -->
  <script src="https://unpkg.com/react@18/umd/react.production.min.js" crossorigin></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js" crossorigin></script>
  
  <!-- Load Babel for JSX -->
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  
  <!-- React Component -->
  <script type="text/babel">
    // Simple counter component
    function Counter() {
      const [count, setCount] = React.useState(0);
      
      return (
        <div>
          <p>Count: {count}</p>
          <button onClick={() => setCount(count + 1)}>Increment</button>
          <button onClick={() => setCount(0)}>Reset</button>
        </div>
      );
    }
    
    // Main app component
    function App() {
      const [browserInfo, setBrowserInfo] = React.useState(null);
      const [consoleOutput, setConsoleOutput] = React.useState([]);
      
      // Get browser information
      React.useEffect(() => {
        const info = {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language,
          cookiesEnabled: navigator.cookieEnabled,
          screenSize: `${window.innerWidth}x${window.innerHeight}`,
          pixelRatio: window.devicePixelRatio
        };
        setBrowserInfo(info);
        
        // Override console methods to capture output
        const originalConsole = {
          log: console.log,
          error: console.error,
          warn: console.warn
        };
        
        console.log = function(...args) {
          originalConsole.log(...args);
          setConsoleOutput(prev => [...prev, { type: 'log', content: args.join(' ') }]);
        };
        
        console.error = function(...args) {
          originalConsole.error(...args);
          setConsoleOutput(prev => [...prev, { type: 'error', content: args.join(' ') }]);
        };
        
        console.warn = function(...args) {
          originalConsole.warn(...args);
          setConsoleOutput(prev => [...prev, { type: 'warn', content: args.join(' ') }]);
        };
        
        console.log('React standalone test initialized');
        
        // Cleanup
        return () => {
          console.log = originalConsole.log;
          console.error = originalConsole.error;
          console.warn = originalConsole.warn;
        };
      }, []);
      
      // Run tests
      const runTests = () => {
        console.log('Running tests...');
        
        // Test localStorage
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          console.log('✅ localStorage is working');
        } catch (e) {
          console.error('❌ localStorage error:', e.message);
        }
        
        // Test sessionStorage
        try {
          sessionStorage.setItem('test', 'test');
          sessionStorage.removeItem('test');
          console.log('✅ sessionStorage is working');
        } catch (e) {
          console.error('❌ sessionStorage error:', e.message);
        }
        
        // Test fetch API
        try {
          fetch('/')
            .then(() => console.log('✅ fetch API is working'))
            .catch(e => console.error('❌ fetch API error:', e.message));
        } catch (e) {
          console.error('❌ fetch API error:', e.message);
        }
        
        console.log('Tests completed');
      };
      
      // Clear console output
      const clearConsole = () => {
        setConsoleOutput([]);
      };
      
      return (
        <div>
          <h1>Sabone React Test</h1>
          <p>This is a standalone React application to test if React is working correctly in your browser.</p>
          
          <div className="section">
            <h2>React Component Test</h2>
            <p>This counter component tests if React state and events are working:</p>
            <Counter />
          </div>
          
          <div className="section">
            <h2>Browser Information</h2>
            {browserInfo && (
              <pre>
                {Object.entries(browserInfo).map(([key, value]) => (
                  <div key={key}><strong>{key}:</strong> {value}</div>
                ))}
              </pre>
            )}
          </div>
          
          <div className="section">
            <h2>Tests</h2>
            <button onClick={runTests}>Run Tests</button>
          </div>
          
          <div className="section">
            <h2>Console Output</h2>
            <button onClick={clearConsole}>Clear Console</button>
            <pre style={{ maxHeight: '200px', overflow: 'auto' }}>
              {consoleOutput.map((entry, index) => (
                <div key={index} style={{ color: entry.type === 'error' ? '#ff6b6b' : entry.type === 'warn' ? '#ffdd67' : '#6bff6b' }}>
                  [{entry.type.toUpperCase()}] {entry.content}
                </div>
              ))}
            </pre>
          </div>
          
          <div className="section">
            <h2>Troubleshooting</h2>
            <p>If this page works but your Sabone app doesn't, the issue is likely with:</p>
            <ol>
              <li>Your Vite configuration</li>
              <li>Missing or incorrect environment variables</li>
              <li>Auth0 integration issues</li>
              <li>Context provider errors</li>
              <li>Circular dependencies in your imports</li>
            </ol>
          </div>
        </div>
      );
    }
    
    // Render the App
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(<App />);
  </script>
</body>
</html>
