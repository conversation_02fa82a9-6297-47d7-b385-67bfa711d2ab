// Import required modules
import express from 'express';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Initialize environment variables
dotenv.config();

const router = express.Router();

// PayPal API URLs
const PAYPAL_API_BASE = process.env.VITE_PAYPAL_SANDBOX === 'true' 
  ? 'https://api-m.sandbox.paypal.com' 
  : 'https://api-m.paypal.com';

// PayPal credentials
const PAYPAL_CLIENT_ID = process.env.VITE_PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.VITE_PAYPAL_CLIENT_SECRET;

/**
 * Get PayPal access token
 * 
 * @returns {Promise<string>} Access token
 */
const getPayPalAccessToken = async () => {
  try {
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');
    
    const response = await fetch(`${PAYPAL_API_BASE}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`
      },
      body: 'grant_type=client_credentials'
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error_description || 'Failed to get PayPal access token');
    }
    
    return data.access_token;
  } catch (error) {
    console.error('Error getting PayPal access token:', error);
    throw error;
  }
};

/**
 * Create a PayPal order
 * 
 * This endpoint creates a PayPal order with the specified amount and currency.
 * 
 * @route POST /api/create-paypal-order
 * @param {number} amount - The amount to charge
 * @param {string} currency - The currency to use (default: USD)
 * @returns {Object} The PayPal order ID and details
 */
router.post('/create-paypal-order', async (req, res) => {
  try {
    const { amount, currency = 'USD' } = req.body;

    // Validate the amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount. Amount must be greater than 0.'
      });
    }

    // Get PayPal access token
    const accessToken = await getPayPalAccessToken();

    // Create PayPal order
    const response = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: currency,
              value: amount.toFixed(2)
            },
            description: 'Sabone luxury soaps and shampoos'
          }
        ],
        application_context: {
          brand_name: 'Sabone',
          landing_page: 'BILLING',
          user_action: 'PAY_NOW',
          return_url: `${req.headers.origin}/checkout/success`,
          cancel_url: `${req.headers.origin}/checkout/cancel`
        }
      })
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('PayPal API error:', data);
      return res.status(response.status).json({
        error: data.message || 'Failed to create PayPal order'
      });
    }

    // Return the order ID and details to the client
    res.json({
      orderId: data.id,
      status: data.status,
      links: data.links
    });
  } catch (error) {
    console.error('Error creating PayPal order:', error);
    res.status(500).json({
      error: 'Failed to create PayPal order'
    });
  }
});

/**
 * Capture a PayPal payment
 * 
 * This endpoint captures a payment for an approved PayPal order.
 * 
 * @route POST /api/capture-paypal-payment
 * @param {string} orderId - The PayPal order ID to capture
 * @returns {Object} The capture details
 */
router.post('/capture-paypal-payment', async (req, res) => {
  try {
    const { orderId } = req.body;

    if (!orderId) {
      return res.status(400).json({
        error: 'Order ID is required'
      });
    }

    // Get PayPal access token
    const accessToken = await getPayPalAccessToken();

    // Capture the payment
    const response = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('PayPal capture error:', data);
      return res.status(response.status).json({
        error: data.message || 'Failed to capture PayPal payment'
      });
    }

    // Return the capture details to the client
    res.json({
      captureId: data.purchase_units[0].payments.captures[0].id,
      status: data.status,
      payerId: data.payer.payer_id,
      paymentId: data.purchase_units[0].payments.captures[0].id
    });
  } catch (error) {
    console.error('Error capturing PayPal payment:', error);
    res.status(500).json({
      error: 'Failed to capture PayPal payment'
    });
  }
});

export default router;
