import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Filter, X, Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export type ProductFilterOption = {
  id: string;
  label: string;
  checked: boolean;
};

export type ProductFilterCategory = {
  id: string;
  name: string;
  options: ProductFilterOption[];
  type: "checkbox" | "radio";
};

interface MobileProductFilterProps {
  categories: ProductFilterCategory[];
  onFilterChange: (categoryId: string, optionId: string, checked: boolean) => void;
  onClearFilters: () => void;
  activeFilterCount: number;
}

const MobileProductFilter = ({
  categories,
  onFilterChange,
  onClearFilters,
  activeFilterCount,
}: MobileProductFilterProps) => {
  const [open, setOpen] = useState(false);
  const [localCategories, setLocalCategories] = useState<ProductFilterCategory[]>(categories);

  // Update local state when props change
  useEffect(() => {
    setLocalCategories(categories);
  }, [categories]);

  // Apply filters and close sheet
  const handleApplyFilters = () => {
    // Apply any pending changes
    setOpen(false);
  };

  // Handle checkbox/radio changes
  const handleOptionChange = (categoryId: string, optionId: string, checked: boolean) => {
    // Update local state first for immediate UI feedback
    setLocalCategories(prevCategories => 
      prevCategories.map(category => {
        if (category.id === categoryId) {
          // For radio buttons, uncheck all other options
          if (category.type === "radio" && checked) {
            return {
              ...category,
              options: category.options.map(option => ({
                ...option,
                checked: option.id === optionId
              }))
            };
          }
          
          // For checkboxes, just toggle the selected option
          return {
            ...category,
            options: category.options.map(option => 
              option.id === optionId ? { ...option, checked } : option
            )
          };
        }
        return category;
      })
    );
    
    // Call the parent handler
    onFilterChange(categoryId, optionId, checked);
  };

  return (
    <div className="md:hidden">
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className="bg-sabone-dark-olive/60 border-sabone-gold/20 text-sabone-cream hover:bg-sabone-dark-olive hover:text-sabone-gold-accent touch-feedback"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filter
            {activeFilterCount > 0 && (
              <Badge className="ml-2 bg-sabone-gold text-sabone-charcoal-deep h-5 w-5 flex items-center justify-center p-0 text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent 
          side="bottom" 
          className="bg-sabone-charcoal-deep/95 border-sabone-gold/20 backdrop-blur-[5px] h-[80vh] rounded-t-xl"
        >
          <SheetHeader className="flex flex-row items-center justify-between">
            <SheetTitle className="text-sabone-gold-accent font-playfair text-xl">Filter Products</SheetTitle>
            <div className="flex space-x-2">
              {activeFilterCount > 0 && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={onClearFilters}
                  className="text-sabone-cream hover:text-sabone-gold-accent border-sabone-gold/20"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              )}
              <Button 
                size="sm" 
                onClick={handleApplyFilters}
                className="bg-sabone-gold text-sabone-charcoal-deep hover:bg-sabone-gold-rich"
              >
                <Check className="h-4 w-4 mr-1" />
                Apply
              </Button>
            </div>
          </SheetHeader>
          
          <div className="mt-6 overflow-y-auto max-h-[calc(80vh-80px)] pb-safe">
            {localCategories.map((category) => (
              <div key={category.id} className="mb-6">
                <h3 className="text-sabone-gold font-medium mb-3">{category.name}</h3>
                
                {category.type === "checkbox" ? (
                  <div className="space-y-3">
                    {category.options.map((option) => (
                      <div key={option.id} className="flex items-center">
                        <Checkbox
                          id={`${category.id}-${option.id}`}
                          checked={option.checked}
                          onCheckedChange={(checked) => 
                            handleOptionChange(category.id, option.id, checked === true)
                          }
                          className="border-sabone-gold/40 data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal-deep"
                        />
                        <Label
                          htmlFor={`${category.id}-${option.id}`}
                          className="ml-2 text-sabone-cream"
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <RadioGroup
                    defaultValue={category.options.find(o => o.checked)?.id}
                    className="space-y-3"
                  >
                    {category.options.map((option) => (
                      <div key={option.id} className="flex items-center">
                        <RadioGroupItem
                          id={`${category.id}-${option.id}`}
                          value={option.id}
                          checked={option.checked}
                          onClick={() => 
                            handleOptionChange(category.id, option.id, true)
                          }
                          className="border-sabone-gold/40 text-sabone-gold"
                        />
                        <Label
                          htmlFor={`${category.id}-${option.id}`}
                          className="ml-2 text-sabone-cream"
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                )}
                
                <Separator className="mt-4 bg-sabone-gold/20" />
              </div>
            ))}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default MobileProductFilter;
