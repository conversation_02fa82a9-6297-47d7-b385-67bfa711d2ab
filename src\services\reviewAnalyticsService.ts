import { Review, ReviewAnalytics, ReviewSentiment, ModerationFlag } from '@/types/review';
import { logger } from '@/utils/logger';

const ANALYTICS_CACHE_KEY = 'sabone-review-analytics';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface CachedAnalytics {
  data: ReviewAnalytics;
  timestamp: number;
}

/**
 * Simple sentiment analysis based on keywords and patterns
 */
export const analyzeSentiment = (text: string): ReviewSentiment => {
  const positiveWords = [
    'amazing', 'excellent', 'fantastic', 'great', 'wonderful', 'perfect', 'love', 'best',
    'awesome', 'outstanding', 'brilliant', 'superb', 'incredible', 'magnificent', 'beautiful',
    'smooth', 'soft', 'gentle', 'effective', 'natural', 'organic', 'fresh', 'clean'
  ];

  const negativeWords = [
    'terrible', 'awful', 'horrible', 'bad', 'worst', 'hate', 'disgusting', 'useless',
    'disappointing', 'poor', 'cheap', 'fake', 'waste', 'regret', 'broken', 'damaged',
    'harsh', 'rough', 'irritating', 'allergic', 'reaction', 'dry', 'greasy', 'smell'
  ];

  const words = text.toLowerCase().split(/\s+/);
  let positiveScore = 0;
  let negativeScore = 0;

  words.forEach(word => {
    if (positiveWords.includes(word)) positiveScore++;
    if (negativeWords.includes(word)) negativeScore++;
  });

  const totalScore = positiveScore - negativeScore;
  const totalWords = positiveScore + negativeScore;
  
  // Normalize score to -1 to 1 range
  const normalizedScore = totalWords > 0 ? totalScore / Math.max(totalWords, 5) : 0;
  
  let label: 'positive' | 'neutral' | 'negative';
  if (normalizedScore > 0.2) label = 'positive';
  else if (normalizedScore < -0.2) label = 'negative';
  else label = 'neutral';

  const confidence = Math.min(Math.abs(normalizedScore) + 0.3, 1);

  return {
    score: Math.max(-1, Math.min(1, normalizedScore)),
    label,
    confidence
  };
};

/**
 * Content moderation using simple pattern matching
 */
export const moderateContent = (text: string): ModerationFlag[] => {
  const flags: ModerationFlag[] = [];

  // Spam detection
  const spamPatterns = [
    /(.)\1{4,}/g, // Repeated characters
    /\b(buy|click|visit|www\.|http)/gi, // Promotional content
    /\b(free|discount|sale|offer)\b/gi // Sales language
  ];

  spamPatterns.forEach(pattern => {
    if (pattern.test(text)) {
      flags.push({
        type: 'spam',
        confidence: 0.7,
        reason: 'Contains spam-like patterns'
      });
    }
  });

  // Inappropriate content detection
  const inappropriatePatterns = [
    /\b(damn|hell|stupid|idiot)\b/gi, // Mild profanity
    /[A-Z]{5,}/g // Excessive caps
  ];

  inappropriatePatterns.forEach(pattern => {
    if (pattern.test(text)) {
      flags.push({
        type: 'inappropriate',
        confidence: 0.6,
        reason: 'Contains potentially inappropriate language'
      });
    }
  });

  // Fake review detection
  if (text.length < 20) {
    flags.push({
      type: 'fake',
      confidence: 0.5,
      reason: 'Review is suspiciously short'
    });
  }

  return flags;
};

/**
 * Calculate comprehensive review analytics
 */
export const calculateReviewAnalytics = (reviews: Review[]): ReviewAnalytics => {
  const now = new Date();
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

  // Basic metrics
  const totalReviews = reviews.length;
  const averageRating = totalReviews > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
    : 0;

  // Monthly metrics
  const reviewsThisMonth = reviews.filter(
    review => new Date(review.createdAt) >= thisMonth
  ).length;

  const reviewsLastMonth = reviews.filter(
    review => {
      const date = new Date(review.createdAt);
      return date >= lastMonth && date < thisMonth;
    }
  ).length;

  const reviewGrowthRate = reviewsLastMonth > 0 
    ? ((reviewsThisMonth - reviewsLastMonth) / reviewsLastMonth) * 100 
    : 0;

  // Product ratings
  const productRatings = new Map<string, { total: number; count: number; name: string }>();
  
  reviews.forEach(review => {
    const current = productRatings.get(review.productId) || { total: 0, count: 0, name: 'Unknown Product' };
    productRatings.set(review.productId, {
      total: current.total + review.rating,
      count: current.count + 1,
      name: current.name // This would be populated from product data in real implementation
    });
  });

  const topRatedProducts = Array.from(productRatings.entries())
    .map(([productId, data]) => ({
      productId,
      productName: data.name,
      averageRating: data.total / data.count,
      reviewCount: data.count
    }))
    .sort((a, b) => b.averageRating - a.averageRating)
    .slice(0, 10);

  // Sentiment analysis
  const sentimentData = reviews.map(review => ({
    date: review.createdAt.split('T')[0],
    sentiment: review.sentiment || analyzeSentiment(review.content)
  }));

  // Group by date for trends
  const sentimentByDate = new Map<string, { positive: number; neutral: number; negative: number }>();
  
  sentimentData.forEach(({ date, sentiment }) => {
    const current = sentimentByDate.get(date) || { positive: 0, neutral: 0, negative: 0 };
    current[sentiment.label]++;
    sentimentByDate.set(date, current);
  });

  const sentimentTrends = Array.from(sentimentByDate.entries())
    .map(([date, counts]) => ({ date, ...counts }))
    .sort((a, b) => a.date.localeCompare(b.date))
    .slice(-30); // Last 30 days

  // Moderation stats
  const pendingReviews = reviews.filter(review => review.status === 'pending').length;
  const flaggedReviews = reviews.filter(review => 
    review.moderationFlags && review.moderationFlags.length > 0
  ).length;
  const approvedReviews = reviews.filter(review => review.status === 'approved').length;
  const approvalRate = totalReviews > 0 ? (approvedReviews / totalReviews) * 100 : 0;

  return {
    totalReviews,
    averageRating,
    reviewsThisMonth,
    reviewGrowthRate,
    topRatedProducts,
    sentimentTrends,
    moderationStats: {
      pendingReviews,
      flaggedReviews,
      approvalRate
    }
  };
};

/**
 * Get cached analytics or calculate new ones
 */
export const getReviewAnalytics = (reviews: Review[]): ReviewAnalytics => {
  try {
    const cached = localStorage.getItem(ANALYTICS_CACHE_KEY);
    if (cached) {
      const { data, timestamp }: CachedAnalytics = JSON.parse(cached);
      if (Date.now() - timestamp < CACHE_DURATION) {
        return data;
      }
    }

    const analytics = calculateReviewAnalytics(reviews);
    
    // Cache the results
    const cacheData: CachedAnalytics = {
      data: analytics,
      timestamp: Date.now()
    };
    localStorage.setItem(ANALYTICS_CACHE_KEY, JSON.stringify(cacheData));

    logger.performance('review_analytics_calculated', {
      totalReviews: analytics.totalReviews,
      calculationTime: Date.now()
    });

    return analytics;
  } catch (error) {
    logger.error('Error calculating review analytics', error);
    // Return default analytics on error
    return {
      totalReviews: 0,
      averageRating: 0,
      reviewsThisMonth: 0,
      reviewGrowthRate: 0,
      topRatedProducts: [],
      sentimentTrends: [],
      moderationStats: {
        pendingReviews: 0,
        flaggedReviews: 0,
        approvalRate: 0
      }
    };
  }
};

/**
 * Clear analytics cache
 */
export const clearAnalyticsCache = (): void => {
  localStorage.removeItem(ANALYTICS_CACHE_KEY);
};

/**
 * Export analytics data to CSV
 */
export const exportAnalyticsToCSV = (analytics: ReviewAnalytics): string => {
  const csvRows = [
    ['Metric', 'Value'],
    ['Total Reviews', analytics.totalReviews.toString()],
    ['Average Rating', analytics.averageRating.toFixed(2)],
    ['Reviews This Month', analytics.reviewsThisMonth.toString()],
    ['Growth Rate (%)', analytics.reviewGrowthRate.toFixed(1)],
    ['Pending Reviews', analytics.moderationStats.pendingReviews.toString()],
    ['Flagged Reviews', analytics.moderationStats.flaggedReviews.toString()],
    ['Approval Rate (%)', analytics.moderationStats.approvalRate.toFixed(1)],
    [''],
    ['Top Rated Products', '', '', ''],
    ['Product ID', 'Product Name', 'Average Rating', 'Review Count'],
    ...analytics.topRatedProducts.map(product => [
      product.productId,
      product.productName,
      product.averageRating.toFixed(2),
      product.reviewCount.toString()
    ])
  ];

  return csvRows.map(row => row.join(',')).join('\n');
};
