// Add this to your main.tsx file temporarily for debugging
// This will help identify where the rendering process is failing

console.log('🔍 Starting Vite React app diagnostics...');

// Check if the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runDiagnostics);
} else {
  runDiagnostics();
}

function runDiagnostics() {
  console.log('🔍 DOM is ready, running diagnostics...');

  // Check if the root element exists
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    console.error('❌ Root element not found! Make sure your HTML has a div with id="root"');
    displayError('Root element not found');
    return;
  }
  console.log('✅ Root element found');

  // Check React and ReactDOM
  if (typeof React === 'undefined') {
    console.error('❌ React is not defined! Check if React is being imported correctly');
    displayError('React is not defined');
    return;
  }
  console.log('✅ React is defined');

  if (typeof ReactDOM === 'undefined') {
    console.error('❌ ReactDOM is not defined! Check if ReactDOM is being imported correctly');
    displayError('ReactDOM is not defined');
    return;
  }
  console.log('✅ ReactDOM is defined');

  // Check for Auth0 if it's being used
  if (typeof window.Auth0 === 'undefined' && document.querySelector('script[src*="auth0"]')) {
    console.warn('⚠️ Auth0 script is included but Auth0 is not defined');
  }

  // Check for environment variables
  const envVars = [
    'VITE_AUTH0_DOMAIN',
    'VITE_AUTH0_CLIENT_ID',
    'VITE_SKIP_AUTH'
  ];

  console.log('🔍 Checking environment variables...');
  envVars.forEach(varName => {
    const value = import.meta.env[varName];
    if (value) {
      console.log(`✅ ${varName} is defined: ${value.substring(0, 3)}...`);
    } else {
      console.warn(`⚠️ ${varName} is not defined`);
    }
  });

  // Check for common errors
  console.log('🔍 Checking for common error patterns...');

  // 1. Check for missing CSS
  const linkElements = document.querySelectorAll('link[rel="stylesheet"]');
  if (linkElements.length === 0) {
    console.warn('⚠️ No stylesheet links found. CSS might be missing.');
  } else {
    console.log(`✅ Found ${linkElements.length} stylesheet links`);
  }

  // 2. Check for script errors
  const scriptElements = document.querySelectorAll('script');
  console.log(`✅ Found ${scriptElements.length} script elements`);

  // 3. Check for network errors
  if (window.performance) {
    const resources = window.performance.getEntriesByType('resource');
    const failedResources = resources.filter(r => r.responseStatus >= 400);

    if (failedResources.length > 0) {
      console.error(`❌ Found ${failedResources.length} failed resource requests:`);
      failedResources.forEach(r => console.error(`  - ${r.name}: ${r.responseStatus}`));
    } else {
      console.log('✅ No failed resource requests detected');
    }
  }

  // Try to render a simple component to test React
  try {
    console.log('🔍 Attempting to render a test React component...');

    // Check if document.body exists
    if (!document.body) {
      console.error('Cannot render test component: document.body is not available');
      return;
    }

    // Check if a test container already exists
    let container = document.getElementById('react-diagnostic-container');
    if (container) {
      // Clear existing container instead of creating a new one
      while (container.firstChild) {
        container.removeChild(container.firstChild);
      }
    } else {
      // Create a container for the test component
      container = document.createElement('div');
      container.id = 'react-diagnostic-container';
      document.body.appendChild(container);
    }

    // Create a simple test component
    const TestComponent = () => {
      return React.createElement('div', {
        style: {
          padding: '20px',
          backgroundColor: '#2a2a1f',
          color: '#e5dcc5',
          border: '1px solid #c6a870',
          borderRadius: '8px',
          position: 'fixed',
          top: '20px',
          right: '20px',
          zIndex: 9999,
          maxWidth: '300px'
        }
      }, [
        React.createElement('h3', {
          style: { color: '#c6a870', marginTop: 0 }
        }, 'React Diagnostic'),
        React.createElement('p', null, 'If you can see this, React rendering works!'),
        React.createElement('button', {
          onClick: () => {
            try {
              const container = document.getElementById('react-diagnostic-container');
              if (container && container.parentNode) {
                container.parentNode.removeChild(container);
              }
            } catch (error) {
              console.error('Error removing diagnostic container:', error);
            }
          },
          style: {
            backgroundColor: '#c6a870',
            color: '#1c1c1c',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer'
          }
        }, 'Close')
      ]);
    };

    // Render the test component
    try {
      const root = ReactDOM.createRoot(container);
      root.render(React.createElement(TestComponent));
      console.log('✅ Test component rendered successfully');
    } catch (error) {
      console.error('❌ Failed to render with createRoot:', error);
      // Fallback to legacy render method
      try {
        ReactDOM.render(React.createElement(TestComponent), container);
        console.log('✅ Test component rendered successfully with legacy render');
      } catch (legacyError) {
        console.error('❌ Failed to render with legacy method:', legacyError);
        throw error; // Re-throw the original error
      }
    }
  } catch (error) {
    console.error('❌ Failed to render test component:', error);
    displayError(`React rendering error: ${error.message}`);
  }

  console.log('🔍 Diagnostics complete');
}

function displayError(message) {
  try {
    // Check if document.body exists before manipulating it
    if (!document.body) {
      console.error('Cannot display error: document.body is not available');
      return;
    }

    // Check if an error container already exists
    const existingContainer = document.getElementById('vite-diagnostics-error');
    if (existingContainer) {
      // Update existing container instead of creating a new one
      existingContainer.innerHTML = `
        <h2>React App Error</h2>
        <p>${message}</p>
        <p>Check the browser console for more details.</p>
      `;
      return;
    }

    // Create an error message element
    const errorContainer = document.createElement('div');
    errorContainer.id = 'vite-diagnostics-error';
    errorContainer.style.position = 'fixed';
    errorContainer.style.top = '0';
    errorContainer.style.left = '0';
    errorContainer.style.width = '100%';
    errorContainer.style.backgroundColor = '#ff6b6b';
    errorContainer.style.color = 'white';
    errorContainer.style.padding = '20px';
    errorContainer.style.textAlign = 'center';
    errorContainer.style.zIndex = '9999';
    errorContainer.style.fontFamily = 'Arial, sans-serif';

    errorContainer.innerHTML = `
      <h2>React App Error</h2>
      <p>${message}</p>
      <p>Check the browser console for more details.</p>
    `;

    document.body.appendChild(errorContainer);
  } catch (error) {
    console.error('Error displaying error message:', error);
  }
}
