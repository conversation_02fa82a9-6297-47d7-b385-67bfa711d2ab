<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sabone | Luxury Natural Soaps & Shampoos</title>
    <meta name="description" content="Discover <PERSON><PERSON>'s handcrafted natural soaps and shampoos inspired by ancient Arabic rituals." />
    <meta name="author" content="Sabone" />
    <link rel="icon" type="image/png" href="/favicon.ico.png" />

    <meta property="og:title" content="sabone-ritual-storefront" />
    <meta property="og:description" content="Lovable Generated Project" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

    <!-- Preload critical images only -->
    <link rel="preload" href="/images/logo.png" as="image" />
    <link rel="preload" href="/images/hero-background.jpg" as="image" />

    <!-- Add meta theme color -->
    <meta name="theme-color" content="#1c1c1c" />
  </head>

  <body>
    <div id="root">
      <!-- Loading state that will be replaced by React -->
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background-color: #1c1c1c; color: #e5dcc5;">
        <div style="text-align: center;">
          <h1 style="color: #c6a870; font-family: 'Playfair Display', serif;">Sabone</h1>
          <p>Loading application...</p>
        </div>
      </div>
    </div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>

    <!-- Add React directly for debugging -->
    <script>
      window.addEventListener('error', function(e) {
        console.error('Global error caught:', e.error || e.message);
        document.getElementById('root').innerHTML += `
          <div style="position: fixed; top: 0; left: 0; right: 0; background-color: #ff6b6b; color: white; padding: 10px; text-align: center;">
            Error: ${e.error ? e.error.message : e.message}
          </div>
        `;
      });
    </script>

    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
