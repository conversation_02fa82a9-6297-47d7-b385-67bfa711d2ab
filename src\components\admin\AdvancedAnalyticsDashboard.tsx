import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { useProducts } from '@/contexts/ProductContext';
import { useReviews } from '@/contexts/ReviewContext';
import {
  businessAnalyticsService,
  BusinessAnalytics
} from '@/services/businessAnalyticsService';
import { toast } from 'sonner';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area
} from 'recharts';
import {
  TrendingUp,
  DollarSign,
  Users,
  ShoppingCart,
  Eye,
  MousePointer,
  Target,
  Download,
  Refresh<PERSON><PERSON>,
  BarChart3,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hart<PERSON><PERSON>,
  Award,
  AlertTriangle,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

interface AdvancedAnalyticsDashboardProps {
  className?: string;
}

const COLORS = ['#C6A870', '#8A7A5A', '#6B5B3D', '#4A3F2A', '#2A2A1F'];

const AdvancedAnalyticsDashboard: React.FC<AdvancedAnalyticsDashboardProps> = ({ className = '' }) => {
  const { products } = useProducts();
  const { reviews } = useReviews();
  const [analytics, setAnalytics] = useState<BusinessAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dateRange, setDateRange] = useState('30d');

  useEffect(() => {
    loadAnalytics();
  }, [products, reviews, dateRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const analyticsData = businessAnalyticsService.getCachedBusinessAnalytics(products, reviews);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Failed to load analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const refreshAnalytics = async () => {
    try {
      setRefreshing(true);
      businessAnalyticsService.clearAnalyticsCache();
      const analyticsData = businessAnalyticsService.getBusinessAnalytics(products, reviews);
      setAnalytics(analyticsData);
      toast.success('Analytics data refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
      toast.error('Failed to refresh analytics data');
    } finally {
      setRefreshing(false);
    }
  };

  const exportAnalytics = () => {
    if (!analytics) return;

    try {
      const csvData = businessAnalyticsService.exportAnalyticsToCSV(analytics);
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `sabone-analytics-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Analytics data exported successfully');
    } catch (error) {
      console.error('Failed to export analytics:', error);
      toast.error('Failed to export analytics data');
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-64 bg-sabone-gold/10" />
            <Skeleton className="h-4 w-96 bg-sabone-gold/10 mt-2" />
          </div>
          <Skeleton className="h-10 w-32 bg-sabone-gold/10" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6">
                <Skeleton className="h-6 w-24 bg-sabone-gold/10" />
                <Skeleton className="h-8 w-32 bg-sabone-gold/10 mt-2" />
                <Skeleton className="h-4 w-20 bg-sabone-gold/10 mt-2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-sabone-gold/40 mx-auto mb-4" />
          <p className="text-sabone-cream/60">Failed to load analytics data</p>
          <Button
            onClick={loadAnalytics}
            variant="outline"
            className="mt-4 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-playfair font-bold text-sabone-gold">
            Advanced Analytics Dashboard
          </h2>
          <p className="text-sabone-cream/60 mt-1">
            Comprehensive business intelligence and performance insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="bg-sabone-charcoal/30 border border-sabone-gold/20 text-sabone-cream rounded px-3 py-2"
            aria-label="Select date range for analytics"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <Button
            onClick={refreshAnalytics}
            disabled={refreshing}
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={exportAnalytics}
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sabone-cream/60 text-sm">Total Revenue</p>
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatCurrency(analytics.salesMetrics.totalRevenue)}
                </p>
                <div className="flex items-center mt-1">
                  {analytics.salesMetrics.revenueGrowth >= 0 ? (
                    <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analytics.salesMetrics.revenueGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {formatPercentage(Math.abs(analytics.salesMetrics.revenueGrowth))}
                  </span>
                </div>
              </div>
              <DollarSign className="h-8 w-8 text-sabone-gold/40" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sabone-cream/60 text-sm">Total Sessions</p>
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatNumber(analytics.userBehaviorMetrics.totalSessions)}
                </p>
                <div className="flex items-center mt-1">
                  <Eye className="h-4 w-4 text-sabone-gold/60 mr-1" />
                  <span className="text-sm text-sabone-cream/60">
                    {formatNumber(analytics.userBehaviorMetrics.pageViewsPerSession)} pages/session
                  </span>
                </div>
              </div>
              <Users className="h-8 w-8 text-sabone-gold/40" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sabone-cream/60 text-sm">Conversion Rate</p>
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatPercentage(analytics.conversionFunnelMetrics.overallConversionRate)}
                </p>
                <div className="flex items-center mt-1">
                  <Target className="h-4 w-4 text-sabone-gold/60 mr-1" />
                  <span className="text-sm text-sabone-cream/60">
                    {analytics.conversionFunnelMetrics.averageTimeToConvert}d avg time
                  </span>
                </div>
              </div>
              <MousePointer className="h-8 w-8 text-sabone-gold/40" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sabone-cream/60 text-sm">Average Order Value</p>
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatCurrency(analytics.salesMetrics.averageOrderValue)}
                </p>
                <div className="flex items-center mt-1">
                  <ShoppingCart className="h-4 w-4 text-sabone-gold/60 mr-1" />
                  <span className="text-sm text-sabone-cream/60">
                    {formatNumber(analytics.salesMetrics.totalOrders)} orders
                  </span>
                </div>
              </div>
              <BarChart3 className="h-8 w-8 text-sabone-gold/40" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="sales" className="space-y-6">
        <TabsList className="bg-sabone-dark-olive/60 border border-sabone-gold/20">
          <TabsTrigger value="sales" className="data-[state=active]:bg-sabone-gold/20 data-[state=active]:text-sabone-gold">
            Sales Analytics
          </TabsTrigger>
          <TabsTrigger value="behavior" className="data-[state=active]:bg-sabone-gold/20 data-[state=active]:text-sabone-gold">
            User Behavior
          </TabsTrigger>
          <TabsTrigger value="conversion" className="data-[state=active]:bg-sabone-gold/20 data-[state=active]:text-sabone-gold">
            Conversion Funnel
          </TabsTrigger>
          <TabsTrigger value="kpis" className="data-[state=active]:bg-sabone-gold/20 data-[state=active]:text-sabone-gold">
            Key Metrics
          </TabsTrigger>
        </TabsList>

        {/* Sales Analytics Tab */}
        <TabsContent value="sales" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trend Chart */}
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Revenue Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={analytics.salesMetrics.salesByTimeframe}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                      <XAxis
                        dataKey="period"
                        stroke="#E5DCC5"
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis
                        stroke="#E5DCC5"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#2A2A1F',
                          borderColor: '#C6A870',
                          color: '#E5DCC5'
                        }}
                        formatter={(value: any) => [formatCurrency(value), 'Revenue']}
                      />
                      <Area
                        type="monotone"
                        dataKey="revenue"
                        stroke="#C6A870"
                        fill="#C6A870"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Sales by Category */}
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center">
                  <PieChartIcon className="h-5 w-5 mr-2" />
                  Sales by Category
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analytics.salesMetrics.salesByCategory}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="revenue"
                        label={({ category, percentage }) => `${category} ${percentage?.toFixed(0)}%`}
                      >
                        {analytics.salesMetrics.salesByCategory.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#2A2A1F',
                          borderColor: '#C6A870',
                          color: '#E5DCC5'
                        }}
                        formatter={(value: any) => [formatCurrency(value), 'Revenue']}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Selling Products */}
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Top Selling Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.salesMetrics.topSellingProducts.map((product, index) => (
                  <div key={product.productId} className="flex items-center justify-between p-4 bg-sabone-charcoal/30 rounded">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-3 border-sabone-gold/30 text-sabone-gold">
                        #{index + 1}
                      </Badge>
                      <div>
                        <p className="font-medium text-sabone-cream">{product.productName}</p>
                        <p className="text-sm text-sabone-cream/60">
                          {formatNumber(product.unitsSold)} units sold
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sabone-gold">{formatCurrency(product.revenue)}</p>
                      <p className="text-sm text-sabone-cream/60">
                        {formatPercentage(product.conversionRate)} conversion
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Behavior Tab */}
        <TabsContent value="behavior" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Pages */}
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center">
                  <Eye className="h-5 w-5 mr-2" />
                  Top Pages
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.userBehaviorMetrics.topPages.map((page) => (
                    <div key={page.page} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sabone-cream">{page.page}</p>
                        <p className="text-sm text-sabone-cream/60">
                          {formatNumber(page.views)} views • {page.averageTimeOnPage}s avg time
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sabone-gold">{formatPercentage(page.exitRate)} exit rate</p>
                        <Progress
                          value={page.exitRate}
                          className="w-20 h-2 mt-1"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Device Breakdown */}
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Device Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.userBehaviorMetrics.deviceBreakdown}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                      <XAxis
                        dataKey="deviceType"
                        stroke="#E5DCC5"
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis
                        stroke="#E5DCC5"
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#2A2A1F',
                          borderColor: '#C6A870',
                          color: '#E5DCC5'
                        }}
                        formatter={(value: any) => [formatNumber(value), 'Sessions']}
                      />
                      <Bar dataKey="sessions" fill="#C6A870" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* User Flow */}
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold flex items-center">
                <MousePointer className="h-5 w-5 mr-2" />
                User Flow Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.userBehaviorMetrics.userFlow.map((flow, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-sabone-charcoal/30 rounded">
                    <div className="flex items-center">
                      <span className="text-sabone-cream">{flow.fromPage}</span>
                      <ArrowUpRight className="h-4 w-4 text-sabone-gold/60 mx-2" />
                      <span className="text-sabone-cream">{flow.toPage}</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sabone-gold">{formatNumber(flow.count)} users</p>
                      <p className="text-sm text-sabone-cream/60">
                        {formatPercentage(flow.conversionRate)} conversion
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Conversion Funnel Tab */}
        <TabsContent value="conversion" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Conversion Funnel Chart */}
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Conversion Funnel
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.conversionFunnelMetrics.stages.map((stage, index) => (
                    <div key={stage.stage} className="relative">
                      <div className="flex items-center justify-between p-4 bg-sabone-charcoal/30 rounded">
                        <div className="flex items-center">
                          <Badge variant="outline" className="mr-3 border-sabone-gold/30 text-sabone-gold">
                            {index + 1}
                          </Badge>
                          <div>
                            <p className="font-medium text-sabone-cream">{stage.stage}</p>
                            <p className="text-sm text-sabone-cream/60">
                              {formatNumber(stage.users)} users
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sabone-gold font-bold">
                            {formatPercentage(stage.conversionRate)}
                          </p>
                          {stage.dropOffRate > 0 && (
                            <p className="text-sm text-red-400">
                              -{formatPercentage(stage.dropOffRate)} drop-off
                            </p>
                          )}
                        </div>
                      </div>
                      <Progress
                        value={stage.conversionRate}
                        className="mt-2 h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Drop-off Analysis */}
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Drop-off Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.conversionFunnelMetrics.dropOffPoints.map((dropOff) => (
                    <div key={dropOff.stage} className="p-4 bg-sabone-charcoal/30 rounded">
                      <div className="flex items-center justify-between mb-2">
                        <p className="font-medium text-sabone-cream">{dropOff.stage}</p>
                        <Badge variant="destructive" className="bg-red-500/20 text-red-400 border-red-500/30">
                          {formatPercentage(dropOff.dropOffRate)} drop-off
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-sabone-cream/60 mb-2">Common reasons:</p>
                        {dropOff.commonReasons.map((reason, reasonIndex) => (
                          <div key={reasonIndex} className="flex items-center text-sm text-sabone-cream/80">
                            <div className="w-1 h-1 bg-sabone-gold rounded-full mr-2"></div>
                            {reason}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Conversion Metrics Summary */}
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold flex items-center">
                <Zap className="h-5 w-5 mr-2" />
                Conversion Metrics Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-3xl font-bold text-sabone-gold">
                    {formatPercentage(analytics.conversionFunnelMetrics.overallConversionRate)}
                  </p>
                  <p className="text-sabone-cream/60">Overall Conversion Rate</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-sabone-gold">
                    {analytics.conversionFunnelMetrics.averageTimeToConvert}d
                  </p>
                  <p className="text-sabone-cream/60">Average Time to Convert</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-sabone-gold">
                    {analytics.conversionFunnelMetrics.stages[0]?.users || 0}
                  </p>
                  <p className="text-sabone-cream/60">Total Funnel Entries</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Key Metrics Tab */}
        <TabsContent value="kpis" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <DollarSign className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatCurrency(analytics.kpis.customerLifetimeValue)}
                </p>
                <p className="text-sabone-cream/60 text-sm">Customer Lifetime Value</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <Users className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatCurrency(analytics.kpis.customerAcquisitionCost)}
                </p>
                <p className="text-sabone-cream/60 text-sm">Customer Acquisition Cost</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <RefreshCw className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatPercentage(analytics.kpis.returnCustomerRate)}
                </p>
                <p className="text-sabone-cream/60 text-sm">Return Customer Rate</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <Activity className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {analytics.kpis.averageOrderFrequency.toFixed(1)}
                </p>
                <p className="text-sabone-cream/60 text-sm">Average Order Frequency</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <Package className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {analytics.kpis.inventoryTurnover.toFixed(1)}x
                </p>
                <p className="text-sabone-cream/60 text-sm">Inventory Turnover</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <TrendingUp className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {formatPercentage(analytics.kpis.grossMargin)}
                </p>
                <p className="text-sabone-cream/60 text-sm">Gross Margin</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <Award className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {analytics.kpis.netPromoterScore}
                </p>
                <p className="text-sabone-cream/60 text-sm">Net Promoter Score</p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6 text-center">
                <CheckCircle className="h-8 w-8 text-sabone-gold mx-auto mb-2" />
                <p className="text-2xl font-bold text-sabone-cream">
                  {analytics.kpis.customerSatisfactionScore.toFixed(1)}/5
                </p>
                <p className="text-sabone-cream/60 text-sm">Customer Satisfaction</p>
              </CardContent>
            </Card>
          </div>

          {/* Integrated Analytics Summary */}
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Integrated Analytics Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <h4 className="font-semibold text-sabone-cream">Sales Performance</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Total Revenue:</span>
                      <span className="text-sabone-cream">{formatCurrency(analytics.salesMetrics.totalRevenue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Growth Rate:</span>
                      <span className={analytics.salesMetrics.revenueGrowth >= 0 ? 'text-green-500' : 'text-red-500'}>
                        {formatPercentage(analytics.salesMetrics.revenueGrowth)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Total Orders:</span>
                      <span className="text-sabone-cream">{formatNumber(analytics.salesMetrics.totalOrders)}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-semibold text-sabone-cream">User Engagement</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Total Sessions:</span>
                      <span className="text-sabone-cream">{formatNumber(analytics.userBehaviorMetrics.totalSessions)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Bounce Rate:</span>
                      <span className="text-sabone-cream">{formatPercentage(analytics.userBehaviorMetrics.bounceRate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Avg Session Duration:</span>
                      <span className="text-sabone-cream">{analytics.userBehaviorMetrics.averageSessionDuration.toFixed(1)}m</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-semibold text-sabone-cream">Inventory & Reviews</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Total Products:</span>
                      <span className="text-sabone-cream">{analytics.inventoryMetrics.totalProducts}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Stock Value:</span>
                      <span className="text-sabone-cream">{formatCurrency(analytics.inventoryMetrics.totalStockValue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sabone-cream/60">Total Reviews:</span>
                      <span className="text-sabone-cream">{analytics.reviewMetrics.totalReviews}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedAnalyticsDashboard;