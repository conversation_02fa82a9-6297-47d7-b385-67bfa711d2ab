import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Activity, AlertTriangle, CheckCircle, Zap, Package, Clock, TrendingUp } from 'lucide-react';
import { trackWebVitals } from '@/hooks/usePerformance';
import { getBundleMetrics, getRouteStats } from '@/utils/bundleAnalyzer';

interface PerformanceMetrics {
  LCP?: number;
  FID?: number;
  CLS?: number;
  memoryUsage?: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: number;
}

const PerformanceTracker = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [bundleMetrics, setBundleMetrics] = useState<any[]>([]);
  const [bundleAlerts, setBundleAlerts] = useState<any[]>([]);
  const [routeStats, setRouteStats] = useState<any[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development mode
    if (import.meta.env.DEV) {
      trackWebVitals();

      // Listen for performance alerts
      const handlePerformanceAlert = (event: CustomEvent) => {
        const alert: PerformanceAlert = event.detail;
        setAlerts(prev => [...prev.slice(-9), alert]); // Keep last 10 alerts
      };

      window.addEventListener('performance-alert', handlePerformanceAlert as EventListener);

      // Listen for bundle alerts
      const handleBundleAlert = (event: CustomEvent) => {
        const alert = event.detail;
        setBundleAlerts(prev => [...prev.slice(-9), alert]);
      };

      window.addEventListener('bundle-alert', handleBundleAlert as EventListener);

      // Monitor memory usage
      const memoryInterval = setInterval(() => {
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          if (memory) {
            const percentage = Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100);
            setMetrics(prev => ({ ...prev, memoryUsage: percentage }));
          }
        }
      }, 10000);

      // Update bundle metrics periodically
      const bundleInterval = setInterval(() => {
        setBundleMetrics(getBundleMetrics());
        setRouteStats(getRouteStats());
      }, 5000);

      return () => {
        window.removeEventListener('performance-alert', handlePerformanceAlert as EventListener);
        window.removeEventListener('bundle-alert', handleBundleAlert as EventListener);
        clearInterval(memoryInterval);
        clearInterval(bundleInterval);
      };
    }
  }, []);

  const getMetricStatus = (metric: string, value: number) => {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
      memoryUsage: { good: 50, poor: 80 }
    };

    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return 'good';

    if (value > threshold.poor) return 'poor';
    if (value > threshold.good) return 'needs-improvement';
    return 'good';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs-improvement':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'poor':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-500';
      case 'needs-improvement':
        return 'bg-yellow-500';
      case 'poor':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatMetricValue = (metric: string, value: number) => {
    switch (metric) {
      case 'CLS':
        return value.toFixed(3);
      case 'memoryUsage':
        return `${value}%`;
      default:
        return `${value.toFixed(0)}ms`;
    }
  };

  if (!import.meta.env.DEV) return null;

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-sabone-charcoal-deep/90 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-charcoal-deep"
        >
          <Zap className="h-4 w-4 mr-2" />
          Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card className="bg-sabone-charcoal-deep/95 border-sabone-gold/30 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sabone-gold text-sm flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Performance Monitor
            </CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-sabone-cream hover:text-sabone-gold"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <Tabs defaultValue="vitals" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-sabone-charcoal/50">
              <TabsTrigger value="vitals" className="text-xs">Vitals</TabsTrigger>
              <TabsTrigger value="bundle" className="text-xs">Bundle</TabsTrigger>
              <TabsTrigger value="routes" className="text-xs">Routes</TabsTrigger>
            </TabsList>

            <TabsContent value="vitals" className="space-y-3 mt-3">
          {/* Core Web Vitals */}
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-sabone-cream uppercase tracking-wide">
              Core Web Vitals
            </h4>
            {Object.entries(metrics).map(([metric, value]) => {
              if (value === undefined) return null;
              const status = getMetricStatus(metric, value);
              return (
                <div key={metric} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(status)}
                    <span className="text-xs text-sabone-cream">{metric}</span>
                  </div>
                  <Badge
                    variant="outline"
                    className={`${getStatusColor(status)} text-white border-transparent text-xs`}
                  >
                    {formatMetricValue(metric, value)}
                  </Badge>
                </div>
              );
            })}
          </div>

          {/* Recent Alerts */}
          {alerts.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-sabone-cream uppercase tracking-wide">
                Recent Alerts
              </h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {alerts.slice(-5).map((alert, index) => (
                  <div
                    key={index}
                    className={`text-xs p-2 rounded ${
                      alert.type === 'error'
                        ? 'bg-red-500/20 text-red-200'
                        : 'bg-yellow-500/20 text-yellow-200'
                    }`}
                  >
                    {alert.message}
                  </div>
                ))}
              </div>
            </div>
          )}
            </TabsContent>

            <TabsContent value="bundle" className="space-y-3 mt-3">
              <div className="space-y-2">
                <h4 className="text-xs font-medium text-sabone-cream uppercase tracking-wide flex items-center">
                  <Package className="h-3 w-3 mr-1" />
                  Bundle Analysis
                </h4>
                {bundleMetrics.length > 0 ? (
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {bundleMetrics.slice(-5).map((metric, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span className="text-sabone-cream truncate">{metric.componentName}</span>
                        <Badge variant="outline" className="text-xs">
                          {metric.loadTime.toFixed(0)}ms
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-sabone-cream/60">No bundle metrics yet</p>
                )}
              </div>

              {bundleAlerts.length > 0 && (
                <div className="space-y-1 max-h-24 overflow-y-auto">
                  {bundleAlerts.slice(-3).map((alert, index) => (
                    <div
                      key={index}
                      className="text-xs p-2 rounded bg-yellow-500/20 text-yellow-200"
                    >
                      {alert.message}
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="routes" className="space-y-3 mt-3">
              <div className="space-y-2">
                <h4 className="text-xs font-medium text-sabone-cream uppercase tracking-wide flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Route Performance
                </h4>
                {routeStats.length > 0 ? (
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {routeStats.slice(0, 5).map((stat, index) => (
                      <div key={index} className="space-y-1">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-sabone-cream truncate">{stat.route}</span>
                          <Badge variant="outline" className="text-xs">
                            {stat.avgLoadTime}ms
                          </Badge>
                        </div>
                        <div className="flex items-center text-xs text-sabone-cream/60">
                          <Clock className="h-3 w-3 mr-1" />
                          {stat.loadCount} loads
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-sabone-cream/60">No route data yet</p>
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* Clear Button */}
          <Button
            onClick={() => {
              setMetrics({});
              setAlerts([]);
              setBundleMetrics([]);
              setBundleAlerts([]);
              setRouteStats([]);
            }}
            variant="outline"
            size="sm"
            className="w-full text-xs bg-transparent border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
          >
            Clear Data
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceTracker;