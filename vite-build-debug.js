// Add this to your vite.config.js temporarily for debugging
// This will help identify where the build process is failing

import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [
    react(),
    {
      name: 'vite-build-debug',
      configResolved(config) {
        console.log('🔍 Vite config resolved:', {
          mode: config.mode,
          base: config.base,
          root: config.root,
          publicDir: config.publicDir,
          cacheDir: config.cacheDir,
        });
      },
      buildStart() {
        console.log('🚀 Build starting...');
      },
      resolveId(id, importer) {
        if (id.includes('App') || id.includes('main') || id.includes('index')) {
          console.log(`🔍 Resolving: ${id} imported by ${importer || 'unknown'}`);
        }
        return null;
      },
      load(id) {
        if (id.includes('App') || id.includes('main') || id.includes('index')) {
          console.log(`📦 Loading: ${id}`);
        }
        return null;
      },
      transform(code, id) {
        if (id.includes('App') || id.includes('main') || id.includes('index')) {
          console.log(`🔄 Transforming: ${id}`);
        }
        return null;
      },
      buildEnd(error) {
        if (error) {
          console.error('❌ Build failed:', error);
        } else {
          console.log('✅ Build completed successfully');
        }
      },
    },
  ],
  server: {
    port: 8080,
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      onwarn(warning, warn) {
        // Log all warnings
        console.warn(`⚠️ Rollup warning: ${warning.message}`);
        warn(warning);
      },
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom'],
    force: true,
  },
});
