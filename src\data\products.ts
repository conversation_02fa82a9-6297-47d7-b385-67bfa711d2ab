
export interface Product {
  id: string;
  name: string;
  description: string;
  fullDescription?: string;
  price: number;
  image: string;
  type: 'bar' | 'liquid';
  ingredients: string[];
  benefits: string[];
  skinType?: string;
  application?: string;
  storage?: string;
}

export const products: Product[] = [
  {
    id: "lemon-verbena",
    name: "Lemon Verbena Fresh Bar",
    description: "A refreshing soap bar with zesty lemon verbena to revitalize your senses.",
    fullDescription: "A refreshing soap bar with zesty lemon verbena to awaken your senses. Crafted with coconut and sunflower oils, it's perfect for daily cleansing and a bright start to the day.",
    price: 12.99,
    image: "/lovable-uploads/Lemon Verbena Fresh Bar.png", // Ensure this matches the actual filename
    type: "bar",
    ingredients: ["Coconut Oil", "Sunflower Oil", "Lemon Verbena"],
    benefits: ["Energizing", "Cleansing", "Uplifting", "Refreshes skin", "Natural antibacterial"],
    skinType: "All skin types",
    application: "Lather with warm water and massage onto skin in circular motions."
  },
  {
    id: "rose-clay",
    name: "Rose Clay Glow Bar",
    description: "Infused with rose clay to gently exfoliate and promote a natural glow.",
    fullDescription: "Infused with mineral-rich rose clay and nourishing oils, this bar gently exfoliates while enhancing your skin's natural glow and softness.",
    price: 13.99,
    image: "/lovable-uploads/Rose Clay Glow Bar.png", // Ensure this matches the actual filename
    type: "bar",
    ingredients: ["Olive Oil", "Shea Butter", "Rose Clay", "Rose Essential Oil"],
    benefits: ["Gentle exfoliation", "Improves complexion", "Detoxifying", "Softening", "Skin-brightening"],
    skinType: "Normal to dry skin",
    application: "Use on damp skin with gentle circular motions, focusing on areas that need exfoliation."
  },
  {
    id: "scalp-rescue",
    name: "Scalp Rescue Shampoo",
    description: "A therapeutic shampoo that fights dandruff and soothes irritated scalp.",
    fullDescription: "A therapeutic shampoo formulated to fight dandruff, soothe irritation, and restore scalp health with a fresh, cooling herbal blend of essential oils.",
    price: 24.99,
    image: "/lovable-uploads/Scalp Rescue Shampoo.png", // Ensure this matches the actual filename
    type: "liquid",
    ingredients: ["Eucalyptus Oil", "Peppermint Oil", "Tea Tree Oil"],
    benefits: ["Fights dandruff", "Calms itchiness", "Soothes irritation", "Improves scalp circulation", "Cooling effect"],
    skinType: "Irritated or flaky scalp",
    application: "Massage into wet hair, focusing on the scalp. Leave for 2-3 minutes before rinsing thoroughly."
  },
  {
    id: "sensitive-scalp",
    name: "Sensitive Scalp Shampoo",
    description: "Formulated for sensitive scalps to cleanse without causing irritation.",
    fullDescription: "Designed for sensitive scalps, this gentle blend calms inflammation, reduces flakiness, and supports skin barrier health with soothing natural ingredients.",
    price: 24.99,
    image: "/lovable-uploads/Sensitive Scalp Shampoo.png", // Ensure this matches the actual filename
    type: "liquid",
    ingredients: ["Chamomile Extract", "Oat Milk", "Jojoba Oil"],
    benefits: ["Anti-inflammatory", "Nourishes scalp", "Reduces flaking", "Hypoallergenic", "Strengthens skin barrier"],
    skinType: "Sensitive or reactive scalp",
    application: "Apply to wet hair and massage gently. Rinse thoroughly with lukewarm water."
  },
  {
    id: "shine-silk",
    name: "Shine & Silk Shampoo",
    description: "Luxurious formula that enhances shine and smooths frizz for silky hair.",
    fullDescription: "A luxurious shampoo that enhances shine, reduces frizz, and nourishes hair with exotic oils and botanical extracts for a silky, radiant finish.",
    price: 26.99,
    image: "/lovable-uploads/Shine & Silk Shampoo.png", // Ensure this matches the actual filename
    type: "liquid",
    ingredients: ["Argan Oil", "Coconut Oil", "Hibiscus Extract"],
    benefits: ["Enhances shine", "Smooths frizz", "Deeply hydrates", "Strengthens hair", "Adds silkiness"],
    skinType: "All hair types, especially dry or damaged",
    application: "Apply to wet hair, massage gently, and rinse. For best results, follow with a conditioner."
  },
  {
    id: "hair-growth",
    name: "Hair Growth Boost Bar",
    description: "Stimulates hair follicles with natural ingredients for healthier hair growth.",
    fullDescription: "A powerful herbal soap bar to stimulate scalp circulation and promote natural hair growth using time-tested botanical ingredients that nourish from root to tip.",
    price: 14.99,
    image: "/lovable-uploads/the Hair Growth Boost Bar.png", // Ensure this matches the actual filename
    type: "bar",
    ingredients: ["Nettle", "Castor Oil", "Rosemary", "Biotin (plant-derived)"],
    benefits: ["Stimulates follicles", "Encourages hair regrowth", "Thickens weak strands", "Reduces hair loss", "Improves scalp health"],
    skinType: "All scalp types, especially thinning hair",
    application: "Massage directly onto wet scalp in circular motions. Leave lather for 1-2 minutes before rinsing."
  },
  {
    id: "herbal-hammam",
    name: "Herbal Hammam Bar",
    description: "Traditional hammam soap bar with herbs and olive oil for deep cleansing.",
    fullDescription: "Inspired by the traditions of the Middle Eastern hammam, this earthy bar combines deep cleansing clays with soothing botanicals for an authentic bathing ritual.",
    price: 15.99,
    image: "/lovable-uploads/the Herbal Hammam Bar.png", // Ensure this matches the actual filename
    type: "bar",
    ingredients: ["Moroccan Rhassoul Clay", "Olive Oil", "Laurel Berry Oil", "Bay Leaf"],
    benefits: ["Deep pore cleansing", "Antibacterial", "Detoxifying", "Restores skin balance", "Ritual-like cleansing"],
    skinType: "All skin types, especially congested skin",
    application: "Use with a traditional kessa glove if available. Massage in circular motions for deep cleansing.",
    storage: "Keep dry between uses to extend the life of the bar."
  },
  {
    id: "royal-oud",
    name: "Royal Oud Bar",
    description: "Luxurious soap with oud, castor and shea for an aromatic bathing ritual.",
    fullDescription: "A luxurious soap bar crafted with oud, castor oil, and shea butter for an aromatic, skin-softening bathing experience that transforms your daily cleansing into a royal ritual.",
    price: 18.99,
    image: "/lovable-uploads/the Royal Oud Bar.png", // Ensure this matches the actual filename
    type: "bar",
    ingredients: ["Oud", "Castor Oil", "Shea Butter", "Amberwood Essence"],
    benefits: ["Moisturizing", "Aromatherapeutic", "Rich, warm scent", "Soothes dry skin", "Elevates mood"],
    skinType: "Dry to normal skin",
    application: "Use during warm baths or showers. Allow the aroma to fill the space for a complete sensory experience.",
    storage: "Store in a cool, dry place to preserve the aromatic properties."
  },
  {
    id: "white-misk",
    name: "White Misk Bar",
    description: "A gentle cleansing bar with musk, olive and honey for a tahara ritual.",
    fullDescription: "A sacred soap for spiritual cleansing, infused with white musk, honey, and olive oil for a soft, purifying experience perfect for wudu and tahara rituals.",
    price: 16.99,
    image: "/lovable-uploads/the White Misk Bar.png", // Ensure this matches the actual filename
    type: "bar",
    ingredients: ["White Musk", "Honey", "Olive Oil", "Frankincense"],
    benefits: ["Calming scent", "Purifies body & spirit", "Skin softening", "Antioxidant-rich", "Perfect before prayer"],
    skinType: "All skin types",
    application: "Use with warm water before prayer or meditation for a complete cleansing ritual.",
    storage: "Store away from direct sunlight to preserve the delicate scent."
  }
];
