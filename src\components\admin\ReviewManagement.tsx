import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useReviews } from "@/contexts/ReviewContext";
import { useProducts } from "@/contexts/ProductContext";
import { Review } from "@/types/review";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { StarRating } from "@/components/ui/star-rating";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Search,
  RefreshCw,
  Eye,
  CheckCircle2,
  XCircle,
  MessageSquare,
  Trash2
} from "lucide-react";

const ReviewManagement = () => {
  const { user } = useAuth();
  const {
    pendingReviews,
    isLoadingPendingReviews,
    getPendingReviews,
    updateReview,
    deleteReview,
    addAdminResponse,
    refreshPendingReviews
  } = useReviews();
  const { getProductById } = useProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [responseText, setResponseText] = useState("");

  const fetchReviews = useCallback(async () => {
    try {
      await getPendingReviews();
    } catch (error) {
      console.error("Error fetching reviews:", error);
      toast.error("Failed to load reviews");
    }
  }, [getPendingReviews]);

  useEffect(() => {
    fetchReviews();
  }, [fetchReviews]);

  const handleApprove = async (reviewId: string) => {
    try {
      await updateReview(reviewId, { status: 'approved' });
      toast.success("Review approved successfully");
      await refreshPendingReviews();
    } catch (error) {
      console.error("Error approving review:", error);
      toast.error("Failed to approve review");
    }
  };

  const handleReject = async (reviewId: string) => {
    try {
      await updateReview(reviewId, { status: 'rejected' });
      toast.success("Review rejected");
      await refreshPendingReviews();
    } catch (error) {
      console.error("Error rejecting review:", error);
      toast.error("Failed to reject review");
    }
  };

  const handleDelete = async (reviewId: string) => {
    try {
      await deleteReview(reviewId);
      toast.success("Review deleted successfully");
      await refreshPendingReviews();
    } catch (error) {
      console.error("Error deleting review:", error);
      toast.error("Failed to delete review");
    }
  };

  const handleSubmitResponse = async () => {
    if (!selectedReview || !responseText.trim() || !user) {
      return;
    }

    try {
      await addAdminResponse(selectedReview.id, responseText);
      await updateReview(selectedReview.id, { status: 'approved' });
      toast.success("Response added and review approved");
      setResponseText("");
      setSelectedReview(null);
      await refreshPendingReviews();
    } catch (error) {
      console.error("Error adding response:", error);
      toast.error("Failed to add response");
    }
  };

  const filteredReviews = pendingReviews.filter(review => {
    const product = getProductById(review.productId);
    const productName = product ? product.name.toLowerCase() : "";
    const reviewContent = review.content.toLowerCase();
    const reviewTitle = review.title.toLowerCase();
    const userName = review.userName.toLowerCase();
    const searchLower = searchTerm.toLowerCase();

    return (
      productName.includes(searchLower) ||
      reviewContent.includes(searchLower) ||
      reviewTitle.includes(searchLower) ||
      userName.includes(searchLower)
    );
  });

  const getProductName = (productId: string) => {
    const product = getProductById(productId);
    return product ? product.name : "Unknown Product";
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Review Management</h2>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-sabone-cream/50" />
            <Input
              placeholder="Search reviews..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream w-full sm:w-[250px]"
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={fetchReviews}
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      {isLoadingPendingReviews ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="bg-sabone-dark-olive/40 p-4 rounded-lg animate-pulse">
              <div className="h-6 w-1/3 bg-sabone-gold/20 rounded mb-2"></div>
              <div className="h-4 w-1/2 bg-sabone-gold/20 rounded mb-4"></div>
              <div className="h-4 w-full bg-sabone-gold/20 rounded"></div>
            </div>
          ))}
        </div>
      ) : filteredReviews.length === 0 ? (
        <div className="bg-sabone-dark-olive/40 p-6 rounded-lg text-center">
          <p className="text-sabone-cream/70">No pending reviews found</p>
        </div>
      ) : (
        <div className="rounded-md border border-sabone-gold/20 overflow-hidden">
          <Table>
            <TableHeader className="bg-sabone-dark-olive/60">
              <TableRow>
                <TableHead className="text-sabone-gold">Product</TableHead>
                <TableHead className="text-sabone-gold">Review</TableHead>
                <TableHead className="text-sabone-gold">Customer</TableHead>
                <TableHead className="text-sabone-gold">Rating</TableHead>
                <TableHead className="text-sabone-gold">Date</TableHead>
                <TableHead className="text-sabone-gold text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReviews.map((review) => (
                <TableRow key={review.id} className="border-sabone-gold/10 hover:bg-sabone-dark-olive/40">
                  <TableCell className="font-medium text-sabone-cream">
                    {getProductName(review.productId)}
                  </TableCell>
                  <TableCell className="text-sabone-cream/90">
                    <div className="max-w-xs truncate" title={review.content}>
                      <span className="font-medium">{review.title}</span>
                      <p className="text-sm text-sabone-cream/70 truncate">{review.content}</p>
                    </div>
                  </TableCell>
                  <TableCell className="text-sabone-cream/90">
                    {review.userName}
                    {review.isVerifiedPurchase && (
                      <Badge variant="outline" className="ml-2 bg-green-500/10 text-green-500 border-green-500/30">
                        Verified
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <StarRating rating={review.rating} size="sm" />
                  </TableCell>
                  <TableCell className="text-sabone-cream/70">
                    {new Date(review.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-sabone-dark-olive/95 border-sabone-gold/30 text-sabone-cream">
                          <DialogHeader>
                            <DialogTitle className="text-sabone-gold">Review Details</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4 mt-4">
                            <div>
                              <h4 className="text-sabone-gold font-medium mb-1">Product</h4>
                              <p className="text-sabone-cream">{getProductName(review.productId)}</p>
                            </div>
                            <div>
                              <h4 className="text-sabone-gold font-medium mb-1">Rating</h4>
                              <StarRating rating={review.rating} />
                            </div>
                            <div>
                              <h4 className="text-sabone-gold font-medium mb-1">Title</h4>
                              <p className="text-sabone-cream">{review.title}</p>
                            </div>
                            <div>
                              <h4 className="text-sabone-gold font-medium mb-1">Review</h4>
                              <p className="text-sabone-cream">{review.content}</p>
                            </div>
                            {review.images && review.images.length > 0 && (
                              <div>
                                <h4 className="text-sabone-gold font-medium mb-1">Images</h4>
                                <div className="flex flex-wrap gap-2">
                                  {review.images.map((image, index) => (
                                    <img
                                      key={index}
                                      src={image}
                                      alt={`Review ${index + 1}`}
                                      className="w-20 h-20 object-cover rounded-md"
                                    />
                                  ))}
                                </div>
                              </div>
                            )}
                            <div>
                              <h4 className="text-sabone-gold font-medium mb-1">Customer</h4>
                              <p className="text-sabone-cream">
                                {review.userName}
                                {review.isVerifiedPurchase && (
                                  <Badge variant="outline" className="ml-2 bg-green-500/10 text-green-500 border-green-500/30">
                                    Verified Purchase
                                  </Badge>
                                )}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sabone-gold font-medium mb-1">Date</h4>
                              <p className="text-sabone-cream">
                                {new Date(review.createdAt).toLocaleString()}
                              </p>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleApprove(review.id)}
                        className="h-8 w-8 border-green-500/30 text-green-500 hover:bg-green-500/10"
                      >
                        <CheckCircle2 className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleReject(review.id)}
                        className="h-8 w-8 border-red-500/30 text-red-500 hover:bg-red-500/10"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                            onClick={() => setSelectedReview(review)}
                          >
                            <MessageSquare className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-sabone-dark-olive/95 border-sabone-gold/30 text-sabone-cream">
                          <DialogHeader>
                            <DialogTitle className="text-sabone-gold">Respond to Review</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4 mt-4">
                            <div className="bg-sabone-charcoal/50 p-4 rounded-md">
                              <p className="text-sabone-gold font-medium mb-1">{review.title}</p>
                              <StarRating rating={review.rating} size="sm" className="mb-2" />
                              <p className="text-sabone-cream/90">{review.content}</p>
                            </div>
                            <Textarea
                              placeholder="Write your response..."
                              value={responseText}
                              onChange={(e) => setResponseText(e.target.value)}
                              className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream min-h-[120px]"
                            />
                          </div>
                          <DialogFooter className="mt-4">
                            <DialogClose asChild>
                              <Button
                                variant="outline"
                                className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
                              >
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button
                              className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                              onClick={handleSubmitResponse}
                              disabled={!responseText.trim()}
                            >
                              Submit Response
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 border-red-500/30 text-red-500 hover:bg-red-500/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-sabone-dark-olive/95 border-sabone-gold/30 text-sabone-cream">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-sabone-gold">Delete Review</AlertDialogTitle>
                            <AlertDialogDescription className="text-sabone-cream/70">
                              Are you sure you want to delete this review? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10">
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                              className="bg-red-500 hover:bg-red-600 text-white"
                              onClick={() => handleDelete(review.id)}
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default ReviewManagement;
