# Sabone.store Email Notification System

This document provides an overview of the email notification system implemented for Sabone.store.

## Overview

The email notification system is designed to send various types of emails to customers and administrators, including:

- **Order-related emails**: Confirmation, shipping, delivery, and cancellation notifications
- **Account-related emails**: Welcome, password reset, and account update notifications
- **Admin notifications**: Low stock alerts, new order notifications, and sales summaries
- **Marketing emails**: Newsletters and promotional campaigns

## Technical Implementation

### Core Components

1. **Email Types and Templates**
   - Located in `src/types/email.ts` and `src/templates/emailTemplates.ts`
   - Defines all email types, data structures, and HTML templates

2. **Email Service**
   - Located in `src/services/emailService.ts`
   - Handles sending emails via SendGrid
   - Manages email preferences and opt-in/opt-out functionality

3. **Email Context**
   - Located in `src/contexts/EmailContext.tsx`
   - Provides a React context for email-related functionality
   - Exposes methods for sending different types of emails

4. **Email Preferences UI**
   - Located in `src/components/account/EmailPreferences.tsx`
   - Allows users to manage their email preferences

### Integration Points

The email notification system is integrated with the following components:

1. **Order Management**
   - Sends order confirmation emails when orders are placed
   - Sends order status update emails when orders are shipped, delivered, or cancelled

2. **Inventory Management**
   - Sends low stock alerts to administrators when inventory falls below thresholds

3. **User Authentication**
   - Sends welcome emails to new users
   - Sends password reset emails when requested

## Setup Instructions

### 1. SendGrid Account Setup

1. Create a SendGrid account at [sendgrid.com](https://sendgrid.com)
2. Create an API key with full access to "Mail Send" permissions
3. Verify your sender identity (domain or single sender)

### 2. Environment Configuration

1. Copy `.env.example` to `.env`
2. Add your SendGrid API key to the `.env` file:
   ```
   VITE_SENDGRID_API_KEY=your-sendgrid-api-key
   ```

### 3. Email Templates Customization

The email templates are defined in `src/templates/emailTemplates.ts`. You can customize the HTML and styling to match your brand identity.

### 4. Testing Email Functionality

1. Use the `previewEmail` function in development to preview emails without sending them
2. For testing actual email delivery, use a test email address

## Email Types and Triggers

| Email Type | Trigger | Recipient | Content |
|------------|---------|-----------|---------|
| Order Confirmation | Order placed | Customer | Order details, items, pricing, estimated delivery |
| Order Shipped | Order status changed to "shipped" | Customer | Shipping confirmation, tracking information |
| Order Delivered | Order status changed to "delivered" | Customer | Delivery confirmation |
| Order Cancelled | Order cancelled | Customer | Cancellation details |
| Welcome | New user registration | Customer | Welcome message, account verification |
| Password Reset | Password reset requested | Customer | Password reset link |
| Account Update | Account information updated | Customer | Update confirmation |
| Low Stock Alert | Inventory falls below threshold | Admin | List of low stock items |
| New Order Admin | New order placed | Admin | Order details, customer information |
| Sales Summary | Daily/weekly/monthly | Admin | Sales metrics, top products |
| Newsletter | Manual trigger | Subscribers | Content, featured products |
| Promotional | Manual trigger | Subscribers | Promotion details, discount codes |

## User Preferences Management

Users can manage their email preferences through their account settings:

1. **Order Notifications**: Emails about orders (confirmations, shipping updates, etc.)
2. **Account Notifications**: Emails about account changes (welcome, password resets, etc.)
3. **Marketing Emails**: Promotional emails and newsletters

## Best Practices

1. **Compliance**: Ensure all marketing emails include an unsubscribe link
2. **Personalization**: Use customer name and relevant details in emails
3. **Responsive Design**: All email templates are responsive for mobile devices
4. **Error Handling**: Email sending failures are logged but don't block critical operations
5. **Testing**: Test emails across different email clients before deployment

## Future Enhancements

1. **Email Analytics**: Track open rates, click-through rates, etc.
2. **A/B Testing**: Test different email templates for effectiveness
3. **Automated Campaigns**: Set up automated email sequences
4. **Advanced Personalization**: Personalize emails based on user behavior
5. **Localization**: Support for multiple languages
