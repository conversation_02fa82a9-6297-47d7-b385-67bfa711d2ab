
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

const Contact = () => {
  const [email, setEmail] = useState("");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsVisible(true);
      }
    }, { threshold: 0.1 });

    const element = document.getElementById("contact-section");
    if (element) observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success("Thank you for subscribing!");
    setEmail("");
  };

  return (
    <section id="contact-section" className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      <div className="pattern-dot absolute inset-0 opacity-5"></div>

      {/* Background elements */}
      <div className="absolute inset-0 opacity-10 overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-sabone-gold/5 blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-48 h-48 rounded-full bg-sabone-gold/5 blur-3xl"></div>
      </div>

      <div className="max-w-4xl mx-auto relative z-10">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-playfair font-bold text-sabone-gold">Join the Circle</h2>
            <div className="arabesque-divider w-24 mx-auto my-6"></div>
            <p className="max-w-2xl mx-auto text-lg text-sabone-cream/80">
              Receive exclusive access to seasonal drops, ancient rituals, and sacred scents.
              Be the first to discover our limited edition collections and ritual wisdom.
            </p>
          </div>

          <div className="bg-sabone-dark-olive/40 p-8 rounded-lg gold-border animate-soft-glow">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream placeholder:text-sabone-cream/50 flex-grow"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <Button
                  type="submit"
                  className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                >
                  Subscribe
                </Button>
              </div>

              <p className="text-sm text-sabone-cream/60 text-center">
                Join our sacred circle of ritual enthusiasts. Receive wisdom from ancient traditions
                and be the first to experience our seasonal collections.
              </p>
            </form>
          </div>

          <div className="mt-16 grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-3">Contact</h3>
              <p className="text-sabone-cream/80"><EMAIL></p>
              <p className="text-sabone-cream/80">+1 (555) 123-4567</p>
            </div>

            <div className="text-center">
              <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-3">Visit</h3>
              <p className="text-sabone-cream/80">123 Essence Street</p>
              <p className="text-sabone-cream/80">Aromacity, AC 56789</p>
            </div>

            <div className="text-center">
              <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-3">Follow</h3>
              <div className="flex justify-center gap-4 mt-2">
                <a href="#" className="text-sabone-gold hover:text-sabone-gold-light transition-colors">
                  <span className="sr-only">Instagram</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-instagram"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"/><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"/></svg>
                </a>
                <a href="#" className="text-sabone-gold hover:text-sabone-gold-light transition-colors">
                  <span className="sr-only">Facebook</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-facebook"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/></svg>
                </a>
                <a href="#" className="text-sabone-gold hover:text-sabone-gold-light transition-colors">
                  <span className="sr-only">Twitter</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-twitter"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"/></svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
