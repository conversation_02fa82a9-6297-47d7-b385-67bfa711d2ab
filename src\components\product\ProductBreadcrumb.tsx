import { Link } from "react-router-dom";
import { ChevronRight } from "lucide-react";
import { Product } from "@/data/products";

interface ProductBreadcrumbProps {
  product: Product;
}

const ProductBreadcrumb = ({ product }: ProductBreadcrumbProps) => {
  // Determine the category name based on product type
  const categoryName = product.type === 'bar' ? 'Soap Bars' : 'Shampoos';
  
  return (
    <nav aria-label="Breadcrumb" className="mb-6">
      <ol className="flex flex-wrap items-center text-sm text-sabone-cream/60">
        <li className="flex items-center">
          <Link to="/" className="hover:text-sabone-gold transition-colors">
            Home
          </Link>
          <ChevronRight className="h-4 w-4 mx-2" />
        </li>
        <li className="flex items-center">
          <Link to="/" className="hover:text-sabone-gold transition-colors">
            {categoryName}
          </Link>
          <ChevronRight className="h-4 w-4 mx-2" />
        </li>
        <li className="text-sabone-gold font-medium truncate max-w-[200px] sm:max-w-xs">
          {product.name}
        </li>
      </ol>
    </nav>
  );
};

export default ProductBreadcrumb;
