import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { Product } from '@/data/products';
import { useProducts } from '@/contexts/ProductContext';
import { searchProducts, filterProducts, sortProducts } from '@/utils/searchUtils';

export interface SearchFilters {
  category?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  inStock?: boolean;
  featured?: boolean;
  type?: 'bar' | 'liquid';
  ingredients?: string[];
  benefits?: string[];
  scent?: string;
}

export interface SortOption {
  field: 'name' | 'price' | 'featured' | 'type';
  direction: 'asc' | 'desc';
}

interface SearchState {
  query: string;
  filters: SearchFilters;
  sortBy: SortOption;
  results: Product[];
  isSearching: boolean;
  searchHistory: string[];
  suggestions: string[];
  totalResults: number;
  currentPage: number;
  itemsPerPage: number;
}

interface SearchContextType extends SearchState {
  // Search actions
  setQuery: (query: string) => void;
  setFilters: (filters: Partial<SearchFilters>) => void;
  setSortBy: (sort: SortOption) => void;
  clearFilters: () => void;
  clearSearch: () => void;
  
  // Pagination
  setCurrentPage: (page: number) => void;
  setItemsPerPage: (items: number) => void;
  
  // Search history
  addToHistory: (query: string) => void;
  clearHistory: () => void;
  
  // Utility functions
  hasActiveFilters: boolean;
  paginatedResults: Product[];
  totalPages: number;
  
  // Quick filters
  applyQuickFilter: (filterType: keyof SearchFilters, value: any) => void;
  getFilterOptions: () => {
    categories: string[];
    priceRange: { min: number; max: number };
    ingredients: string[];
    benefits: string[];
    scents: string[];
  };
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

const INITIAL_STATE: SearchState = {
  query: '',
  filters: {},
  sortBy: { field: 'name', direction: 'asc' },
  results: [],
  isSearching: false,
  searchHistory: [],
  suggestions: [],
  totalResults: 0,
  currentPage: 1,
  itemsPerPage: 12,
};

export const SearchProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { products } = useProducts();
  const [searchState, setSearchState] = useState<SearchState>(INITIAL_STATE);

  // Load search history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('sabone-search-history');
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory);
        setSearchState(prev => ({ ...prev, searchHistory: history }));
      } catch (error) {
        console.error('Error loading search history:', error);
      }
    }
  }, []);

  // Save search history to localStorage
  useEffect(() => {
    localStorage.setItem('sabone-search-history', JSON.stringify(searchState.searchHistory));
  }, [searchState.searchHistory]);

  // Perform search when query, filters, or sort changes
  useEffect(() => {
    const performSearch = async () => {
      setSearchState(prev => ({ ...prev, isSearching: true }));

      try {
        let results = [...products];

        // Apply text search
        if (searchState.query.trim()) {
          results = searchProducts(results, searchState.query);
        }

        // Apply filters
        results = filterProducts(results, searchState.filters);

        // Apply sorting
        results = sortProducts(results, searchState.sortBy);

        setSearchState(prev => ({
          ...prev,
          results,
          totalResults: results.length,
          isSearching: false,
          currentPage: 1, // Reset to first page on new search
        }));
      } catch (error) {
        console.error('Search error:', error);
        setSearchState(prev => ({
          ...prev,
          results: [],
          totalResults: 0,
          isSearching: false,
        }));
      }
    };

    performSearch();
  }, [products, searchState.query, searchState.filters, searchState.sortBy]);

  // Generate search suggestions based on query
  useEffect(() => {
    if (searchState.query.length > 1) {
      const suggestions = products
        .filter(product => 
          product.name.toLowerCase().includes(searchState.query.toLowerCase()) ||
          product.description.toLowerCase().includes(searchState.query.toLowerCase()) ||
          product.ingredients.some(ingredient => 
            ingredient.toLowerCase().includes(searchState.query.toLowerCase())
          )
        )
        .slice(0, 5)
        .map(product => product.name);
      
      setSearchState(prev => ({ ...prev, suggestions }));
    } else {
      setSearchState(prev => ({ ...prev, suggestions: [] }));
    }
  }, [searchState.query, products]);

  // Memoized computed values
  const hasActiveFilters = useMemo(() => {
    return Object.keys(searchState.filters).length > 0;
  }, [searchState.filters]);

  const paginatedResults = useMemo(() => {
    const startIndex = (searchState.currentPage - 1) * searchState.itemsPerPage;
    const endIndex = startIndex + searchState.itemsPerPage;
    return searchState.results.slice(startIndex, endIndex);
  }, [searchState.results, searchState.currentPage, searchState.itemsPerPage]);

  const totalPages = useMemo(() => {
    return Math.ceil(searchState.totalResults / searchState.itemsPerPage);
  }, [searchState.totalResults, searchState.itemsPerPage]);

  const filterOptions = useMemo(() => {
    const categories = [...new Set(products.map(p => p.category))].filter(Boolean);
    const ingredients = [...new Set(products.flatMap(p => p.ingredients))];
    const benefits = [...new Set(products.flatMap(p => p.benefits))];
    const scents = [...new Set(products.map(p => p.scent))].filter(Boolean);
    
    const prices = products.map(p => p.price);
    const priceRange = {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };

    return { categories, priceRange, ingredients, benefits, scents };
  }, [products]);

  // Action handlers
  const setQuery = useCallback((query: string) => {
    setSearchState(prev => ({ ...prev, query }));
  }, []);

  const setFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setSearchState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters },
    }));
  }, []);

  const setSortBy = useCallback((sort: SortOption) => {
    setSearchState(prev => ({ ...prev, sortBy: sort }));
  }, []);

  const clearFilters = useCallback(() => {
    setSearchState(prev => ({ ...prev, filters: {} }));
  }, []);

  const clearSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      query: '',
      filters: {},
      currentPage: 1,
    }));
  }, []);

  const setCurrentPage = useCallback((page: number) => {
    setSearchState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setItemsPerPage = useCallback((items: number) => {
    setSearchState(prev => ({ ...prev, itemsPerPage: items, currentPage: 1 }));
  }, []);

  const addToHistory = useCallback((query: string) => {
    if (query.trim() && !searchState.searchHistory.includes(query)) {
      setSearchState(prev => ({
        ...prev,
        searchHistory: [query, ...prev.searchHistory.slice(0, 9)], // Keep last 10
      }));
    }
  }, [searchState.searchHistory]);

  const clearHistory = useCallback(() => {
    setSearchState(prev => ({ ...prev, searchHistory: [] }));
  }, []);

  const applyQuickFilter = useCallback((filterType: keyof SearchFilters, value: any) => {
    setFilters({ [filterType]: value });
  }, [setFilters]);

  const getFilterOptions = useCallback(() => filterOptions, [filterOptions]);

  const contextValue: SearchContextType = {
    ...searchState,
    setQuery,
    setFilters,
    setSortBy,
    clearFilters,
    clearSearch,
    setCurrentPage,
    setItemsPerPage,
    addToHistory,
    clearHistory,
    hasActiveFilters,
    paginatedResults,
    totalPages,
    applyQuickFilter,
    getFilterOptions,
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
};

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (context === undefined) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

export default SearchContext;
