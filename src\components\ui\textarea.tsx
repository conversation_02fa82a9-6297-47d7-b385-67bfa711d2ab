import * as React from "react"
import { sanitizeString, containsXssPatterns, containsSqlInjection } from "@/utils/inputSanitization"
import { cn } from "@/lib/utils"

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  enableSanitization?: boolean;
  onSecurityViolation?: (violation: string) => void;
  maxLength?: number;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, enableSanitization = true, onSecurityViolation, maxLength = 5000, onChange, ...props }, ref) => {

    const handleChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;

      if (enableSanitization && value) {
        // Check for security violations
        if (containsXssPatterns(value)) {
          onSecurityViolation?.('XSS patterns detected');
          console.warn('Security: XSS patterns detected in textarea');
          return; // Block the input
        }

        if (containsSqlInjection(value)) {
          onSecurityViolation?.('SQL injection patterns detected');
          console.warn('Security: SQL injection patterns detected in textarea');
          return; // Block the input
        }

        // Sanitize the input
        const sanitized = sanitizeString(value, {
          level: 'MODERATE',
          allowHtml: false,
          maxLength: maxLength,
          trimWhitespace: false, // Don't trim whitespace in textarea
          removeControlChars: true,
          normalizeUnicode: true,
        });

        // Update the textarea value with sanitized content
        e.target.value = sanitized;
      }

      // Call the original onChange handler
      onChange?.(e);
    }, [enableSanitization, onSecurityViolation, maxLength, onChange]);

    return (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        onChange={handleChange}
        maxLength={maxLength}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
