import DOMPurify from 'dompurify';
import type { Config } from 'dompurify';
import { logger } from './logger';

/**
 * Sanitizes HTML content to prevent XSS attacks
 * @param dirty - The potentially unsafe HTML string
 * @param options - Optional DOMPurify configuration
 * @returns Sanitized HTML string
 */
export const sanitizeHTML = (
  dirty: string,
  options?: Config
): string => {
  try {
    const defaultOptions: Config = {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: ['href', 'target', 'rel'],
      ALLOW_DATA_ATTR: false,
      RETURN_TRUSTED_TYPE: false,
    };

    return DOMPurify.sanitize(dirty, { ...defaultOptions, ...options }) as string;
  } catch (error: unknown) {
    logger.error('Failed to sanitize HTML', error instanceof Error ? error : new Error(String(error)), {
      content: dirty
    });
    return '';
  }
};

/**
 * Escapes special characters in a string to prevent XSS
 * @param unsafe - The potentially unsafe string
 * @returns Escaped string safe for HTML rendering
 */
export const escapeHTML = (unsafe: string): string => {
  if (typeof unsafe !== 'string') return '';

  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

/**
 * Validates and sanitizes user input based on type
 * @param input - The user input to validate
 * @param type - The expected input type
 * @returns Sanitized input or null if invalid
 */
export const validateInput = (
  input: unknown,
  type: 'email' | 'phone' | 'text' | 'number' | 'url'
): string | number | null => {
  if (input === null || input === undefined) return null;

  const stringInput = String(input).trim();

  switch (type) {
    case 'email': {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(stringInput) ? stringInput.toLowerCase() : null;
    }

    case 'phone': {
      const phoneRegex = /^[\d\s\-+()]+$/;
      const cleaned = stringInput.replace(/\D/g, '');
      return phoneRegex.test(stringInput) && cleaned.length >= 10 ? cleaned : null;
    }

    case 'text': {
      // Remove any HTML tags and dangerous characters
      return escapeHTML(stringInput);
    }

    case 'number': {
      const num = Number(stringInput);
      return !isNaN(num) && isFinite(num) ? num : null;
    }

    case 'url': {
      try {
        const url = new URL(stringInput);
        // Only allow http and https protocols
        if (url.protocol === 'http:' || url.protocol === 'https:') {
          return url.toString();
        }
        return null;
      } catch {
        return null;
      }
    }

    default:
      return null;
  }
};

/**
 * Generates a CSRF token for form submissions
 * @returns A secure random token
 */
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Validates a CSRF token
 * @param token - The token to validate
 * @param storedToken - The stored token to compare against
 * @returns True if tokens match
 */
export const validateCSRFToken = (token: string, storedToken: string): boolean => {
  if (!token || !storedToken) return false;
  if (token.length !== storedToken.length) return false;

  // Constant-time comparison to prevent timing attacks
  let result = 0;
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ storedToken.charCodeAt(i);
  }
  return result === 0;
};

/**
 * Sanitizes file names to prevent directory traversal attacks
 * @param fileName - The file name to sanitize
 * @returns Sanitized file name
 */
export const sanitizeFileName = (fileName: string): string => {
  return fileName
    .replace(/[^a-zA-Z0-9\-_.]/g, '_')
    .replace(/\.{2,}/g, '.')
    .replace(/^\./, '')
    .slice(0, 255);
};

/**
 * Creates a Content Security Policy header
 * @returns CSP header string
 */
export const getCSPHeader = (): string => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  const directives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://www.paypal.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://api.stripe.com https://www.paypal.com https://*.auth0.com",
    "frame-src 'self' https://js.stripe.com https://www.paypal.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    isDevelopment ? "" : "upgrade-insecure-requests"
  ].filter(Boolean);

  return directives.join('; ');
};

/**
 * Validates environment variables to prevent exposure of sensitive data
 * @returns Object with validated environment variables
 */
export const getSecureEnvVars = () => {
  // For testing environment, use process.env or provide defaults
  const getEnvVar = (key: string) => {
    if (typeof window !== 'undefined' && (window as any).process?.env) {
      return (window as any).process.env[key];
    }
    if (typeof process !== 'undefined' && process.env) {
      return process.env[key];
    }
    return undefined;
  };

  const publicVars = {
    VITE_STRIPE_PUBLISHABLE_KEY: getEnvVar('VITE_STRIPE_PUBLISHABLE_KEY'),
    VITE_PAYPAL_CLIENT_ID: getEnvVar('VITE_PAYPAL_CLIENT_ID'),
    VITE_AUTH0_DOMAIN: getEnvVar('VITE_AUTH0_DOMAIN'),
    VITE_AUTH0_CLIENT_ID: getEnvVar('VITE_AUTH0_CLIENT_ID'),
    VITE_API_URL: getEnvVar('VITE_API_URL'),
  };

  // Never expose these in client code
  const sensitiveKeys = [
    'VITE_STRIPE_SECRET_KEY',
    'VITE_PAYPAL_CLIENT_SECRET',
    'VITE_SENDGRID_API_KEY',
    'VITE_STRIPE_WEBHOOK_SECRET',
    'DATABASE_URL',
    'JWT_SECRET'
  ];

  // Check if any sensitive keys are exposed
  sensitiveKeys.forEach(key => {
    if (getEnvVar(key)) {
      logger.error(`SECURITY WARNING: Sensitive environment variable ${key} is exposed in client code!`);
    }
  });

  return publicVars;
};

/**
 * Rate limiting utility for client-side operations
 */
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  /**
   * Checks if an action is allowed based on rate limits
   * @param key - Unique identifier for the action
   * @returns True if action is allowed
   */
  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];

    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);

    if (validAttempts.length >= this.maxAttempts) {
      logger.warn('Rate limit exceeded', { key, attempts: validAttempts.length });
      return false;
    }

    validAttempts.push(now);
    this.attempts.set(key, validAttempts);

    // Cleanup old entries
    if (this.attempts.size > 1000) {
      this.cleanup();
    }

    return true;
  }

  /**
   * Cleans up old entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, attempts] of this.attempts.entries()) {
      const validAttempts = attempts.filter(time => now - time < this.windowMs);
      if (validAttempts.length === 0) {
        this.attempts.delete(key);
      } else {
        this.attempts.set(key, validAttempts);
      }
    }
  }

  /**
   * Resets rate limit for a specific key
   * @param key - The key to reset
   */
  reset(key: string): void {
    this.attempts.delete(key);
  }
}

// Export a default rate limiter instance
export const defaultRateLimiter = new RateLimiter();
