import { useState, useEffect } from "react";
import { useProducts } from "@/contexts/ProductContext";
import ProductCard from "./ProductCard"; // This will now use the modular ProductCard
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import PullToRefresh from "@/components/ui/PullToRefresh";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";
import { useTranslations, useLocale } from "next-intl"; // Import useLocale

const ProductGrid = () => {
  const t = useTranslations('common.productGrid');
  const locale = useLocale(); // Get current locale
  const { products, refreshProducts } = useProducts();
  const [activeTab, setActiveTab] = useState("all");
  const [isVisible, setIsVisible] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsVisible(true);
      }
    }, { threshold: 0.1 });

    const element = document.getElementById("products-section");
    if (element) observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, []);

  // Debug locale and translations in ProductGrid
  useEffect(() => {
    console.log('ProductGrid: Current locale:', locale);
    console.log('ProductGrid: Translations for common.productGrid:', {
      title: t('title'),
      description: t('description'),
      tabAllProducts: t('tabAllProducts'),
      tabSoapBars: t('tabSoapBars'),
      tabShampoos: t('tabShampoos'),
    });
  }, [locale, t]); // Re-run when locale or t changes

  // Force component re-render when locale changes
  useEffect(() => {
    console.log('ProductGrid: Locale changed to', locale);
    // This empty effect with locale dependency will trigger re-render when locale changes
  }, [locale]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle pull-to-refresh action
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // If refreshProducts is async, await it
      await refreshProducts();
      toast.success("Products refreshed successfully");
    } catch (error) {
      console.error("Error refreshing products:", error);
      toast.error("Failed to refresh products");
    } finally {
      setIsRefreshing(false);
    }
  };

  const filteredProducts = activeTab === "all"
    ? products
    : products.filter(product => product.type === activeTab);

  return (
    <section id="products-section" className="py-16 md:py-20 px-4 sm:px-6 lg:px-8 relative" key={`product-grid-${locale}`}>
      {/* Background elements */}
      <div className="absolute inset-0 opacity-5 bg-arabesque bg-repeat bg-center"></div>

      <div className="max-w-7xl mx-auto relative">
        <div className={`text-center mb-12 md:mb-16 transition-all duration-700 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-8'}`}>
          <h2 className="text-3xl sm:text-4xl font-playfair font-bold mb-4 text-sabone-gold text-center">{t('title')}</h2>
          <div className="arabesque-divider w-24 mx-auto my-6 animate-soft-glow"></div>
          <p className="max-w-2xl mx-auto text-base md:text-lg text-sabone-cream/90 text-center">
            {t('description')}
          </p>
        </div>

        {/* Conditionally wrap with PullToRefresh only on mobile */}
        {isMobile ? (
          <PullToRefresh onRefresh={handleRefresh} disabled={isRefreshing}>
            <Tabs defaultValue="all" className="w-full" onValueChange={handleTabChange}>
              <div className="flex justify-center mb-8 md:mb-10 overflow-x-auto pb-2">
                <TabsList className="bg-sabone-dark-olive h-12 p-1 flex justify-center">
                  <TabsTrigger
                    value="all"
                    className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px] text-center"
                  >
                    {t('tabAllProducts')}
                  </TabsTrigger>
                  <TabsTrigger
                    value="bar"
                    className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px] text-center"
                  >
                    {t('tabSoapBars')}
                  </TabsTrigger>
                  <TabsTrigger
                    value="liquid"
                    className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px] text-center"
                  >
                    {t('tabShampoos')}
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="all" className="mt-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                  {filteredProducts.map((product, index) => (
                    <div
                      key={product.id}
                      className={`animate-fade-in`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <ProductCard product={product} />
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="bar" className="mt-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                  {filteredProducts.map((product, index) => (
                    <div
                      key={product.id}
                      className={`animate-fade-in`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <ProductCard product={product} />
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="liquid" className="mt-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                  {filteredProducts.map((product, index) => (
                    <div
                      key={product.id}
                      className={`animate-fade-in`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <ProductCard product={product} />
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </PullToRefresh>
        ) : (
          <Tabs defaultValue="all" className="w-full" onValueChange={handleTabChange}>
            <div className="flex justify-center mb-8 md:mb-10 overflow-x-auto pb-2">
              <TabsList className="bg-sabone-dark-olive h-12 p-1 flex justify-center">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px] text-center"
                >
                  {t('tabAllProducts')}
                </TabsTrigger>
                <TabsTrigger
                  value="bar"
                  className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px] text-center"
                >
                  {t('tabSoapBars')}
                </TabsTrigger>
                <TabsTrigger
                  value="liquid"
                  className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px] text-center"
                >
                  {t('tabShampoos')}
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                {filteredProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className={`animate-fade-in`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <ProductCard product={product} />
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="bar" className="mt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                {filteredProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className={`animate-fade-in`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <ProductCard product={product} />
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="liquid" className="mt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                {filteredProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className={`animate-fade-in`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <ProductCard product={product} />
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </section>
  );
};

export default ProductGrid;
