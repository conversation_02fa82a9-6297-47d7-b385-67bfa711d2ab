import { Product } from '@/data/products';
import { SearchFilters, SortOption } from '@/contexts/SearchContext';

/**
 * Advanced search utilities for product filtering, sorting, and text search
 */

// Fuzzy search configuration
const FUZZY_SEARCH_CONFIG = {
  threshold: 0.6, // Similarity threshold (0-1, higher = more strict)
  maxDistance: 3, // Maximum edit distance for fuzzy matching
  minQueryLength: 2, // Minimum query length for fuzzy search
};

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Calculate similarity score between two strings (0-1, higher = more similar)
 */
function calculateSimilarity(str1: string, str2: string): number {
  const maxLength = Math.max(str1.length, str2.length);
  if (maxLength === 0) return 1;
  
  const distance = levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
  return 1 - distance / maxLength;
}

/**
 * Check if a string contains another string with fuzzy matching
 */
function fuzzyContains(haystack: string, needle: string): boolean {
  if (needle.length < FUZZY_SEARCH_CONFIG.minQueryLength) {
    return haystack.toLowerCase().includes(needle.toLowerCase());
  }

  const words = haystack.toLowerCase().split(/\s+/);
  const searchTerm = needle.toLowerCase();

  // Check for exact substring match first
  if (haystack.toLowerCase().includes(searchTerm)) {
    return true;
  }

  // Check for fuzzy match in individual words
  for (const word of words) {
    if (word.length >= needle.length - 1) {
      const similarity = calculateSimilarity(word, searchTerm);
      if (similarity >= FUZZY_SEARCH_CONFIG.threshold) {
        return true;
      }
    }
  }

  return false;
}

/**
 * Calculate search relevance score for a product
 */
function calculateRelevanceScore(product: Product, query: string): number {
  const searchTerm = query.toLowerCase().trim();
  if (!searchTerm) return 0;

  let score = 0;
  const weights = {
    name: 10,
    description: 5,
    category: 3,
    ingredients: 2,
    benefits: 2,
    scent: 1,
  };

  // Name matching (highest weight)
  if (product.name.toLowerCase().includes(searchTerm)) {
    score += weights.name * 2; // Exact match bonus
  } else if (fuzzyContains(product.name, searchTerm)) {
    score += weights.name;
  }

  // Description matching
  if (product.description.toLowerCase().includes(searchTerm)) {
    score += weights.description;
  } else if (fuzzyContains(product.description, searchTerm)) {
    score += weights.description * 0.7;
  }

  // Category matching
  if (product.category && product.category.toLowerCase().includes(searchTerm)) {
    score += weights.category;
  }

  // Ingredients matching
  for (const ingredient of product.ingredients) {
    if (ingredient.toLowerCase().includes(searchTerm)) {
      score += weights.ingredients;
    } else if (fuzzyContains(ingredient, searchTerm)) {
      score += weights.ingredients * 0.7;
    }
  }

  // Benefits matching
  for (const benefit of product.benefits) {
    if (benefit.toLowerCase().includes(searchTerm)) {
      score += weights.benefits;
    } else if (fuzzyContains(benefit, searchTerm)) {
      score += weights.benefits * 0.7;
    }
  }

  // Scent matching
  if (product.scent && product.scent.toLowerCase().includes(searchTerm)) {
    score += weights.scent;
  }

  // Boost score for featured products
  if (product.featured) {
    score *= 1.2;
  }

  // Boost score for in-stock products
  if (product.inStock) {
    score *= 1.1;
  }

  return score;
}

/**
 * Search products by text query with relevance scoring
 */
export function searchProducts(products: Product[], query: string): Product[] {
  if (!query.trim()) {
    return products;
  }

  const searchResults = products
    .map(product => ({
      product,
      score: calculateRelevanceScore(product, query),
    }))
    .filter(result => result.score > 0)
    .sort((a, b) => b.score - a.score)
    .map(result => result.product);

  return searchResults;
}

/**
 * Filter products based on search filters
 */
export function filterProducts(products: Product[], filters: SearchFilters): Product[] {
  return products.filter(product => {
    // Category filter
    if (filters.category && product.category !== filters.category) {
      return false;
    }

    // Price range filter
    if (filters.priceRange) {
      const { min, max } = filters.priceRange;
      if (product.price < min || product.price > max) {
        return false;
      }
    }

    // Stock filter
    if (filters.inStock !== undefined && product.inStock !== filters.inStock) {
      return false;
    }

    // Featured filter
    if (filters.featured !== undefined && product.featured !== filters.featured) {
      return false;
    }

    // Type filter
    if (filters.type && product.type !== filters.type) {
      return false;
    }

    // Ingredients filter
    if (filters.ingredients && filters.ingredients.length > 0) {
      const hasMatchingIngredient = filters.ingredients.some(ingredient =>
        product.ingredients.some(productIngredient =>
          productIngredient.toLowerCase().includes(ingredient.toLowerCase())
        )
      );
      if (!hasMatchingIngredient) {
        return false;
      }
    }

    // Benefits filter
    if (filters.benefits && filters.benefits.length > 0) {
      const hasMatchingBenefit = filters.benefits.some(benefit =>
        product.benefits.some(productBenefit =>
          productBenefit.toLowerCase().includes(benefit.toLowerCase())
        )
      );
      if (!hasMatchingBenefit) {
        return false;
      }
    }

    // Scent filter
    if (filters.scent && product.scent !== filters.scent) {
      return false;
    }

    return true;
  });
}

/**
 * Sort products based on sort option
 */
export function sortProducts(products: Product[], sortBy: SortOption): Product[] {
  const sortedProducts = [...products];

  sortedProducts.sort((a, b) => {
    let comparison = 0;

    switch (sortBy.field) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'price':
        comparison = a.price - b.price;
        break;
      case 'featured':
        comparison = (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
      default:
        comparison = 0;
    }

    return sortBy.direction === 'desc' ? -comparison : comparison;
  });

  return sortedProducts;
}

/**
 * Generate search suggestions based on query and products
 */
export function generateSearchSuggestions(
  products: Product[],
  query: string,
  maxSuggestions: number = 5
): string[] {
  if (!query.trim() || query.length < 2) {
    return [];
  }

  const suggestions = new Set<string>();
  const searchTerm = query.toLowerCase();

  // Add product names that match
  products.forEach(product => {
    if (product.name.toLowerCase().includes(searchTerm)) {
      suggestions.add(product.name);
    }
  });

  // Add ingredients that match
  products.forEach(product => {
    product.ingredients.forEach(ingredient => {
      if (ingredient.toLowerCase().includes(searchTerm)) {
        suggestions.add(ingredient);
      }
    });
  });

  // Add benefits that match
  products.forEach(product => {
    product.benefits.forEach(benefit => {
      if (benefit.toLowerCase().includes(searchTerm)) {
        suggestions.add(benefit);
      }
    });
  });

  // Add categories that match
  products.forEach(product => {
    if (product.category && product.category.toLowerCase().includes(searchTerm)) {
      suggestions.add(product.category);
    }
  });

  return Array.from(suggestions).slice(0, maxSuggestions);
}

/**
 * Highlight search terms in text
 */
export function highlightSearchTerms(text: string, query: string): string {
  if (!query.trim()) {
    return text;
  }

  const searchTerm = query.trim();
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  return text.replace(regex, '<mark class="bg-sabone-gold/30 text-sabone-charcoal">$1</mark>');
}

/**
 * Get popular search terms (mock implementation - in real app would come from analytics)
 */
export function getPopularSearchTerms(): string[] {
  return [
    'lavender soap',
    'natural shampoo',
    'moisturizing',
    'organic',
    'sensitive skin',
    'tea tree',
    'coconut oil',
    'charcoal',
  ];
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}
