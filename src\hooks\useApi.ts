/**
 * API Hooks
 * 
 * React hooks for data fetching, mutations, and state management
 * that integrate with the centralized API service layer.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { api, ApiResponse, ApiError, ApiRequestOptions } from '@/services/api';
import { toast } from 'sonner';

// Types for hook options
interface UseApiQueryOptions<T> extends Omit<UseQueryOptions<ApiResponse<T>, ApiError>, 'queryFn'> {
  apiOptions?: ApiRequestOptions;
}

interface UseApiMutationOptions<TData, TVariables> 
  extends Omit<UseMutationOptions<ApiResponse<TData>, ApiError, TVariables>, 'mutationFn'> {
  apiOptions?: ApiRequestOptions;
  invalidateQueries?: string[];
  showSuccessToast?: boolean;
  successMessage?: string;
}

interface UseApiStateOptions {
  initialData?: unknown;
  persistKey?: string;
  debounceMs?: number;
}

/**
 * Hook for GET requests with React Query integration
 */
export function useApiQuery<T = unknown>(
  key: string | string[],
  url: string,
  options: UseApiQueryOptions<T> = {}
) {
  const { apiOptions, ...queryOptions } = options;
  
  return useQuery<ApiResponse<T>, ApiError>({
    queryKey: Array.isArray(key) ? key : [key],
    queryFn: () => api.get<T>(url, apiOptions),
    ...queryOptions,
  });
}

/**
 * Hook for POST mutations
 */
export function useApiMutation<TData = unknown, TVariables = Record<string, unknown>>(
  url: string,
  options: UseApiMutationOptions<TData, TVariables> = {}
) {
  const queryClient = useQueryClient();
  const { 
    apiOptions, 
    invalidateQueries = [], 
    showSuccessToast = true,
    successMessage = 'Operation completed successfully',
    ...mutationOptions 
  } = options;

  return useMutation<ApiResponse<TData>, ApiError, TVariables>({
    mutationFn: (variables) => api.post<TData>(url, variables as Record<string, unknown>, apiOptions),
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      });

      // Show success toast
      if (showSuccessToast) {
        toast.success(data.message || successMessage);
      }

      // Call custom onSuccess if provided
      if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(data, variables, context);
      }
    },
    ...mutationOptions,
  });
}

/**
 * Hook for PUT mutations
 */
export function useApiUpdateMutation<TData = unknown, TVariables = Record<string, unknown>>(
  url: string,
  options: UseApiMutationOptions<TData, TVariables> = {}
) {
  const queryClient = useQueryClient();
  const { 
    apiOptions, 
    invalidateQueries = [], 
    showSuccessToast = true,
    successMessage = 'Updated successfully',
    ...mutationOptions 
  } = options;

  return useMutation<ApiResponse<TData>, ApiError, TVariables>({
    mutationFn: (variables) => api.put<TData>(url, variables as Record<string, unknown>, apiOptions),
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      });

      // Show success toast
      if (showSuccessToast) {
        toast.success(data.message || successMessage);
      }

      // Call custom onSuccess if provided
      if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(data, variables, context);
      }
    },
    ...mutationOptions,
  });
}

/**
 * Hook for DELETE mutations
 */
export function useApiDeleteMutation<TData = unknown>(
  url: string,
  options: UseApiMutationOptions<TData, string> = {}
) {
  const queryClient = useQueryClient();
  const { 
    apiOptions, 
    invalidateQueries = [], 
    showSuccessToast = true,
    successMessage = 'Deleted successfully',
    ...mutationOptions 
  } = options;

  return useMutation<ApiResponse<TData>, ApiError, string>({
    mutationFn: (id) => api.delete<TData>(`${url}/${id}`, apiOptions),
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      });

      // Show success toast
      if (showSuccessToast) {
        toast.success(data.message || successMessage);
      }

      // Call custom onSuccess if provided
      if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(data, variables, context);
      }
    },
    ...mutationOptions,
  });
}

/**
 * Hook for file uploads with progress tracking
 */
export function useApiUpload<TData = unknown>(
  url: string,
  options: {
    onProgress?: (progress: number) => void;
    fieldName?: string;
    apiOptions?: ApiRequestOptions;
    invalidateQueries?: string[];
    showSuccessToast?: boolean;
    successMessage?: string;
  } = {}
) {
  const queryClient = useQueryClient();
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  
  const { 
    onProgress,
    fieldName = 'file',
    apiOptions,
    invalidateQueries = [], 
    showSuccessToast = true,
    successMessage = 'File uploaded successfully'
  } = options;

  const upload = useCallback(async (file: File): Promise<ApiResponse<TData>> => {
    setIsUploading(true);
    setProgress(0);

    try {
      const result = await api.upload<TData>(url, file, {
        ...apiOptions,
        fieldName,
        onProgress: (uploadProgress) => {
          setProgress(uploadProgress);
          if (onProgress) {
            onProgress(uploadProgress);
          }
        },
      });

      // Invalidate related queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      });

      // Show success toast
      if (showSuccessToast) {
        toast.success(result.message || successMessage);
      }

      setProgress(100);
      return result;
    } catch (error) {
      setProgress(0);
      throw error;
    } finally {
      setIsUploading(false);
    }
  }, [url, fieldName, apiOptions, onProgress, invalidateQueries, showSuccessToast, successMessage, queryClient]);

  return {
    upload,
    progress,
    isUploading,
  };
}

/**
 * Hook for managing local state with optional persistence
 */
export function useApiState<T>(
  initialValue: T,
  options: UseApiStateOptions = {}
) {
  const { persistKey, debounceMs = 0 } = options;
  const [state, setState] = useState<T>(() => {
    if (persistKey) {
      try {
        const stored = localStorage.getItem(persistKey);
        return stored ? JSON.parse(stored) : initialValue;
      } catch {
        return initialValue;
      }
    }
    return initialValue;
  });

  const debounceTimeout = useRef<NodeJS.Timeout>();

  const updateState = useCallback((newState: T | ((prev: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prevState)
        : newState;

      // Persist to localStorage if key provided
      if (persistKey) {
        if (debounceTimeout.current) {
          clearTimeout(debounceTimeout.current);
        }

        if (debounceMs > 0) {
          debounceTimeout.current = setTimeout(() => {
            try {
              localStorage.setItem(persistKey, JSON.stringify(nextState));
            } catch (error) {
              console.warn('Failed to persist state:', error);
            }
          }, debounceMs);
        } else {
          try {
            localStorage.setItem(persistKey, JSON.stringify(nextState));
          } catch (error) {
            console.warn('Failed to persist state:', error);
          }
        }
      }

      return nextState;
    });
  }, [persistKey, debounceMs]);

  const clearState = useCallback(() => {
    setState(initialValue);
    if (persistKey) {
      localStorage.removeItem(persistKey);
    }
  }, [initialValue, persistKey]);

  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, []);

  return [state, updateState, clearState] as const;
}

/**
 * Hook for pagination with API integration
 */
export function useApiPagination<T = unknown>(
  baseUrl: string,
  options: {
    pageSize?: number;
    initialPage?: number;
    apiOptions?: ApiRequestOptions;
    queryOptions?: UseApiQueryOptions<T[]>;
  } = {}
) {
  const { pageSize = 10, initialPage = 1, apiOptions, queryOptions } = options;
  const [page, setPage] = useState(initialPage);
  
  const url = `${baseUrl}?page=${page}&limit=${pageSize}`;
  
  const query = useApiQuery<T[]>(
    ['pagination', baseUrl, page, pageSize],
    url,
    {
      ...queryOptions,
      apiOptions,
    }
  );

  const pagination = query.data?.pagination;

  const goToPage = useCallback((newPage: number) => {
    if (pagination && newPage >= 1 && newPage <= pagination.totalPages) {
      setPage(newPage);
    }
  }, [pagination]);

  const nextPage = useCallback(() => {
    if (pagination && page < pagination.totalPages) {
      setPage(prev => prev + 1);
    }
  }, [pagination, page]);

  const prevPage = useCallback(() => {
    if (page > 1) {
      setPage(prev => prev - 1);
    }
  }, [page]);

  return {
    ...query,
    page,
    pageSize,
    pagination,
    goToPage,
    nextPage,
    prevPage,
    hasNextPage: pagination ? page < pagination.totalPages : false,
    hasPrevPage: page > 1,
  };
}

/**
 * Hook for infinite scrolling with API integration
 */
export function useApiInfiniteQuery<T = unknown>(
  key: string | string[],
  baseUrl: string,
  options: {
    pageSize?: number;
    apiOptions?: ApiRequestOptions;
  } = {}
) {
  const { pageSize = 10, apiOptions } = options;
  const [data, setData] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);

  const loadMore = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    try {
      const url = `${baseUrl}?page=${page}&limit=${pageSize}`;
      const response = await api.get<T[]>(url, apiOptions);
      
      const newData = response.data;
      setData(prev => [...prev, ...newData]);
      
      // Check if there's more data
      if (response.pagination) {
        setHasMore(page < response.pagination.totalPages);
      } else {
        setHasMore(newData.length === pageSize);
      }
      
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Failed to load more data:', error);
      toast.error('Failed to load more data');
    } finally {
      setIsLoading(false);
    }
  }, [baseUrl, page, pageSize, isLoading, hasMore, apiOptions]);

  const reset = useCallback(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
  }, []);

  useEffect(() => {
    if (data.length === 0 && hasMore) {
      loadMore();
    }
  }, [data.length, hasMore, loadMore]);

  return {
    data,
    isLoading,
    hasMore,
    loadMore,
    reset,
  };
}

/**
 * Hook for API cache management
 */
export function useApiCache() {
  const queryClient = useQueryClient();

  const clearCache = useCallback(() => {
    api.clearCache();
    queryClient.clear();
    toast.success('Cache cleared successfully');
  }, [queryClient]);

  const invalidateQuery = useCallback((queryKey: string | string[]) => {
    const key = Array.isArray(queryKey) ? queryKey : [queryKey];
    queryClient.invalidateQueries({ queryKey: key });
  }, [queryClient]);

  const getCacheStats = useCallback(() => {
    return api.getCacheStats();
  }, []);

  return {
    clearCache,
    invalidateQuery,
    getCacheStats,
  };
} 