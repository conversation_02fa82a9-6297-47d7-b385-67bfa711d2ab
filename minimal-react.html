<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Minimal React Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #1c1c1c;
      color: #e5dcc5;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    #root {
      text-align: center;
      padding: 2rem;
      background-color: #2a2a1f;
      border-radius: 8px;
      border: 1px solid #c6a870;
      max-width: 600px;
      width: 100%;
    }
    h1 {
      color: #c6a870;
    }
    button {
      background-color: #c6a870;
      color: #1c1c1c;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px;
    }
    button:hover {
      background-color: #d8ba82;
    }
  </style>
  <!-- Load React -->
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <!-- Load Babel for JSX -->
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
  <div id="root"></div>

  <script type="text/babel">
    // Simple React component
    function App() {
      const [count, setCount] = React.useState(0);
      
      return (
        <div>
          <h1>Minimal React Test</h1>
          <p>If you can see this, React is working correctly!</p>
          <p>Count: {count}</p>
          <button onClick={() => setCount(count + 1)}>Increment</button>
          <button onClick={() => setCount(0)}>Reset</button>
          
          <div style={{ marginTop: '20px' }}>
            <h2>Troubleshooting Steps</h2>
            <ol style={{ textAlign: 'left' }}>
              <li>If this page works but your main app doesn't, the issue is specific to your app configuration.</li>
              <li>Check for JavaScript errors in the console when loading your main app.</li>
              <li>Verify that your app's entry point (root div) exists in the HTML.</li>
              <li>Check if your app's JavaScript bundle is loading correctly.</li>
              <li>Inspect network requests for any 404 errors on critical resources.</li>
            </ol>
          </div>
        </div>
      );
    }
    
    // Render the App component
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(<App />);
  </script>
</body>
</html>
