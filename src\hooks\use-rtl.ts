/**
 * Custom hook for RTL (Right-to-Left) layout detection and utilities
 * This hook provides functions and values related to RTL layout
 */

import { useEffect, useState, useMemo } from 'react';
import { useLocale } from 'next-intl';
import { isRTL } from '@/i18n/config';

export const useRTL = () => {
  const locale = useLocale(); // Get current locale from next-intl
  const [isRTLLayout, setIsRTLLayout] = useState(isRTL(locale));

  // Update RTL state when locale changes
  useEffect(() => {
    setIsRTLLayout(isRTL(locale));

    try {
      // Apply RTL settings to document
      if (document.documentElement) {
        document.documentElement.dir = isRTL(locale) ? 'rtl' : 'ltr';
        console.log(`RTL Hook: Set document direction to ${document.documentElement.dir}`);
      }

      // Add or remove RTL class on body
      if (document.body) {
        if (isRTL(locale)) {
          if (!document.body.classList.contains('rtl')) {
            document.body.classList.add('rtl');
            console.log('RTL Hook: Added rtl class to body');
          }
        } else {
          if (document.body.classList.contains('rtl')) {
            document.body.classList.remove('rtl');
            console.log('RTL Hook: Removed rtl class from body');
          }
        }
      }
    } catch (error) {
      console.error('Error updating RTL DOM attributes:', error);
    }
  }, [locale]);

  /**
   * Flip a direction value for RTL layout
   * @param direction The direction to flip ('left' or 'right')
   * @returns The flipped direction for RTL, or the original direction for LTR
   */
  const flipDirection = (direction: 'left' | 'right'): 'left' | 'right' => {
    if (!isRTLLayout) return direction;
    return direction === 'left' ? 'right' : 'left';
  };

  /**
   * Flip a horizontal position value for RTL layout
   * @param value The position value to flip
   * @returns The flipped position for RTL, or the original position for LTR
   */
  const flipPosition = (value: number): number => {
    if (!isRTLLayout) return value;
    return -value;
  };

  /**
   * Get the appropriate class name for RTL-aware spacing
   * @param direction The direction ('left' or 'right')
   * @param size The size of the spacing (1-12)
   * @returns The appropriate class name for the spacing
   */
  const getSpacingClass = (direction: 'left' | 'right', size: number): string => {
    const actualDirection = flipDirection(direction);
    return `m${actualDirection[0]}-${size}`;
  };

  // Create a memoized object to prevent unnecessary re-renders
  const rtlUtils = useMemo(() => ({
    isRTL: isRTLLayout,
    flipDirection,
    flipPosition,
    getSpacingClass,
    dir: isRTLLayout ? 'rtl' : 'ltr'
  }), [isRTLLayout]);

  return rtlUtils;
};
