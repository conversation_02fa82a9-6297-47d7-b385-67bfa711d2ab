// Mock for sonner toast library
import { jest } from '@jest/globals';

// Mock toast functions
const mockToastSuccess = jest.fn();
const mockToastError = jest.fn();
const mockToastWarning = jest.fn();
const mockToastInfo = jest.fn();
const mockToastLoading = jest.fn();
const mockToastPromise = jest.fn();
const mockToastCustom = jest.fn();
const mockToastDismiss = jest.fn();

// Mock toast object
export const toast = {
  success: mockToastSuccess,
  error: mockToastError,
  warning: mockToastWarning,
  info: mockToastInfo,
  loading: mockToastLoading,
  promise: mockToastPromise,
  custom: mockToastCustom,
  dismiss: mockToastDismiss,
  // Additional methods that might be used
  message: jest.fn(),
  default: jest.fn(),
};

// Mock Toaster component
export const Toaster = ({ children, ...props }) => {
  return <div data-testid="toaster" {...props}>{children}</div>;
};

// Mock Sonner component (alternative name)
export const Sonner = ({ children, ...props }) => {
  return <div data-testid="sonner" {...props}>{children}</div>;
};

// Helper functions for testing
export const __getMockToastFunctions = () => ({
  success: mockToastSuccess,
  error: mockToastError,
  warning: mockToastWarning,
  info: mockToastInfo,
  loading: mockToastLoading,
  promise: mockToastPromise,
  custom: mockToastCustom,
  dismiss: mockToastDismiss,
});

export const __clearMockToasts = () => {
  mockToastSuccess.mockClear();
  mockToastError.mockClear();
  mockToastWarning.mockClear();
  mockToastInfo.mockClear();
  mockToastLoading.mockClear();
  mockToastPromise.mockClear();
  mockToastCustom.mockClear();
  mockToastDismiss.mockClear();
};

export const __getToastCalls = () => ({
  success: mockToastSuccess.mock.calls,
  error: mockToastError.mock.calls,
  warning: mockToastWarning.mock.calls,
  info: mockToastInfo.mock.calls,
  loading: mockToastLoading.mock.calls,
  promise: mockToastPromise.mock.calls,
  custom: mockToastCustom.mock.calls,
  dismiss: mockToastDismiss.mock.calls,
});

// Mock toast with specific behavior for testing
export const __mockToastBehavior = (type, behavior) => {
  switch (type) {
    case 'success':
      mockToastSuccess.mockImplementation(behavior);
      break;
    case 'error':
      mockToastError.mockImplementation(behavior);
      break;
    case 'warning':
      mockToastWarning.mockImplementation(behavior);
      break;
    case 'info':
      mockToastInfo.mockImplementation(behavior);
      break;
    case 'loading':
      mockToastLoading.mockImplementation(behavior);
      break;
    case 'promise':
      mockToastPromise.mockImplementation(behavior);
      break;
    case 'custom':
      mockToastCustom.mockImplementation(behavior);
      break;
    case 'dismiss':
      mockToastDismiss.mockImplementation(behavior);
      break;
    default:
      console.warn(`Unknown toast type: ${type}`);
  }
};

// Mock toast with return values for testing
export const __mockToastReturnValue = (type, returnValue) => {
  switch (type) {
    case 'success':
      mockToastSuccess.mockReturnValue(returnValue);
      break;
    case 'error':
      mockToastError.mockReturnValue(returnValue);
      break;
    case 'warning':
      mockToastWarning.mockReturnValue(returnValue);
      break;
    case 'info':
      mockToastInfo.mockReturnValue(returnValue);
      break;
    case 'loading':
      mockToastLoading.mockReturnValue(returnValue);
      break;
    case 'promise':
      mockToastPromise.mockReturnValue(returnValue);
      break;
    case 'custom':
      mockToastCustom.mockReturnValue(returnValue);
      break;
    case 'dismiss':
      mockToastDismiss.mockReturnValue(returnValue);
      break;
    default:
      console.warn(`Unknown toast type: ${type}`);
  }
};

// Verify toast was called with specific message
export const __expectToastCalled = (type, message) => {
  const mockFunction = toast[type];
  if (!mockFunction) {
    throw new Error(`Unknown toast type: ${type}`);
  }
  
  expect(mockFunction).toHaveBeenCalledWith(message);
};

// Verify toast was called a specific number of times
export const __expectToastCalledTimes = (type, times) => {
  const mockFunction = toast[type];
  if (!mockFunction) {
    throw new Error(`Unknown toast type: ${type}`);
  }
  
  expect(mockFunction).toHaveBeenCalledTimes(times);
};

// Verify no toasts were called
export const __expectNoToasts = () => {
  Object.values(__getMockToastFunctions()).forEach(mockFn => {
    expect(mockFn).not.toHaveBeenCalled();
  });
};

export default {
  toast,
  Toaster,
  Sonner,
  __getMockToastFunctions,
  __clearMockToasts,
  __getToastCalls,
  __mockToastBehavior,
  __mockToastReturnValue,
  __expectToastCalled,
  __expectToastCalledTimes,
  __expectNoToasts,
};
