import { lazy } from 'react';

/**
 * Helper function for improved lazy loading with TypeScript
 * This allows us to maintain type safety with lazy imports
 * 
 * @param factory Function that imports the component
 * @param name Name of the exported component
 * @returns Lazy loaded component with proper types
 */
export function lazyImport<
  T extends React.ComponentType<any>,
  I extends { [K2 in K]: T },
  <PERSON> extends keyof I
>(factory: () => Promise<I>, name: K): I {
  return Object.create({
    [name]: lazy(() => factory().then((module) => ({ default: module[name] }))),
  });
}

/**
 * Helper function for lazy loading default exports
 * 
 * @param factory Function that imports the component
 * @returns Lazy loaded component with proper types
 */
export function lazyLoad<T extends React.ComponentType<any>>(
  factory: () => Promise<{ default: T }>
): T {
  return lazy(factory) as T;
}
