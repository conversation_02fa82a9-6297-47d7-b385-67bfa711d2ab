import { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { AlertTriangle, ShieldAlert, LogIn } from "lucide-react";

const DevAdminAccess = () => {
  const { isAuthenticated, user, login, setAsAdmin } = useAuth();
  const navigate = useNavigate();
  const isDevelopmentMode = import.meta.env.DEV && import.meta.env.VITE_SKIP_AUTH === 'true';

  useEffect(() => {
    // Safely add class to body
    try {
      if (document.body && !document.body.classList.contains("bg-sabone-charcoal")) {
        document.body.classList.add("bg-sabone-charcoal");
      }
    } catch (error) {
      console.error("Error adding class to body:", error);
    }

    return () => {
      // Safely remove class from body
      try {
        if (document.body && document.body.classList.contains("bg-sabone-charcoal")) {
          document.body.classList.remove("bg-sabone-charcoal");
        }
      } catch (error) {
        console.error("Error removing class from body:", error);
      }
    };
  }, []);

  const handleSetAsAdmin = () => {
    setAsAdmin();
    toast.success("Admin access granted! You can now access the admin dashboard.");
    setTimeout(() => {
      navigate("/account/admin");
    }, 1500);
  };

  if (!isDevelopmentMode) {
    return (
      <HelmetProvider>
        <Helmet>
          <title>Development Tools | Sabone</title>
          <meta name="robots" content="noindex, nofollow" />
        </Helmet>
        <div className="min-h-screen bg-sabone-charcoal flex flex-col">
          <Navbar />
          <main className="flex-grow py-16 px-4 sm:px-6 lg:px-8 flex items-center justify-center">
            <div className="max-w-md w-full bg-sabone-dark-olive/40 p-8 rounded-lg gold-border text-center">
              <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
              <h1 className="text-2xl font-playfair font-bold text-sabone-gold mb-4">
                Development Mode Only
              </h1>
              <p className="text-sabone-cream/80 mb-6">
                This page is only available in development mode with authentication bypass enabled.
              </p>
              <Button
                onClick={() => navigate("/")}
                className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
              >
                Return to Home
              </Button>
            </div>
          </main>
          <Footer />
        </div>
      </HelmetProvider>
    );
  }

  return (
    <HelmetProvider>
      <Helmet>
        <title>Development Admin Access | Sabone</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>
      <div className="min-h-screen bg-sabone-charcoal flex flex-col">
        <Navbar />
        <main className="flex-grow py-16 px-4 sm:px-6 lg:px-8 flex items-center justify-center">
          <div className="max-w-md w-full bg-sabone-dark-olive/40 p-8 rounded-lg gold-border">
            <div className="text-center mb-6">
              <ShieldAlert className="h-16 w-16 text-sabone-gold mx-auto mb-4" />
              <h1 className="text-2xl font-playfair font-bold text-sabone-gold mb-2">
                Development Admin Access
              </h1>
              <p className="text-sabone-cream/80">
                This page allows you to grant admin privileges to the development user.
              </p>
            </div>

            {!isAuthenticated ? (
              <div className="text-center">
                <p className="text-sabone-cream/80 mb-4">
                  You need to be logged in to access admin features.
                </p>
                <Button
                  onClick={login}
                  className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  Log In
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="bg-sabone-charcoal/50 p-4 rounded-md">
                  <h2 className="text-lg font-medium text-sabone-gold mb-2">Current User</h2>
                  <p className="text-sabone-cream/90">
                    <span className="text-sabone-cream/70">Name:</span> {user?.name}
                  </p>
                  <p className="text-sabone-cream/90">
                    <span className="text-sabone-cream/70">Email:</span> {user?.email}
                  </p>
                  <p className="text-sabone-cream/90">
                    <span className="text-sabone-cream/70">Role:</span>{" "}
                    <span className={user?.role === "admin" ? "text-green-500" : "text-sabone-cream/90"}>
                      {user?.role || "user"}
                    </span>
                  </p>
                </div>

                {user?.role === "admin" ? (
                  <div className="text-center">
                    <p className="text-green-500 mb-4">
                      You already have admin privileges!
                    </p>
                    <Button
                      onClick={() => navigate("/account/admin")}
                      className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                    >
                      <ShieldAlert className="h-4 w-4 mr-2" />
                      Go to Admin Dashboard
                    </Button>
                  </div>
                ) : (
                  <div className="text-center">
                    <p className="text-sabone-cream/80 mb-4">
                      Click the button below to grant admin privileges to your development user.
                    </p>
                    <Button
                      onClick={handleSetAsAdmin}
                      className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                    >
                      <ShieldAlert className="h-4 w-4 mr-2" />
                      Grant Admin Access
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
        <Footer />
      </div>
    </HelmetProvider>
  );
};

export default DevAdminAccess;
