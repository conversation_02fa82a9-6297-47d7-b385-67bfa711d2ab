import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  ogType?: 'website' | 'article' | 'product';
  ogImage?: string;
  ogImageAlt?: string;
  ogImageWidth?: number;
  ogImageHeight?: number;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  twitterSite?: string;
  twitterCreator?: string;
  jsonLd?: Record<string, any> | Record<string, any>[];
  noIndex?: boolean;
  children?: React.ReactNode;
}

/**
 * Default SEO values
 */
const defaults = {
  title: 'Sabone | Luxury Natural Soaps & Shampoos',
  description: 'Discover Sabone\'s luxury natural soaps and shampoos, handcrafted with premium ingredients for ritual purity and skin wellness.',
  ogType: 'website' as const,
  ogImage: '/images/sabone-og-image.jpg',
  ogImageAlt: 'Sabone luxury natural soaps and shampoos',
  ogImageWidth: 1200,
  ogImageHeight: 630,
  twitterCard: 'summary_large_image' as const,
  twitterSite: '@sabone_store',
  twitterCreator: '@sabone_store',
};

/**
 * SEO component for managing all metadata and structured data
 */
export default function SEO({
  title = defaults.title,
  description = defaults.description,
  canonical,
  ogType = defaults.ogType,
  ogImage = defaults.ogImage,
  ogImageAlt = defaults.ogImageAlt,
  ogImageWidth = defaults.ogImageWidth,
  ogImageHeight = defaults.ogImageHeight,
  twitterCard = defaults.twitterCard,
  twitterSite = defaults.twitterSite,
  twitterCreator = defaults.twitterCreator,
  jsonLd,
  noIndex = false,
  children,
}: SEOProps) {
  const router = useRouter();
  
  // Construct the canonical URL
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://sabone.store';
  const canonicalUrl = canonical 
    ? `${siteUrl}${canonical}` 
    : `${siteUrl}${router.asPath}`;
  
  // Ensure ogImage is an absolute URL
  const ogImageUrl = ogImage.startsWith('http') 
    ? ogImage 
    : `${siteUrl}${ogImage}`;
  
  // Format JSON-LD
  const jsonLdData = jsonLd 
    ? Array.isArray(jsonLd) 
      ? jsonLd 
      : [jsonLd]
    : [];
  
  // Add default Organization schema if not present
  if (!jsonLdData.some(item => item['@type'] === 'Organization')) {
    jsonLdData.push({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Sabone',
      url: siteUrl,
      logo: `${siteUrl}/images/logo.png`,
      sameAs: [
        'https://facebook.com/sabonestore',
        'https://instagram.com/sabone_store',
        'https://twitter.com/sabone_store',
      ],
    });
  }
  
  // Add WebSite schema if not present
  if (!jsonLdData.some(item => item['@type'] === 'WebSite')) {
    jsonLdData.push({
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'Sabone',
      url: siteUrl,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${siteUrl}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string',
      },
    });
  }
  
  // Add BreadcrumbList schema if on a nested page
  if (router.asPath !== '/' && !jsonLdData.some(item => item['@type'] === 'BreadcrumbList')) {
    const pathSegments = router.asPath.split('/').filter(Boolean);
    
    if (pathSegments.length > 0) {
      const breadcrumbItems = [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: siteUrl,
        },
        ...pathSegments.map((segment, index) => ({
          '@type': 'ListItem',
          position: index + 2,
          name: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
          item: `${siteUrl}/${pathSegments.slice(0, index + 1).join('/')}`,
        })),
      ];
      
      jsonLdData.push({
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: breadcrumbItems,
      });
    }
  }

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <link rel="canonical" href={canonicalUrl} />
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImageUrl} />
      <meta property="og:image:alt" content={ogImageAlt} />
      <meta property="og:image:width" content={ogImageWidth.toString()} />
      <meta property="og:image:height" content={ogImageHeight.toString()} />
      <meta property="og:site_name" content="Sabone" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content={twitterSite} />
      <meta name="twitter:creator" content={twitterCreator} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImageUrl} />
      <meta name="twitter:image:alt" content={ogImageAlt} />
      
      {/* JSON-LD Structured Data */}
      {jsonLdData.map((data, index) => (
        <script
          key={`jsonld-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
        />
      ))}
      
      {/* Additional head elements */}
      {children}
    </Head>
  );
}
