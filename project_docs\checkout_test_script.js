/**
 * Sabone.store Checkout Flow Test Script
 * 
 * This script contains functions to test the checkout flow with both Stripe and PayPal payment methods.
 * It's designed to be run in the browser console for manual testing.
 */

// Test configuration
const config = {
  testMode: true,
  logResults: true,
  captureScreenshots: false,
  testCards: {
    valid: '4242 4242 4242 4242',
    requires3DS: '4000 0000 0000 3220',
    declined: '4000 0000 0000 0002'
  },
  testUser: {
    email: '<EMAIL>',
    password: 'Test123!',
    name: 'Test User',
    phone: '************'
  },
  testAddress: {
    fullName: 'Test User',
    addressLine1: '123 Test St',
    addressLine2: 'Apt 4B',
    city: 'Testville',
    state: 'TS',
    zipCode: '12345',
    country: 'United States',
    phone: '************'
  }
};

/**
 * Test the complete checkout flow with Stripe payment
 */
async function testStripeCheckout() {
  console.log('🧪 Starting Stripe checkout test...');
  
  try {
    // Step 1: Add products to cart
    await addTestProductsToCart();
    console.log('✅ Added test products to cart');
    
    // Step 2: Navigate to checkout
    window.location.href = '/checkout';
    console.log('✅ Navigated to checkout page');
    
    // Step 3: Fill customer information form
    // Note: This needs to be called after the checkout page loads
    console.log('ℹ️ Please run fillCustomerForm() after the checkout page loads');
    
    // Step 4: Select Stripe payment method
    // Note: This needs to be called after filling the customer form
    console.log('ℹ️ Please run selectStripePayment() after filling the customer form');
    
    // Step 5: Complete Stripe payment
    // Note: This needs to be called after selecting Stripe payment
    console.log('ℹ️ Please run completeStripePayment() after selecting Stripe payment');
    
    return true;
  } catch (error) {
    console.error('❌ Stripe checkout test failed:', error);
    return false;
  }
}

/**
 * Test the complete checkout flow with PayPal payment
 */
async function testPayPalCheckout() {
  console.log('🧪 Starting PayPal checkout test...');
  
  try {
    // Step 1: Add products to cart
    await addTestProductsToCart();
    console.log('✅ Added test products to cart');
    
    // Step 2: Navigate to checkout
    window.location.href = '/checkout';
    console.log('✅ Navigated to checkout page');
    
    // Step 3: Fill customer information form
    // Note: This needs to be called after the checkout page loads
    console.log('ℹ️ Please run fillCustomerForm() after the checkout page loads');
    
    // Step 4: Select PayPal payment method
    // Note: This needs to be called after filling the customer form
    console.log('ℹ️ Please run selectPayPalPayment() after filling the customer form');
    
    // Step 5: Complete PayPal payment
    // Note: This needs to be called after selecting PayPal payment
    console.log('ℹ️ Please run initiatePayPalPayment() after selecting PayPal payment');
    
    return true;
  } catch (error) {
    console.error('❌ PayPal checkout test failed:', error);
    return false;
  }
}

/**
 * Add test products to cart
 */
async function addTestProductsToCart() {
  // Get the cart context
  const cartContext = window.__SABONE_CART_CONTEXT__;
  
  if (!cartContext) {
    throw new Error('Cart context not found. Make sure you are on the Sabone website.');
  }
  
  // Get some products from the inventory context
  const inventoryContext = window.__SABONE_INVENTORY_CONTEXT__;
  
  if (!inventoryContext) {
    throw new Error('Inventory context not found. Make sure you are on the Sabone website.');
  }
  
  // Add 2-3 products to the cart
  const products = inventoryContext.inventory.slice(0, 3);
  
  for (const product of products) {
    cartContext.addItem(product, 1);
    await new Promise(resolve => setTimeout(resolve, 300)); // Small delay between adds
  }
  
  return true;
}

/**
 * Fill the customer information form
 */
function fillCustomerForm() {
  console.log('🧪 Filling customer information form...');
  
  try {
    // Get the form elements
    const fullNameInput = document.querySelector('input[name="fullName"]');
    const emailInput = document.querySelector('input[name="email"]');
    const phoneInput = document.querySelector('input[name="phone"]');
    const addressLine1Input = document.querySelector('input[name="addressLine1"]');
    const addressLine2Input = document.querySelector('input[name="addressLine2"]');
    const cityInput = document.querySelector('input[name="city"]');
    const stateInput = document.querySelector('input[name="state"]');
    const zipCodeInput = document.querySelector('input[name="zipCode"]');
    const countryInput = document.querySelector('input[name="country"]');
    
    // Fill the form with test data
    if (fullNameInput) fullNameInput.value = config.testAddress.fullName;
    if (emailInput) emailInput.value = config.testUser.email;
    if (phoneInput) phoneInput.value = config.testAddress.phone;
    if (addressLine1Input) addressLine1Input.value = config.testAddress.addressLine1;
    if (addressLine2Input) addressLine2Input.value = config.testAddress.addressLine2;
    if (cityInput) cityInput.value = config.testAddress.city;
    if (stateInput) stateInput.value = config.testAddress.state;
    if (zipCodeInput) zipCodeInput.value = config.testAddress.zipCode;
    if (countryInput) countryInput.value = config.testAddress.country;
    
    // Trigger input events to update form state
    const inputs = [
      fullNameInput, emailInput, phoneInput, addressLine1Input, 
      addressLine2Input, cityInput, stateInput, zipCodeInput, countryInput
    ];
    
    for (const input of inputs) {
      if (input) {
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
      }
    }
    
    console.log('✅ Customer information form filled');
    return true;
  } catch (error) {
    console.error('❌ Error filling customer form:', error);
    return false;
  }
}

/**
 * Select Stripe payment method
 */
function selectStripePayment() {
  console.log('🧪 Selecting Stripe payment method...');
  
  try {
    // Find the credit card payment option
    const creditCardRadio = document.querySelector('input[value="credit_card"]');
    
    if (creditCardRadio) {
      creditCardRadio.checked = true;
      creditCardRadio.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ Stripe payment method selected');
      return true;
    } else {
      throw new Error('Credit card payment option not found');
    }
  } catch (error) {
    console.error('❌ Error selecting Stripe payment:', error);
    return false;
  }
}

/**
 * Select PayPal payment method
 */
function selectPayPalPayment() {
  console.log('🧪 Selecting PayPal payment method...');
  
  try {
    // Find the PayPal payment option
    const paypalRadio = document.querySelector('input[value="paypal"]');
    
    if (paypalRadio) {
      paypalRadio.checked = true;
      paypalRadio.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ PayPal payment method selected');
      return true;
    } else {
      throw new Error('PayPal payment option not found');
    }
  } catch (error) {
    console.error('❌ Error selecting PayPal payment:', error);
    return false;
  }
}

/**
 * Complete Stripe payment with test card
 */
function completeStripePayment(cardType = 'valid') {
  console.log(`🧪 Completing Stripe payment with ${cardType} card...`);
  
  try {
    // Get the card number from config
    const cardNumber = config.testCards[cardType];
    
    if (!cardNumber) {
      throw new Error(`Invalid card type: ${cardType}`);
    }
    
    // This is a placeholder - in a real test, we would need to interact with the Stripe iframe
    // which is challenging from the console. This would be better implemented in a Cypress test.
    console.log(`ℹ️ For manual testing, use the test card: ${cardNumber}`);
    console.log('ℹ️ Expiry: Any future date (e.g., 12/25)');
    console.log('ℹ️ CVC: Any 3 digits (e.g., 123)');
    console.log('ℹ️ ZIP: Any 5 digits (e.g., 12345)');
    
    // Find and click the submit button
    const submitButton = document.querySelector('button[type="submit"]');
    
    if (submitButton) {
      console.log('ℹ️ After entering card details, click the submit button or run:');
      console.log('submitButton.click()');
    } else {
      throw new Error('Submit button not found');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error completing Stripe payment:', error);
    return false;
  }
}

/**
 * Initiate PayPal payment
 */
function initiatePayPalPayment() {
  console.log('🧪 Initiating PayPal payment...');
  
  try {
    // Find the PayPal button
    const paypalButton = document.querySelector('.paypal-button');
    
    if (paypalButton) {
      console.log('ℹ️ Click the PayPal button to open the PayPal modal');
      console.log('ℹ️ Use your PayPal sandbox account to complete the payment');
      return true;
    } else {
      throw new Error('PayPal button not found');
    }
  } catch (error) {
    console.error('❌ Error initiating PayPal payment:', error);
    return false;
  }
}

/**
 * Verify order creation and email notifications
 */
function verifyOrderCreation() {
  console.log('🧪 Verifying order creation...');
  
  try {
    // Get the order context
    const orderContext = window.__SABONE_ORDER_CONTEXT__;
    
    if (!orderContext) {
      throw new Error('Order context not found. Make sure you are on the Sabone website.');
    }
    
    // Check if the latest order exists
    const latestOrder = orderContext.orders[orderContext.orders.length - 1];
    
    if (latestOrder) {
      console.log('✅ Order created successfully:', latestOrder);
      return true;
    } else {
      throw new Error('No orders found');
    }
  } catch (error) {
    console.error('❌ Error verifying order creation:', error);
    return false;
  }
}

// Export test functions for use in the browser console
window.saboneTests = {
  testStripeCheckout,
  testPayPalCheckout,
  addTestProductsToCart,
  fillCustomerForm,
  selectStripePayment,
  selectPayPalPayment,
  completeStripePayment,
  initiatePayPalPayment,
  verifyOrderCreation
};

console.log('🧪 Sabone checkout test script loaded. Use window.saboneTests to access test functions.');
