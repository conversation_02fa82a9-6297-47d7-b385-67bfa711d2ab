export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  rating: number; // 1-5 stars
  title: string;
  content: string;
  images?: string[]; // URLs to uploaded images
  isVerifiedPurchase: boolean;
  status: ReviewStatus; // For moderation
  createdAt: string;
  updatedAt: string;
  adminResponse?: AdminResponse;
  // Enhanced features
  helpfulVotes: number; // Count of helpful votes
  unhelpfulVotes: number; // Count of unhelpful votes
  userVotes: Record<string, 'helpful' | 'unhelpful'>; // Track user votes
  sentiment?: ReviewSentiment; // AI-analyzed sentiment
  moderationFlags?: ModerationFlag[]; // Content moderation flags
  responseCount?: number; // Number of admin responses
}

export type ReviewStatus = 'pending' | 'approved' | 'rejected';

export interface AdminResponse {
  content: string;
  respondedBy: string; // Admin user ID
  respondedAt: string;
}

export interface ReviewSummary {
  productId: string;
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  // Enhanced analytics
  verifiedPurchasePercentage: number;
  averageHelpfulnessRatio: number;
  sentimentDistribution: {
    positive: number;
    neutral: number;
    negative: number;
  };
  photoReviewCount: number;
  responseRate: number; // Percentage of reviews with admin responses
}

// New interfaces for enhanced features
export interface ReviewSentiment {
  score: number; // -1 to 1 (negative to positive)
  label: 'positive' | 'neutral' | 'negative';
  confidence: number; // 0 to 1
}

export interface ModerationFlag {
  type: 'spam' | 'inappropriate' | 'fake' | 'offensive';
  confidence: number;
  reason: string;
}

export interface ReviewAnalytics {
  totalReviews: number;
  averageRating: number;
  reviewsThisMonth: number;
  reviewGrowthRate: number; // Month over month
  topRatedProducts: Array<{
    productId: string;
    productName: string;
    averageRating: number;
    reviewCount: number;
  }>;
  sentimentTrends: Array<{
    date: string;
    positive: number;
    neutral: number;
    negative: number;
  }>;
  moderationStats: {
    pendingReviews: number;
    flaggedReviews: number;
    approvalRate: number;
  };
}

export type ReviewSortOption = 'newest' | 'oldest' | 'highest-rating' | 'lowest-rating';

export interface ReviewFilterOptions {
  minRating?: number;
  maxRating?: number;
  verifiedOnly?: boolean;
  withImages?: boolean;
  sortBy: ReviewSortOption;
}
