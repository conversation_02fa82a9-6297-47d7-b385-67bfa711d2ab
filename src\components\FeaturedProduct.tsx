import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { products } from "@/data/products";
import { Badge } from "@/components/ui/badge";
import { useCart } from "@/contexts/CartContext";
import OptimizedImage from "@/components/OptimizedImage";

// Featured product - Royal Oud Bar
const FEATURED_PRODUCT_ID = "royal-oud";

const FeaturedProduct = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { addItem } = useCart();

  // Find the featured product
  const featuredProduct = products.find(p => p.id === FEATURED_PRODUCT_ID);

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsVisible(true);
      }
    }, { threshold: 0.1 });

    const element = document.getElementById("featured-section");
    if (element) observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, []);

  if (!featuredProduct) return null;

  const handleAddToCart = () => {
    addItem(featuredProduct, 1);
  };

  return (
    <section id="featured-section" className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-sabone-dark-olive/20"></div>
      <div className="pattern-dot absolute inset-0 opacity-5"></div>

      <div className="max-w-7xl mx-auto relative">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-playfair font-bold text-sabone-gold">Ritual Highlight</h2>
            <div className="arabesque-divider w-24 mx-auto my-6"></div>
          </div>

          <div className="bg-sabone-dark-olive/40 rounded-lg gold-border overflow-hidden">
            <div className="grid md:grid-cols-2 gap-0">
              {/* Product Image */}
              <div className="relative h-full">
                <div className="absolute top-4 left-4 z-10">
                  <Badge className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/90">
                    Best Seller
                  </Badge>
                </div>
                <OptimizedImage
                  src={featuredProduct.image}
                  alt={featuredProduct.name}
                  className="w-full h-full max-h-[500px]"
                  objectFit="cover"
                  priority={true}
                />
                <div className="absolute inset-0 bg-gradient-to-r from-sabone-charcoal/50 to-transparent md:hidden"></div>
              </div>

              {/* Product Details */}
              <div className="p-8 md:p-10 flex flex-col justify-center">
                <h3 className="text-2xl md:text-3xl font-playfair font-bold text-sabone-gold mb-4">
                  {featuredProduct.name}
                </h3>

                <p className="text-sabone-cream/90 mb-6 text-lg">
                  {featuredProduct.description}
                </p>

                <div className="mb-6">
                  <h4 className="text-lg font-playfair text-sabone-gold mb-3">Key Ingredients</h4>
                  <ul className="grid grid-cols-2 gap-2">
                    {featuredProduct.ingredients.map((ingredient, index) => (
                      <li key={index} className="flex items-center text-sabone-cream/80">
                        <span className="text-sabone-gold mr-2">•</span>
                        <span>{ingredient}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-8">
                  <h4 className="text-lg font-playfair text-sabone-gold mb-3">Benefits</h4>
                  <ul className="grid grid-cols-2 gap-2">
                    {featuredProduct.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-center text-sabone-cream/80">
                        <span className="text-sabone-gold mr-2">•</span>
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto flex flex-col sm:flex-row gap-4">
                  <Button
                    onClick={handleAddToCart}
                    className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                  >
                    Add to Cart - ${featuredProduct.price.toFixed(2)}
                  </Button>

                  <Link to={`/product/${featuredProduct.id}`}>
                    <Button
                      variant="outline"
                      className="w-full sm:w-auto border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    >
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProduct;
