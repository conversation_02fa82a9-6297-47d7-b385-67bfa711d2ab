import React, { createContext, useContext, useState, useEffect } from 'react';
import { Product } from '@/data/products';
import { toast } from 'sonner';
import { useAuth } from './AuthContext';
import { useCart } from './CartContext';

// Define the context type
interface WishlistContextType {
  wishlistItems: Product[];
  wishlistCount: number;
  addToWishlist: (product: Product) => void;
  removeFromWishlist: (productId: string) => void;
  clearWishlist: () => void;
  isInWishlist: (productId: string) => boolean;
  moveToCart: (productId: string) => void;
}

// Create the context
const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

// Storage key for wishlist
const WISHLIST_STORAGE_KEY = 'sabone-wishlist';

// Provider component
export const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [wishlistItems, setWishlistItems] = useState<Product[]>([]);
  const { isAuthenticated, user } = useAuth();
  const { addItem } = useCart();

  // Get user-specific storage key
  const getUserWishlistKey = () => {
    return isAuthenticated && user?.sub
      ? `${WISHLIST_STORAGE_KEY}-${user.sub}`
      : WISHLIST_STORAGE_KEY;
  };

  // Load wishlist from localStorage on mount and when user changes
  useEffect(() => {
    const loadWishlist = () => {
      try {
        const storageKey = getUserWishlistKey();
        const storedWishlist = localStorage.getItem(storageKey);

        if (storedWishlist) {
          setWishlistItems(JSON.parse(storedWishlist));
        }
      } catch (error) {
        console.error('Error loading wishlist from localStorage:', error);
        setWishlistItems([]);
      }
    };

    loadWishlist();
  }, [isAuthenticated, user?.sub]);

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    try {
      const storageKey = getUserWishlistKey();
      localStorage.setItem(storageKey, JSON.stringify(wishlistItems));
    } catch (error) {
      console.error('Error saving wishlist to localStorage:', error);
    }
  }, [wishlistItems, isAuthenticated, user?.sub]);

  // Add a product to the wishlist
  const addToWishlist = (product: Product) => {
    if (isInWishlist(product.id)) {
      toast.info(`${product.name} is already in your wishlist`);
      return;
    }

    setWishlistItems(prevItems => [...prevItems, product]);
    toast.success(`${product.name} added to your wishlist`);
  };

  // Remove a product from the wishlist
  const removeFromWishlist = (productId: string) => {
    const product = wishlistItems.find(item => item.id === productId);
    if (!product) return;

    setWishlistItems(prevItems => prevItems.filter(item => item.id !== productId));
    toast.success(`${product.name} removed from your wishlist`);
  };

  // Clear the entire wishlist
  const clearWishlist = () => {
    setWishlistItems([]);
    toast.success('Wishlist cleared');
  };

  // Check if a product is in the wishlist
  const isInWishlist = (productId: string) => {
    return wishlistItems.some(item => item.id === productId);
  };

  // Move an item from wishlist to cart
  const moveToCart = (productId: string) => {
    const product = wishlistItems.find(item => item.id === productId);
    if (!product) return;

    // Add to cart
    addItem(product, 1);

    // Remove from wishlist
    removeFromWishlist(productId);

    toast.success(`${product.name} moved to your cart`);
  };

  return (
    <WishlistContext.Provider
      value={{
        wishlistItems,
        wishlistCount: wishlistItems.length,
        addToWishlist,
        removeFromWishlist,
        clearWishlist,
        isInWishlist,
        moveToCart
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
};

// Custom hook to use the wishlist context
export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
