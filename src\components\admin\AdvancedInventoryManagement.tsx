import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  Plus,
  BarChart3,
  DollarSign
} from 'lucide-react';
import { useProducts } from '@/contexts/ProductContext';
import { useInventory } from '@/contexts/InventoryContext';
import { inventoryAnalyticsService, InventoryAnalytics, StockAlert } from '@/services/inventoryAnalyticsService';
import { restockInventory } from '@/services/inventoryService';
import { logger } from '@/utils/logger';
import { toast } from 'sonner';

const AdvancedInventoryManagement: React.FC = () => {
  const { products } = useProducts();
  const { inventory, refreshInventory } = useInventory();
  const [analytics, setAnalytics] = useState<InventoryAnalytics | null>(null);
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, [products, inventory]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const analyticsData = inventoryAnalyticsService.getInventoryAnalytics(products);
      const activeAlerts = inventoryAnalyticsService.generateStockAlerts(products);

      setAnalytics(analyticsData);
      setAlerts(activeAlerts);

      logger.userAction('inventory_analytics_loaded', {
        totalProducts: analyticsData.totalProducts,
        alertCount: activeAlerts.length
      });
    } catch (error) {
      logger.error('Failed to load inventory analytics', error);
      toast.error('Failed to load inventory analytics');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    await refreshInventory();
    await loadAnalyticsData();
    toast.success('Inventory data refreshed');
  };

  const handleQuickRestock = async (productId: string, quantity: number) => {
    try {
      const result = restockInventory(productId, quantity);
      if (result) {
        await refreshInventory();
        await loadAnalyticsData();
        toast.success(`Restocked ${quantity} units successfully`);

        logger.userAction('quick_restock', {
          productId,
          quantity
        });
      } else {
        toast.error('Failed to restock inventory');
      }
    } catch (error) {
      logger.error('Failed to restock inventory', error);
      toast.error('Failed to restock inventory');
    }
  };

  const handleAcknowledgeAlert = (alertId: string) => {
    const success = inventoryAnalyticsService.acknowledgeAlert(alertId);
    if (success) {
      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
      toast.success('Alert acknowledged');
    }
  };

  const exportAnalytics = () => {
    if (!analytics) return;

    const csvData = [
      ['Metric', 'Value'],
      ['Total Products', analytics.totalProducts.toString()],
      ['Total Stock Value', `$${analytics.totalStockValue.toFixed(2)}`],
      ['Low Stock Count', analytics.lowStockCount.toString()],
      ['Out of Stock Count', analytics.outOfStockCount.toString()],
      ['Average Stock Level', analytics.averageStockLevel.toFixed(1)],
      ['Stock Turnover Rate', analytics.stockTurnoverRate.toFixed(2)],
      [''],
      ['Top Selling Products', '', '', ''],
      ['Product Name', 'Current Stock', 'Sales Velocity', 'Days of Stock'],
      ...analytics.topSellingProducts.map(product => [
        product.productName,
        product.currentStock.toString(),
        product.salesVelocity.toFixed(1),
        product.daysOfStock.toFixed(1)
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory-analytics-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    logger.userAction('inventory_analytics_exported');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-playfair font-bold text-sabone-gold">
            Advanced Inventory Management
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
              <div className="animate-pulse">
                <div className="h-4 bg-sabone-gold/20 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-sabone-gold/20 rounded w-1/2"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-sabone-cream/60">Failed to load inventory analytics</p>
        <Button onClick={loadAnalyticsData} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-playfair font-bold text-sabone-gold">
          Advanced Inventory Management
        </h2>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={exportAnalytics}
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Total Products</p>
              <p className="text-2xl font-bold text-sabone-gold">
                {analytics.totalProducts}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Stock Value</p>
              <p className="text-2xl font-bold text-sabone-gold">
                ${analytics.totalStockValue.toFixed(0)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-yellow-500 mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Low Stock Items</p>
              <p className="text-2xl font-bold text-yellow-500">
                {analytics.lowStockCount}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Turnover Rate</p>
              <p className="text-2xl font-bold text-sabone-gold">
                {analytics.stockTurnoverRate.toFixed(1)}x
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Active Alerts */}
      {alerts.length > 0 && (
        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold">
              Active Alerts ({alerts.length})
            </h3>
          </div>
          <div className="space-y-3">
            {alerts.slice(0, 5).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 bg-sabone-charcoal/30 rounded">
                <div className="flex items-center">
                  <AlertTriangle className={`h-5 w-5 mr-3 ${
                    alert.severity === 'critical' ? 'text-red-500' :
                    alert.severity === 'high' ? 'text-orange-500' :
                    alert.severity === 'medium' ? 'text-yellow-500' : 'text-blue-500'
                  }`} />
                  <div>
                    <p className="font-medium text-sabone-cream">{alert.message}</p>
                    <p className="text-sm text-sabone-cream/60">
                      {alert.productName} - Current: {alert.currentStock}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={`${
                    alert.severity === 'critical' ? 'bg-red-500/20 text-red-500 border-red-500/30' :
                    alert.severity === 'high' ? 'bg-orange-500/20 text-orange-500 border-orange-500/30' :
                    alert.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-500 border-yellow-500/30' :
                    'bg-blue-500/20 text-blue-500 border-blue-500/30'
                  }`}>
                    {alert.severity}
                  </Badge>
                  <Button
                    onClick={() => handleAcknowledgeAlert(alert.id)}
                    size="sm"
                    variant="outline"
                    className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                  >
                    Acknowledge
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Detailed Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3 bg-sabone-dark-olive/60">
          <TabsTrigger value="overview" className="data-[state=active]:bg-sabone-gold/20">
            Overview
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-sabone-gold/20">
            Performance
          </TabsTrigger>
          <TabsTrigger value="actions" className="data-[state=active]:bg-sabone-gold/20">
            Quick Actions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Selling Products */}
            <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
              <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">
                Top Selling Products
              </h3>
              <div className="space-y-3">
                {analytics.topSellingProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-sabone-charcoal/30 rounded">
                    <div>
                      <p className="font-medium text-sabone-cream">{product.productName}</p>
                      <p className="text-sm text-sabone-cream/60">
                        Stock: {product.currentStock} | Velocity: {product.salesVelocity.toFixed(1)}/day
                      </p>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-green-500 font-medium">
                        {product.daysOfStock.toFixed(0)} days
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Slow Moving Products */}
            <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
              <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">
                Slow Moving Products
              </h3>
              <div className="space-y-3">
                {analytics.slowMovingProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-sabone-charcoal/30 rounded">
                    <div>
                      <p className="font-medium text-sabone-cream">{product.productName}</p>
                      <p className="text-sm text-sabone-cream/60">
                        Stock: {product.currentStock} | Velocity: {product.salesVelocity.toFixed(1)}/day
                      </p>
                    </div>
                    <div className="flex items-center">
                      <TrendingDown className="h-4 w-4 text-orange-500 mr-2" />
                      <span className="text-orange-500 font-medium">
                        {product.daysOfStock > 999 ? '∞' : product.daysOfStock.toFixed(0)} days
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">
              Performance Metrics
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-sabone-gold mb-2">
                  {analytics.averageStockLevel.toFixed(1)}
                </div>
                <p className="text-sabone-cream/80">Average Stock Level</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-sabone-gold mb-2">
                  {analytics.stockTurnoverRate.toFixed(2)}x
                </div>
                <p className="text-sabone-cream/80">Stock Turnover Rate</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-sabone-gold mb-2">
                  ${(analytics.totalStockValue / analytics.totalProducts).toFixed(0)}
                </div>
                <p className="text-sabone-cream/80">Avg Value per Product</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="actions" className="space-y-6">
          <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">
              Quick Restock Actions
            </h3>
            <div className="space-y-4">
              {inventory.filter(item => item.stockQuantity <= item.lowStockThreshold).map((item) => {
                const product = products.find(p => p.id === item.productId);
                if (!product) return null;

                return (
                  <div key={item.productId} className="flex items-center justify-between p-4 bg-sabone-charcoal/30 rounded">
                    <div>
                      <p className="font-medium text-sabone-cream">{product.name}</p>
                      <p className="text-sm text-sabone-cream/60">
                        Current Stock: {item.stockQuantity} | Threshold: {item.lowStockThreshold}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => handleQuickRestock(item.productId, 10)}
                        size="sm"
                        variant="outline"
                        className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        +10
                      </Button>
                      <Button
                        onClick={() => handleQuickRestock(item.productId, 25)}
                        size="sm"
                        variant="outline"
                        className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        +25
                      </Button>
                      <Button
                        onClick={() => handleQuickRestock(item.productId, 50)}
                        size="sm"
                        className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        +50
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedInventoryManagement;
