/* OptimizedImage component styles */

.container {
  position: relative;
  overflow: hidden;
}

.containerWithAspectRatio {
  position: relative;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  transition: opacity 0.5s ease-in-out;
  object-fit: cover;
}

.imageLoading {
  opacity: 0;
}

.imageLoaded {
  opacity: 1;
}

.imageError {
  opacity: 0.8;
}

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(20px);
  transform: scale(1.1);
  transition: opacity 0.5s ease-in-out;
  object-fit: cover;
}

.placeholderVisible {
  opacity: 1;
}

.placeholderHidden {
  opacity: 0;
}

/* Responsive container utilities */
.responsiveContainer {
  width: 100%;
  height: auto;
}

.fixedContainer[data-width] {
  width: attr(data-width px, 100%);
}

.fixedContainer[data-height] {
  height: attr(data-height px, auto);
}

/* Dynamic aspect ratio support */
.containerWithAspectRatio[data-aspect-ratio] {
  aspect-ratio: attr(data-aspect-ratio);
}

/* Object fit support for images inside */
.containerWithObjectFit img {
  object-fit: cover;
}

.containerWithObjectFit[data-object-fit="contain"] img {
  object-fit: contain;
}

.containerWithObjectFit[data-object-fit="fill"] img {
  object-fit: fill;
}

.containerWithObjectFit[data-object-fit="none"] img {
  object-fit: none;
}

.containerWithObjectFit[data-object-fit="scale-down"] img {
  object-fit: scale-down;
}

/* Performance optimizations */
.optimized {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Accessibility improvements */
.skeleton {
  position: absolute;
  inset: 0;
  background-color: var(--sabone-dark-olive, #6b7280);
  border-radius: 0.375rem;
}
