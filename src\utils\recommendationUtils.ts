import { Product } from '@/data/products';

// Types for recommendation system
export interface UserBehavior {
  userId?: string;
  sessionId: string;
  productViews: ProductView[];
  cartAdditions: CartAddition[];
  purchases: Purchase[];
  timestamp: number;
}

export interface ProductView {
  productId: string;
  timestamp: number;
  duration?: number; // Time spent viewing in seconds
}

export interface CartAddition {
  productId: string;
  quantity: number;
  timestamp: number;
}

export interface Purchase {
  productId: string;
  quantity: number;
  timestamp: number;
  orderId?: string;
}

export interface RecommendationScore {
  productId: string;
  score: number;
  reason: string;
  algorithm: 'collaborative' | 'content-based' | 'popularity' | 'recently-viewed';
}

export interface RecommendationResult {
  recommendations: RecommendationScore[];
  metadata: {
    algorithm: string;
    timestamp: number;
    userId?: string;
    sessionId: string;
  };
}

// Storage keys for user behavior tracking
export const STORAGE_KEYS = {
  USER_BEHAVIOR: 'sabone-user-behavior',
  RECENTLY_VIEWED: 'sabone-recently-viewed',
  RECOMMENDATION_CACHE: 'sabone-recommendation-cache',
} as const;

// Recommendation configuration
export const RECOMMENDATION_CONFIG = {
  MAX_RECOMMENDATIONS: 6,
  MAX_RECENTLY_VIEWED: 10,
  CACHE_DURATION: 30 * 60 * 1000, // 30 minutes
  VIEW_DURATION_THRESHOLD: 5, // seconds
  SIMILARITY_THRESHOLD: 0.3,
} as const;

/**
 * Calculate content-based similarity between two products
 */
export const calculateProductSimilarity = (product1: Product, product2: Product): number => {
  if (product1.id === product2.id) return 0;

  let score = 0;
  let factors = 0;

  // Type similarity (high weight)
  if (product1.type === product2.type) {
    score += 0.4;
  }
  factors += 0.4;

  // Ingredient similarity
  const commonIngredients = product1.ingredients.filter(ingredient =>
    product2.ingredients.some(ing => 
      ing.toLowerCase().includes(ingredient.toLowerCase()) ||
      ingredient.toLowerCase().includes(ing.toLowerCase())
    )
  );
  const ingredientScore = commonIngredients.length / Math.max(product1.ingredients.length, product2.ingredients.length);
  score += ingredientScore * 0.3;
  factors += 0.3;

  // Benefits similarity
  const commonBenefits = product1.benefits.filter(benefit =>
    product2.benefits.some(ben => 
      ben.toLowerCase().includes(benefit.toLowerCase()) ||
      benefit.toLowerCase().includes(ben.toLowerCase())
    )
  );
  const benefitScore = commonBenefits.length / Math.max(product1.benefits.length, product2.benefits.length);
  score += benefitScore * 0.2;
  factors += 0.2;

  // Price similarity (closer prices get higher scores)
  const priceDiff = Math.abs(product1.price - product2.price);
  const maxPrice = Math.max(product1.price, product2.price);
  const priceScore = 1 - (priceDiff / maxPrice);
  score += priceScore * 0.1;
  factors += 0.1;

  return factors > 0 ? score / factors : 0;
};

/**
 * Get content-based recommendations for a product
 */
export const getContentBasedRecommendations = (
  targetProduct: Product,
  allProducts: Product[],
  maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECOMMENDATIONS
): RecommendationScore[] => {
  const similarities = allProducts
    .filter(product => product.id !== targetProduct.id)
    .map(product => ({
      productId: product.id,
      score: calculateProductSimilarity(targetProduct, product),
      reason: `Similar to ${targetProduct.name}`,
      algorithm: 'content-based' as const
    }))
    .filter(item => item.score >= RECOMMENDATION_CONFIG.SIMILARITY_THRESHOLD)
    .sort((a, b) => b.score - a.score)
    .slice(0, maxRecommendations);

  return similarities;
};

/**
 * Get recently viewed products recommendations
 */
export const getRecentlyViewedRecommendations = (
  sessionId: string,
  allProducts: Product[],
  maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECENTLY_VIEWED
): RecommendationScore[] => {
  try {
    const recentlyViewed = JSON.parse(
      localStorage.getItem(STORAGE_KEYS.RECENTLY_VIEWED) || '[]'
    ) as ProductView[];

    const recentProducts = recentlyViewed
      .filter(view => Date.now() - view.timestamp < 24 * 60 * 60 * 1000) // Last 24 hours
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, maxRecommendations)
      .map(view => {
        const product = allProducts.find(p => p.id === view.productId);
        return product ? {
          productId: view.productId,
          score: 1 - ((Date.now() - view.timestamp) / (24 * 60 * 60 * 1000)), // Decay over time
          reason: 'Recently viewed',
          algorithm: 'recently-viewed' as const
        } : null;
      })
      .filter(Boolean) as RecommendationScore[];

    return recentProducts;
  } catch (error) {
    console.error('Error getting recently viewed recommendations:', error);
    return [];
  }
};

/**
 * Get popularity-based recommendations
 */
export const getPopularityRecommendations = (
  allProducts: Product[],
  maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECOMMENDATIONS
): RecommendationScore[] => {
  // For now, we'll use a simple popularity algorithm based on product features
  // In a real app, this would be based on actual sales/view data
  const popularityScores = allProducts.map(product => {
    let score = 0;
    
    // Higher score for products with more benefits
    score += product.benefits.length * 0.1;
    
    // Higher score for products with more ingredients (complexity)
    score += product.ingredients.length * 0.05;
    
    // Add some randomness to simulate real popularity data
    score += Math.random() * 0.3;
    
    return {
      productId: product.id,
      score,
      reason: 'Popular choice',
      algorithm: 'popularity' as const
    };
  })
  .sort((a, b) => b.score - a.score)
  .slice(0, maxRecommendations);

  return popularityScores;
};

/**
 * Track user product view
 */
export const trackProductView = (productId: string, duration?: number): void => {
  try {
    const view: ProductView = {
      productId,
      timestamp: Date.now(),
      duration
    };

    // Update recently viewed
    const recentlyViewed = JSON.parse(
      localStorage.getItem(STORAGE_KEYS.RECENTLY_VIEWED) || '[]'
    ) as ProductView[];

    // Remove existing view of the same product
    const filteredViews = recentlyViewed.filter(v => v.productId !== productId);
    
    // Add new view at the beginning
    const updatedViews = [view, ...filteredViews].slice(0, RECOMMENDATION_CONFIG.MAX_RECENTLY_VIEWED);
    
    localStorage.setItem(STORAGE_KEYS.RECENTLY_VIEWED, JSON.stringify(updatedViews));

    // Update user behavior
    const sessionId = getSessionId();
    const userBehavior = getUserBehavior(sessionId);
    userBehavior.productViews.push(view);
    
    // Keep only recent views
    userBehavior.productViews = userBehavior.productViews
      .filter(v => Date.now() - v.timestamp < 24 * 60 * 60 * 1000)
      .slice(-50); // Keep last 50 views
    
    saveUserBehavior(userBehavior);
  } catch (error) {
    console.error('Error tracking product view:', error);
  }
};

/**
 * Track cart addition
 */
export const trackCartAddition = (productId: string, quantity: number): void => {
  try {
    const sessionId = getSessionId();
    const userBehavior = getUserBehavior(sessionId);
    
    const cartAddition: CartAddition = {
      productId,
      quantity,
      timestamp: Date.now()
    };
    
    userBehavior.cartAdditions.push(cartAddition);
    saveUserBehavior(userBehavior);
  } catch (error) {
    console.error('Error tracking cart addition:', error);
  }
};

/**
 * Get or create session ID
 */
export const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('sabone-session-id');
  if (!sessionId) {
    sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('sabone-session-id', sessionId);
  }
  return sessionId;
};

/**
 * Get user behavior data
 */
export const getUserBehavior = (sessionId: string): UserBehavior => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.USER_BEHAVIOR);
    if (stored) {
      const behavior = JSON.parse(stored) as UserBehavior;
      if (behavior.sessionId === sessionId) {
        return behavior;
      }
    }
  } catch (error) {
    console.error('Error getting user behavior:', error);
  }

  // Return new behavior object
  return {
    sessionId,
    productViews: [],
    cartAdditions: [],
    purchases: [],
    timestamp: Date.now()
  };
};

/**
 * Save user behavior data
 */
export const saveUserBehavior = (behavior: UserBehavior): void => {
  try {
    behavior.timestamp = Date.now();
    localStorage.setItem(STORAGE_KEYS.USER_BEHAVIOR, JSON.stringify(behavior));
  } catch (error) {
    console.error('Error saving user behavior:', error);
  }
};

/**
 * Clear old user behavior data
 */
export const clearOldBehaviorData = (): void => {
  try {
    const sessionId = getSessionId();
    const behavior = getUserBehavior(sessionId);
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    // Filter out old data
    behavior.productViews = behavior.productViews.filter(v => v.timestamp > oneDayAgo);
    behavior.cartAdditions = behavior.cartAdditions.filter(c => c.timestamp > oneDayAgo);
    behavior.purchases = behavior.purchases.filter(p => p.timestamp > oneDayAgo);
    
    saveUserBehavior(behavior);
  } catch (error) {
    console.error('Error clearing old behavior data:', error);
  }
};
