import { getDatabaseInstance } from '../config.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Order repository for database operations related to orders
 */
class OrderRepository {
  constructor() {
    this.collectionName = 'orders';
    this.db = null;
    this.collection = null;
  }

  /**
   * Initializes the repository
   * @returns {Promise<void>}
   */
  async init() {
    if (!this.db) {
      this.db = await getDatabaseInstance();
      this.collection = this.db.collection(this.collectionName);
      console.log(`OrderRepository initialized with collection: ${this.collectionName}`);
    }
  }

  /**
   * Creates a new order
   * @param {Object} orderData - Order data
   * @returns {Promise<Object>} Created order
   */
  async createOrder(orderData) {
    await this.init();

    const order = {
      ...orderData,
      id: orderData.id || uuidv4(),
      createdAt: orderData.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      const result = await this.collection.insertOne(order);
      console.log(`Order created with ID: ${order.id}`);
      return { ...order, _id: result.insertedId };
    } catch (error) {
      console.error('Error creating order:', error);
      throw new Error(`Failed to create order: ${error.message}`);
    }
  }

  /**
   * Gets an order by ID
   * @param {string} orderId - Order ID
   * @returns {Promise<Object|null>} Order or null if not found
   */
  async getOrderById(orderId) {
    await this.init();

    try {
      return await this.collection.findOne({ id: orderId });
    } catch (error) {
      console.error(`Error getting order ${orderId}:`, error);
      throw new Error(`Failed to get order: ${error.message}`);
    }
  }

  /**
   * Gets orders by user ID
   * @param {string} userId - User ID
   * @param {Object} options - Query options (limit, skip, sort)
   * @returns {Promise<Array<Object>>} Orders
   */
  async getOrdersByUserId(userId, options = {}) {
    await this.init();

    const { 
      limit = 10, 
      skip = 0, 
      sort = { createdAt: -1 } 
    } = options;

    try {
      const query = { userId };
      const cursor = this.collection.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);
      
      return await cursor.toArray();
    } catch (error) {
      console.error(`Error getting orders for user ${userId}:`, error);
      throw new Error(`Failed to get user orders: ${error.message}`);
    }
  }

  /**
   * Updates an order
   * @param {string} orderId - Order ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated order or null if not found
   */
  async updateOrder(orderId, updateData) {
    await this.init();

    // Remove fields that shouldn't be updated directly
    const { id, createdAt, ...safeUpdateData } = updateData;

    try {
      const result = await this.collection.updateOne(
        { id: orderId },
        { 
          $set: {
            ...safeUpdateData,
            updatedAt: new Date().toISOString(),
          }
        }
      );

      if (result.modifiedCount === 0) {
        console.warn(`No order found with ID ${orderId} to update`);
        return null;
      }

      return this.getOrderById(orderId);
    } catch (error) {
      console.error(`Error updating order ${orderId}:`, error);
      throw new Error(`Failed to update order: ${error.message}`);
    }
  }

  /**
   * Updates order status
   * @param {string} orderId - Order ID
   * @param {string} status - New status
   * @param {string} paymentStatus - New payment status (optional)
   * @returns {Promise<Object|null>} Updated order or null if not found
   */
  async updateOrderStatus(orderId, status, paymentStatus = null) {
    await this.init();

    const updateData = {
      status,
      updatedAt: new Date().toISOString(),
    };

    if (paymentStatus) {
      updateData.paymentStatus = paymentStatus;
    }

    try {
      const result = await this.collection.updateOne(
        { id: orderId },
        { $set: updateData }
      );

      if (result.modifiedCount === 0) {
        console.warn(`No order found with ID ${orderId} to update status`);
        return null;
      }

      console.log(`Order ${orderId} status updated to ${status}`);
      return this.getOrderById(orderId);
    } catch (error) {
      console.error(`Error updating order status ${orderId}:`, error);
      throw new Error(`Failed to update order status: ${error.message}`);
    }
  }

  /**
   * Deletes an order (soft delete)
   * @param {string} orderId - Order ID
   * @returns {Promise<boolean>} True if deleted, false if not found
   */
  async deleteOrder(orderId) {
    await this.init();

    try {
      // Soft delete - mark as deleted but keep the record
      const result = await this.collection.updateOne(
        { id: orderId },
        { 
          $set: {
            isDeleted: true,
            updatedAt: new Date().toISOString(),
          }
        }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      console.error(`Error deleting order ${orderId}:`, error);
      throw new Error(`Failed to delete order: ${error.message}`);
    }
  }

  /**
   * Gets all orders with optional filtering
   * @param {Object} filters - Filter criteria
   * @param {Object} options - Query options (limit, skip, sort)
   * @returns {Promise<Array<Object>>} Orders
   */
  async getAllOrders(filters = {}, options = {}) {
    await this.init();

    const { 
      limit = 20, 
      skip = 0, 
      sort = { createdAt: -1 } 
    } = options;

    // Build query from filters
    const query = { ...filters };
    
    // Exclude deleted orders by default
    if (query.includeDeleted) {
      delete query.includeDeleted;
    } else {
      query.isDeleted = { $ne: true };
    }

    try {
      const cursor = this.collection.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);
      
      return await cursor.toArray();
    } catch (error) {
      console.error('Error getting all orders:', error);
      throw new Error(`Failed to get orders: ${error.message}`);
    }
  }

  /**
   * Counts orders with optional filtering
   * @param {Object} filters - Filter criteria
   * @returns {Promise<number>} Count of matching orders
   */
  async countOrders(filters = {}) {
    await this.init();

    // Build query from filters
    const query = { ...filters };
    
    // Exclude deleted orders by default
    if (query.includeDeleted) {
      delete query.includeDeleted;
    } else {
      query.isDeleted = { $ne: true };
    }

    try {
      return await this.collection.countDocuments(query);
    } catch (error) {
      console.error('Error counting orders:', error);
      throw new Error(`Failed to count orders: ${error.message}`);
    }
  }
}

// Export singleton instance
const orderRepository = new OrderRepository();
export default orderRepository; 