import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display login button when not authenticated', async ({ page }) => {
    // Look for login button or sign in text
    const loginButton = page.getByRole('button', { name: /sign in|login/i });
    await expect(loginButton).toBeVisible();
  });

  test('should navigate to account page after login in dev mode', async ({ page }) => {
    // Set development mode
    await page.addInitScript(() => {
      window.localStorage.setItem('sabone-dev-logged-out', 'false');
    });

    await page.reload();
    
    // Check if user is logged in (development mode)
    await expect(page.getByText(/development user|welcome/i)).toBeVisible();
  });

  test('should logout successfully in dev mode', async ({ page }) => {
    // Set logged in state
    await page.addInitScript(() => {
      window.localStorage.setItem('sabone-dev-logged-out', 'false');
    });

    await page.reload();
    
    // Find and click logout button
    const logoutButton = page.getByRole('button', { name: /logout|sign out/i });
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      
      // Verify logout
      await expect(page.getByRole('button', { name: /sign in|login/i })).toBeVisible();
    }
  });

  test('should handle auth errors gracefully', async ({ page }) => {
    // Simulate auth error by manipulating localStorage
    await page.addInitScript(() => {
      // Force an auth error scenario
      window.localStorage.setItem('auth-error-test', 'true');
    });

    await page.goto('/');
    
    // Should not crash and should display appropriate error handling
    const errorMessage = page.getByText(/authentication|error|failed/i);
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toBeVisible();
    }
  });
}); 