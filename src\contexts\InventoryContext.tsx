import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  InventoryItem,
  InventoryUpdate,
  InventoryCheck
} from '@/types/inventory';
import {
  getInventory,
  getProductInventory as _getProductInventory,
  updateInventory,
  checkInventory as checkInventoryService,
  updateInventoryAfterOrder,
  restockInventory as _restockInventory,
  getLowStockItems
} from '@/services/inventoryService';
import { CartItem } from '@/contexts/CartContext';
import { Product as _Product } from '@/data/products';
import { useEmail } from '@/contexts/EmailContext';
import ProductContext from '@/contexts/ProductContext';

interface InventoryContextType {
  inventory: InventoryItem[];
  loading: boolean;
  getProductStock: (productId: string) => number;
  isProductInStock: (productId: string) => boolean;
  isLowStock: (productId: string) => boolean;
  updateProductStock: (update: InventoryUpdate) => Promise<InventoryItem | null>;
  checkInventory: (items: CartItem[]) => Promise<InventoryCheck[]>;
  processOrderInventory: (items: CartItem[]) => Promise<boolean>;
  refreshInventory: () => Promise<void>;
  lowStockItems: InventoryItem[];
}

const InventoryContext = createContext<InventoryContextType | undefined>(undefined);

export const InventoryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [lowStockItems, setLowStockItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const { sendLowStockAlert } = useEmail();
  const { products } = useContext(ProductContext) || { products: [] };

  // Load inventory on mount
  useEffect(() => {
    refreshInventory();
  }, []);

  // Refresh inventory from storage/API
  const refreshInventory = async (): Promise<void> => {
    setLoading(true);
    try {
      const inventoryData = getInventory();
      setInventory(inventoryData);

      // Check for low stock items
      const lowStock = getLowStockItems();
      setLowStockItems(lowStock);

      // Send low stock alerts if there are any low stock items
      if (lowStock.length > 0 && products.length > 0) {
        try {
          // Get product names for the low stock items
          const productNames = lowStock.map(item => {
            const product = products.find(p => p.id === item.productId);
            return product ? product.name : item.productId;
          });

          // Send low stock alert email to admin
          await sendLowStockAlert(lowStock, productNames);
        } catch (emailError) {
          console.error('Error sending low stock alert email:', emailError);
          // Don't fail the inventory refresh if email sending fails
        }
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
      toast.error('Failed to load inventory data');
    } finally {
      setLoading(false);
    }
  };

  // Get stock quantity for a product
  const getProductStock = (productId: string): number => {
    const item = inventory.find(item => item.productId === productId);
    return item ? item.stockQuantity : 0;
  };

  // Check if a product is in stock
  const isProductInStock = (productId: string): boolean => {
    const item = inventory.find(item => item.productId === productId);
    return item ? item.isInStock : false;
  };

  // Check if a product is low in stock
  const isLowStock = (productId: string): boolean => {
    const item = inventory.find(item => item.productId === productId);
    if (!item) return false;

    return (
      item.isInStock &&
      item.stockQuantity <= item.lowStockThreshold
    );
  };

  // Update product stock
  const updateProductStock = async (update: InventoryUpdate): Promise<InventoryItem | null> => {
    try {
      const updatedItem = updateInventory(update);

      if (updatedItem) {
        // Update local inventory state
        setInventory(prev =>
          prev.map(item =>
            item.productId === updatedItem.productId ? updatedItem : item
          )
        );

        // Refresh low stock items
        const lowStock = getLowStockItems();
        setLowStockItems(lowStock);

        return updatedItem;
      }

      return null;
    } catch (error) {
      console.error('Error updating product stock:', error);
      toast.error('Failed to update inventory');
      return null;
    }
  };

  // Check if items are in stock
  const checkInventory = async (items: CartItem[]): Promise<InventoryCheck[]> => {
    try {
      return checkInventoryService(items);
    } catch (error) {
      console.error('Error checking inventory:', error);
      toast.error('Failed to check inventory');
      return [];
    }
  };

  // Process inventory updates after order placement
  const processOrderInventory = async (items: CartItem[]): Promise<boolean> => {
    try {
      const success = updateInventoryAfterOrder(items);

      if (success) {
        // Refresh inventory after order
        await refreshInventory();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error processing order inventory:', error);
      toast.error('Failed to update inventory after order');
      return false;
    }
  };

  return (
    <InventoryContext.Provider
      value={{
        inventory,
        loading,
        getProductStock,
        isProductInStock,
        isLowStock,
        updateProductStock,
        checkInventory,
        processOrderInventory,
        refreshInventory,
        lowStockItems
      }}
    >
      {children}
    </InventoryContext.Provider>
  );
};

export const useInventory = () => {
  const context = useContext(InventoryContext);
  if (context === undefined) {
    throw new Error('useInventory must be used within an InventoryProvider');
  }
  return context;
};
