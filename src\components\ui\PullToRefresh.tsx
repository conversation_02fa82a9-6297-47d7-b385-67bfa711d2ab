import React from 'react';
import { usePullToRefresh } from '@/hooks/use-pull-to-refresh';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  pullDownThreshold?: number;
  maxPullDownDistance?: number;
  refreshIndicatorHeight?: number;
}

/**
 * PullToRefresh component that wraps content and provides pull-to-refresh functionality
 */
const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  children,
  className,
  disabled = false,
  pullDownThreshold = 80,
  maxPullDownDistance = 120,
  refreshIndicatorHeight = 60,
}) => {
  const {
    isPulling: _isPulling,
    pullDistance,
    isRefreshing,
    containerProps,
  } = usePullToRefresh({
    onRefresh,
    pullDownThreshold,
    maxPullDownDistance,
    refreshIndicatorHeight,
    disabled,
  });

  // Calculate progress percentage for the spinner animation
  const pullProgress = Math.min(100, (pullDistance / pullDownThreshold) * 100);
  
  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Pull indicator */}
      <div 
        className="absolute left-0 right-0 flex justify-center items-center overflow-hidden transition-transform duration-300 z-10 bg-sabone-charcoal-deep/80 backdrop-blur-sm"
        style={{ 
          height: `${refreshIndicatorHeight}px`,
          transform: `translateY(${pullDistance > 0 ? pullDistance - refreshIndicatorHeight : -refreshIndicatorHeight}px)`,
        }}
      >
        {isRefreshing ? (
          <div className="flex items-center justify-center text-sabone-gold">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span className="text-sm font-medium">Refreshing...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center text-sabone-gold">
            <svg 
              className="h-6 w-6 mr-2 transition-transform duration-200"
              style={{ 
                transform: `rotate(${Math.min(180, pullProgress * 1.8)}deg)`,
                opacity: pullProgress / 100,
              }}
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            >
              <polyline points="1 4 1 10 7 10"></polyline>
              <polyline points="23 20 23 14 17 14"></polyline>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
            </svg>
            <span className="text-sm font-medium">
              {pullProgress >= 100 ? 'Release to refresh' : 'Pull down to refresh'}
            </span>
          </div>
        )}
      </div>
      
      {/* Content container with pull-to-refresh behavior */}
      <div
        {...containerProps}
        className="w-full transition-transform duration-300 touch-pan-y"
        style={{
          transform: `translateY(${pullDistance}px)`,
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PullToRefresh;
