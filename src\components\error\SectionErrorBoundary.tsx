import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw, Home, Bug } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  section?: string;
  showDetails?: boolean;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * Section-specific error boundary for isolating errors to specific parts of the application
 * Provides graceful error handling with recovery options and detailed error reporting
 */
class SectionErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error details
    console.error(`Error in ${this.props.section || 'Unknown'} section:`, error);
    console.error('Error Info:', errorInfo);

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Report to error tracking service (if available)
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Log to console with structured data
      const errorReport = {
        errorId: this.state.errorId,
        section: this.props.section || 'Unknown',
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      console.error('Structured Error Report:', errorReport);

      // Store error in localStorage for debugging
      try {
        const existingErrors = JSON.parse(localStorage.getItem('sabone_errors') || '[]');
        existingErrors.push(errorReport);
        
        // Keep only last 10 errors
        if (existingErrors.length > 10) {
          existingErrors.splice(0, existingErrors.length - 10);
        }
        
        localStorage.setItem('sabone_errors', JSON.stringify(existingErrors));
      } catch (storageError) {
        console.warn('Failed to store error in localStorage:', storageError);
      }

      // TODO: Send to external error tracking service (Sentry, LogRocket, etc.)
      // if (window.Sentry) {
      //   window.Sentry.captureException(error, {
      //     tags: { section: this.props.section },
      //     extra: errorReport,
      //   });
      // }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: '',
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private renderErrorDetails = () => {
    if (!this.props.showDetails || !this.state.error) return null;

    return (
      <details className="mt-4 text-sm">
        <summary className="cursor-pointer text-sabone-gold hover:text-sabone-gold/80 mb-2">
          Technical Details
        </summary>
        <div className="bg-sabone-charcoal/50 p-3 rounded border border-sabone-gold/20 font-mono text-xs">
          <div className="mb-2">
            <strong>Error ID:</strong> {this.state.errorId}
          </div>
          <div className="mb-2">
            <strong>Message:</strong> {this.state.error.message}
          </div>
          {this.state.error.stack && (
            <div className="mb-2">
              <strong>Stack Trace:</strong>
              <pre className="mt-1 whitespace-pre-wrap text-red-300">
                {this.state.error.stack}
              </pre>
            </div>
          )}
          {this.state.errorInfo?.componentStack && (
            <div>
              <strong>Component Stack:</strong>
              <pre className="mt-1 whitespace-pre-wrap text-yellow-300">
                {this.state.errorInfo.componentStack}
              </pre>
            </div>
          )}
        </div>
      </details>
    );
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-lg bg-sabone-dark-olive/40 border-red-500/30">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-400" />
              </div>
              <CardTitle className="text-sabone-cream">
                Something went wrong in {this.props.section || 'this section'}
              </CardTitle>
              <CardDescription className="text-sabone-cream/70">
                We apologize for the inconvenience. The error has been logged and our team will investigate.
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Alert className="border-yellow-500/30 bg-yellow-500/10">
                <Bug className="h-4 w-4 text-yellow-400" />
                <AlertDescription className="text-yellow-200">
                  Error ID: <code className="font-mono text-xs">{this.state.errorId}</code>
                </AlertDescription>
              </Alert>

              <div className="flex flex-col sm:flex-row gap-2">
                {this.retryCount < this.maxRetries && (
                  <Button
                    onClick={this.handleRetry}
                    variant="outline"
                    className="flex-1 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again ({this.maxRetries - this.retryCount} left)
                  </Button>
                )}
                
                <Button
                  onClick={this.handleGoHome}
                  className="flex-1 bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </Button>
              </div>

              <Button
                onClick={this.handleReload}
                variant="ghost"
                className="w-full text-sabone-cream/70 hover:text-sabone-cream hover:bg-sabone-charcoal/30"
              >
                Reload Page
              </Button>

              {this.renderErrorDetails()}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default SectionErrorBoundary;
