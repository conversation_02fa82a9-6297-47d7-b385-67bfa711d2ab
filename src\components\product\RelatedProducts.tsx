import { Link } from "react-router-dom";
import { Product } from "@/data/products";
import { Card } from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";

import { useState } from "react";
import OptimizedImage from "@/components/OptimizedImage";

interface RelatedProductsProps {
  products: Product[];
}

const RelatedProducts = ({ products }: RelatedProductsProps) => {
  const [_loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});

  const handleImageLoad = (productId: string) => {
    setLoadedImages(prev => ({ ...prev, [productId]: true }));
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map((product) => (
        <Link
          key={product.id}
          to={`/product/${product.id}`}
          className="block transition-transform hover:scale-[1.02] duration-300"
        >
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20 rounded-md overflow-hidden h-full transition-all duration-300 gold-border hover:border-sabone-gold/40">
            <div className="p-0 relative">
              <AspectRatio ratio={3/4} className="overflow-hidden">
                <OptimizedImage
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full"
                  objectFit="cover"
                  onLoad={() => handleImageLoad(product.id)}
                />

                {/* Product Info Overlay */}
                <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm p-3 rounded-md flex flex-col justify-between">
                  <h3 className="text-base font-playfair font-medium text-sabone-gold truncate">
                    {product.name}
                  </h3>
                  <div className="flex justify-between items-center mt-2">
                    <span className="font-medium text-sabone-gold text-sm md:text-base">
                      ${product.price.toFixed(2)}
                    </span>
                  </div>
                </div>
              </AspectRatio>
            </div>
          </Card>
        </Link>
      ))}
    </div>
  );
};

export default RelatedProducts;
