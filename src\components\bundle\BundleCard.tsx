import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Bundle } from "@/data/bundles";
import { useCart } from "@/contexts/CartContext";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Badge } from "@/components/ui/badge";

interface BundleCardProps {
  bundle: Bundle;
}

const BundleCard = ({ bundle }: BundleCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const { addItem } = useCart();

  // Use the custom bundle image if available, otherwise use the first product's image
  const bundleImage = bundle.image || bundle.products[0]?.image;

  const handleAddBundleToCart = () => {
    // Add each product in the bundle to the cart
    bundle.products.forEach(product => {
      addItem(product, 1);
    });
  };

  return (
    <div
      className="bg-sabone-dark-olive/60 border-sabone-gold/20 rounded-md overflow-hidden h-full transition-all duration-300 gold-border hover:border-sabone-gold/40 relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="p-0 relative">
        {/* Bundle Image */}
        <AspectRatio ratio={4/3} className="overflow-hidden">
          <div className="absolute top-4 left-4 z-10">
            <Badge className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/90">
              Save {bundle.discountPercentage}%
            </Badge>
          </div>

          {/* If we have a custom bundle image, use it; otherwise create a collage */}
          <div className="relative w-full h-full">
            {bundle.image ? (
              <img
                src={bundle.image}
                alt={bundle.name}
                className="w-full h-full object-cover transition-all duration-500"
              />
            ) : bundle.products.length > 1 ? (
              <div className="grid grid-cols-2 gap-1 h-full">
                <div className="col-span-2 h-1/2">
                  <img
                    src={bundle.products[0]?.image}
                    alt={bundle.products[0]?.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="h-1/2">
                  <img
                    src={bundle.products[1]?.image}
                    alt={bundle.products[1]?.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="h-1/2">
                  <img
                    src={bundle.products[2]?.image}
                    alt={bundle.products[2]?.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            ) : (
              <img
                src={bundleImage}
                alt={bundle.name}
                className="w-full h-full object-cover transition-all duration-500"
              />
            )}

            {/* Visual enhancement for hover state */}
            <div
              className={`absolute inset-0 bg-sabone-gold/10 transition-opacity duration-300 ${
                isHovered ? 'opacity-100' : 'opacity-0'
              }`}
              aria-hidden="true"
            />
          </div>
        </AspectRatio>

        {/* Bundle Details */}
        <div className="p-6">
          <h3 className="text-xl font-playfair font-bold text-sabone-gold mb-2">
            {bundle.name}
          </h3>

          <p className="text-sabone-cream/90 mb-4">
            {bundle.description}
          </p>

          <div className="mb-4">
            <h4 className="text-sm font-medium text-sabone-gold mb-2">Products:</h4>
            <ul className="space-y-1">
              {bundle.products.map((product) => (
                <li key={product.id} className="text-sabone-cream/80 text-sm flex items-start">
                  <span className="text-sabone-gold mr-2">•</span>
                  <span>{product.name}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sabone-cream/60 line-through text-sm">
                ${bundle.originalPrice.toFixed(2)}
              </span>
              <span className="text-sabone-gold font-semibold text-lg">
                ${bundle.discountedPrice.toFixed(2)}
              </span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleAddBundleToCart}
              className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
            >
              Add Bundle to Cart
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                >
                  View Details
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-3xl">
                <DialogHeader>
                  <DialogTitle className="text-2xl font-playfair text-sabone-gold">{bundle.name}</DialogTitle>
                  <DialogDescription className="text-sabone-cream/80">
                    {bundle.description}
                  </DialogDescription>
                </DialogHeader>

                <div className="grid md:grid-cols-2 gap-6 mt-4">
                  {/* Bundle Products */}
                  <div>
                    <h4 className="text-lg font-playfair text-sabone-gold mb-3">Included Products</h4>
                    <div className="space-y-4">
                      {bundle.products.map((product) => (
                        <div key={product.id} className="flex gap-3 items-start">
                          <div className="w-16 h-16 bg-sabone-charcoal/50 rounded overflow-hidden flex-shrink-0">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <h5 className="font-medium text-sabone-gold">{product.name}</h5>
                            <p className="text-sm text-sabone-cream/70">{product.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Bundle Details */}
                  <div>
                    <h4 className="text-lg font-playfair text-sabone-gold mb-3">Bundle Details</h4>
                    <p className="mb-4 text-sabone-cream/90">
                      This curated set combines our finest products to create a complete ritual experience.
                      Save {bundle.discountPercentage}% when purchasing these items together.
                    </p>

                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sabone-cream/80">Original Price:</span>
                        <span className="text-sabone-cream/60 line-through">
                          ${bundle.originalPrice.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sabone-cream/80">Bundle Price:</span>
                        <span className="text-sabone-gold font-semibold text-lg">
                          ${bundle.discountedPrice.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sabone-cream/80">You Save:</span>
                        <span className="text-sabone-gold/90">
                          ${(bundle.originalPrice - bundle.discountedPrice).toFixed(2)}
                        </span>
                      </div>
                    </div>

                    <Button
                      onClick={handleAddBundleToCart}
                      className="w-full bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                    >
                      Add Bundle to Cart
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BundleCard;
