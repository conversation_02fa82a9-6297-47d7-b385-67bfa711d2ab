import React from 'react';
import ErrorBoundary from './ErrorBoundary';

interface GlobalErrorBoundaryProps {
  children: React.ReactNode;
}

/**
 * A global error boundary component that wraps the entire application.
 * Provides a more detailed fallback UI with support options.
 */
const GlobalErrorBoundary: React.FC<GlobalErrorBoundaryProps> = ({ children }) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // In production, this would send to an error tracking service like Sentry
    console.error('Global error caught:', error, errorInfo);
    
    // TODO: Implement error logging service integration
    // Example: Sentry.captureException(error);
  };

  const globalFallback = (
    <div className="min-h-screen flex items-center justify-center bg-sabone-dark p-4">
      <div className="max-w-lg w-full bg-sabone-dark-olive/30 p-8 rounded-lg border border-sabone-gold shadow-lg">
        <div className="text-center mb-6">
          <h1 className="text-sabone-gold text-3xl font-medium mb-2">We've encountered a problem</h1>
          <p className="text-sabone-beige">
            We apologize for the inconvenience. The application has encountered an unexpected error.
          </p>
        </div>

        <div className="bg-sabone-dark/50 rounded-lg p-4 mb-6 border border-sabone-gold/30">
          <h2 className="text-sabone-gold-accent text-lg mb-2">What you can try:</h2>
          <ul className="text-sabone-beige space-y-2 list-disc pl-5">
            <li>Refresh the page</li>
            <li>Clear your browser cache and cookies</li>
            <li>Try again in a few minutes</li>
            <li>Contact our support team if the problem persists</li>
          </ul>
        </div>

        <div className="flex gap-4 justify-center">
          <button
            onClick={() => window.location.reload()}
            className="px-5 py-2 bg-sabone-gold text-sabone-dark font-medium rounded hover:bg-sabone-gold-accent transition-colors"
          >
            Refresh Page
          </button>
          <a
            href="/"
            className="px-5 py-2 bg-transparent border border-sabone-gold text-sabone-gold rounded hover:bg-sabone-gold/10 transition-colors"
          >
            Return Home
          </a>
        </div>
      </div>
    </div>
  );

  return (
    <ErrorBoundary
      fallback={globalFallback}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
};

export default GlobalErrorBoundary; 