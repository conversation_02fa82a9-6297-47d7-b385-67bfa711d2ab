
import { Button } from "@/components/ui/button";
import { ChevronUp } from "lucide-react";
import { useTranslations } from "next-intl";

const Footer = () => {
  const t = useTranslations('common.footer');
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  return (
    <footer className="bg-sabone-charcoal border-t border-sabone-gold/20 py-10 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">{t('aboutUsTitle')}</h3>
            <p className="text-sm text-sabone-cream/70">
              {t('aboutUsDescription')}
            </p>
            <div className="mt-4">
              <img
                src="/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png"
                alt="Sabone Logo"
                className="h-12 w-auto"
              />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">{t('quickLinksTitle')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkOurStory')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkProducts')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkIngredients')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkSustainability')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkBlog')}
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">{t('customerCareTitle')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkShippingPolicy')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkReturnsRefunds')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkFAQ')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkTrackOrder')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkContactUs')}
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">{t('legalTitle')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkPrivacyPolicy')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkTermsOfService')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkAccessibility')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                  {t('linkCookiePolicy')}
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-sabone-gold/10 flex flex-col-reverse md:flex-row justify-between items-center">
          <div className="text-sm text-sabone-cream/60 mt-4 md:mt-0">
            <p className="mb-1">
              <a href="mailto:<EMAIL>" className="hover:text-sabone-gold transition-colors"><EMAIL></a>
            </p>
            <p>
              {t('copyright', { year: new Date().getFullYear() })}
            </p>
          </div>

          <div className="flex items-center">
            <div className="mr-6 flex space-x-4">
              <a href="/" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                {t('navHome')}
              </a>
              <a href="/checkout" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                {t('navCheckout')}
              </a>
              <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                {t('navPrivacy')}
              </a>
              <a href="#" className="text-sabone-cream/70 hover:text-sabone-gold transition-colors">
                {t('navContact')}
              </a>
            </div>

            <Button
              onClick={scrollToTop}
              variant="outline"
              size="icon"
              className="bg-transparent border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10 hover:text-sabone-gold-light"
              aria-label={t('scrollToTopLabel')}
            >
              <ChevronUp className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
