/**
 * API Service Layer
 * 
 * Centralized API management with authentication, error handling,
 * security integration, and caching strategies.
 */

import { validateFormData, checkRateLimit } from '@/middleware/securityMiddleware';
import { toast } from 'sonner';

// API Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
};

// Error types
export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: Record<string, unknown>;
  timestamp: number;
}

// Response wrapper
export interface ApiResponse<T = unknown> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Request options
export interface ApiRequestOptions extends RequestInit {
  skipAuth?: boolean;
  skipSanitization?: boolean;
  skipRateLimit?: boolean;
  cacheKey?: string;
  cacheTTL?: number;
  retries?: number;
  timeout?: number;
}

// Cache entry
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// In-memory cache (in production, consider using Redis or similar)
class ApiCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private readonly maxEntries = 1000;

  set<T>(key: string, data: T, ttl: number = API_CONFIG.cacheTimeout): void {
    // Clean up old entries if cache is full
    if (this.cache.size >= this.maxEntries) {
      this.cleanup();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    // Remove expired entries
    expiredKeys.forEach(key => this.cache.delete(key));

    // If still too many entries, remove oldest ones
    if (this.cache.size >= this.maxEntries) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.maxEntries * 0.2));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }

  getStats(): { size: number; hitRate: number; entries: number } {
    return {
      size: this.cache.size,
      hitRate: 0, // TODO: Implement hit rate tracking
      entries: this.cache.size,
    };
  }
}

const apiCache = new ApiCache();

/**
 * Creates an API error from response or error object
 */
const createApiError = (
  message: string,
  status?: number,
  code?: string,
  details?: Record<string, unknown>
): ApiError => {
  const error = new Error(message) as ApiError;
  error.status = status;
  error.code = code;
  error.details = details;
  error.timestamp = Date.now();
  return error;
};

/**
 * Gets authentication token from context
 */
const getAuthToken = async (): Promise<string | null> => {
  try {
    // In a real app, get this from your auth context
    // For now, return null or mock token
    if (import.meta.env.DEV) {
      return 'dev-token-123456';
    }
    return null;
  } catch (error) {
    console.warn('Failed to get auth token:', error);
    return null;
  }
};

/**
 * Handles API response and error processing
 */
const processResponse = async <T>(response: Response): Promise<ApiResponse<T>> => {
  const contentType = response.headers.get('content-type');
  
  let data: unknown;
  try {
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
  } catch (error) {
    throw createApiError(
      'Failed to parse response',
      response.status,
      'PARSE_ERROR',
      { contentType, error: error instanceof Error ? error.message : error }
    );
  }

  if (!response.ok) {
    const errorData = data as Record<string, unknown>;
    throw createApiError(
      (errorData.message as string) || `HTTP ${response.status}: ${response.statusText}`,
      response.status,
      (errorData.code as string) || 'HTTP_ERROR',
      errorData
    );
  }

  const responseData = data as Record<string, unknown>;
  return {
    data: (responseData.data || data) as T,
    message: responseData.message as string,
    success: true,
    timestamp: Date.now(),
    pagination: responseData.pagination as ApiResponse<T>['pagination'],
  };
};

/**
 * Creates request with security and authentication headers
 */
const createSecureRequest = async (
  url: string,
  options: ApiRequestOptions = {}
): Promise<{ url: string; options: RequestInit }> => {
  const {
    skipAuth = false,
    skipSanitization = false,
    skipRateLimit = false,
    timeout = API_CONFIG.timeout,
    ...requestOptions
  } = options;

  // Rate limiting check
  if (!skipRateLimit) {
    const operation = `api_${url.split('/').pop() || 'request'}`;
    if (!checkRateLimit(operation, 'user', 30, 60000)) { // 30 requests per minute
      throw createApiError(
        'Rate limit exceeded',
        429,
        'RATE_LIMIT_EXCEEDED'
      );
    }
  }

  // Prepare headers
  const headers = new Headers({
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Client-Version': '1.0.0',
    'X-Request-ID': crypto.randomUUID(),
  });

  // Add existing headers
  if (requestOptions.headers) {
    const existingHeaders = new Headers(requestOptions.headers);
    for (const [key, value] of existingHeaders.entries()) {
      headers.set(key, value);
    }
  }

  // Add authentication if not skipped
  if (!skipAuth) {
    const token = await getAuthToken();
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
  }

  // Sanitize request body if present
  let body = requestOptions.body;
  if (body && !skipSanitization) {
    try {
      if (typeof body === 'string') {
        const parsed = JSON.parse(body);
        const validation = validateFormData(parsed);
        
        if (!validation.isValid) {
          throw createApiError(
            'Invalid request data',
            400,
            'VALIDATION_ERROR',
            { errors: validation.errors }
          );
        }
        
        body = JSON.stringify(validation.sanitizedData);
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid request data')) {
        throw error;
      }
      // If not JSON, skip sanitization
    }
  }

  return {
    url: `${API_CONFIG.baseURL}${url}`,
    options: {
      ...requestOptions,
      headers,
      body,
      signal: timeout ? AbortSignal.timeout(timeout) : undefined,
    },
  };
};

/**
 * Makes HTTP request with retry logic
 */
const makeRequest = async <T>(
  url: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> => {
  const { retries = API_CONFIG.retryAttempts } = options;
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const { url: requestUrl, options: requestOptions } = await createSecureRequest(url, options);
      
      const response = await fetch(requestUrl, requestOptions);
      return await processResponse<T>(response);
      
    } catch (error) {
      const isLastAttempt = attempt === retries;
      const isRetryableError = error instanceof Error && (
        error.name === 'TypeError' || // Network errors
        error.message.includes('fetch') ||
        (error as ApiError).status >= 500 // Server errors
      );

      if (isLastAttempt || !isRetryableError) {
        throw error;
      }

      // Wait before retry with exponential backoff
      const delay = API_CONFIG.retryDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw createApiError('Max retries exceeded', 0, 'MAX_RETRIES_EXCEEDED');
};

/**
 * Main API client class
 */
class ApiClient {
  /**
   * GET request with caching support
   */
  async get<T>(
    url: string,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const { cacheKey = url, cacheTTL = API_CONFIG.cacheTimeout } = options;

    // Check cache first
    const cachedData = apiCache.get<ApiResponse<T>>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const response = await makeRequest<T>(url, { ...options, method: 'GET' });
      
      // Cache successful responses
      apiCache.set(cacheKey, response, cacheTTL);
      
      return response;
    } catch (error) {
      this.handleError(error, 'GET', url);
      throw error;
    }
  }

  /**
   * POST request
   */
  async post<T>(
    url: string,
    data?: Record<string, unknown>,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await makeRequest<T>(url, {
        ...options,
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      });

      // Invalidate related cache entries
      this.invalidateCache(url);
      
      return response;
    } catch (error) {
      this.handleError(error, 'POST', url);
      throw error;
    }
  }

  /**
   * PUT request
   */
  async put<T>(
    url: string,
    data?: Record<string, unknown>,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await makeRequest<T>(url, {
        ...options,
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      });

      // Invalidate related cache entries
      this.invalidateCache(url);
      
      return response;
    } catch (error) {
      this.handleError(error, 'PUT', url);
      throw error;
    }
  }

  /**
   * DELETE request
   */
  async delete<T>(
    url: string,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await makeRequest<T>(url, {
        ...options,
        method: 'DELETE',
      });

      // Invalidate related cache entries
      this.invalidateCache(url);
      
      return response;
    } catch (error) {
      this.handleError(error, 'DELETE', url);
      throw error;
    }
  }

  /**
   * Upload file with progress tracking
   */
  async upload<T>(
    url: string,
    file: File,
    options: ApiRequestOptions & {
      onProgress?: (progress: number) => void;
      fieldName?: string;
    } = {}
  ): Promise<ApiResponse<T>> {
    const { onProgress, fieldName = 'file', ...requestOptions } = options;

    try {
      const formData = new FormData();
      formData.append(fieldName, file);

      // Create XMLHttpRequest for progress tracking
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        });

        xhr.addEventListener('load', async () => {
          try {
            const response = new Response(xhr.responseText, {
              status: xhr.status,
              statusText: xhr.statusText,
            });
            const result = await processResponse<T>(response);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });

        xhr.addEventListener('error', () => {
          reject(createApiError('Upload failed', 0, 'UPLOAD_ERROR'));
        });

        xhr.addEventListener('abort', () => {
          reject(createApiError('Upload aborted', 0, 'UPLOAD_ABORTED'));
        });

        xhr.open('POST', `${API_CONFIG.baseURL}${url}`);
        
        // Add auth header if needed
        getAuthToken().then(token => {
          if (token && !requestOptions.skipAuth) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
          }
          xhr.send(formData);
        });
      });
    } catch (error) {
      this.handleError(error, 'UPLOAD', url);
      throw error;
    }
  }

  /**
   * Handles API errors with user-friendly messages
   */
  private handleError(error: unknown, method: string, url: string): void {
    const apiError = error as ApiError;
    
    console.error(`API ${method} ${url} failed:`, error);

    // Show user-friendly error messages
    if (apiError.status === 401) {
      toast.error('Authentication required. Please log in.');
    } else if (apiError.status === 403) {
      toast.error('Access denied. You don\'t have permission for this action.');
    } else if (apiError.status === 404) {
      toast.error('Resource not found.');
    } else if (apiError.status === 429) {
      toast.error('Too many requests. Please try again later.');
    } else if (apiError.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error instanceof Error) {
      if (error.name === 'AbortError') {
        toast.error('Request timeout. Please check your connection.');
      } else if (error.name === 'TypeError') {
        toast.error('Network error. Please check your connection.');
      } else {
        toast.error(apiError.message || 'An unexpected error occurred.');
      }
    } else {
      toast.error('An unexpected error occurred.');
    }
  }

  /**
   * Invalidates cache entries related to a URL
   */
  private invalidateCache(url: string): void {
    const baseResource = url.split('?')[0].split('/').filter(Boolean).pop();
    if (baseResource) {
      // Simple cache invalidation - in production, use more sophisticated logic
      apiCache.delete(url);
      apiCache.delete(`/${baseResource}`);
    }
  }

  /**
   * Clears all cached data
   */
  clearCache(): void {
    apiCache.clear();
  }

  /**
   * Gets cache statistics
   */
  getCacheStats() {
    return apiCache.getStats();
  }
}

// Export singleton instance
export const api = new ApiClient();

// Export utilities
export { createApiError, apiCache }; 