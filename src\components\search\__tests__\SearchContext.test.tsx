import { renderHook, act } from '@testing-library/react';
import { SearchProvider, useSearch } from '../../../contexts/SearchContext';
import { ProductProvider } from '../../../contexts/ProductContext';
import { mockProduct, setupTest, cleanupTest } from '@/utils/testHelpers';

// Mock the product context
const mockProducts = [
  {
    ...mockProduct,
    id: 'soap-1',
    name: 'Lavender Soap Bar',
    category: 'soap',
    type: 'bar' as const,
    price: 12.99,
    ingredients: ['Lavender Oil', 'Coconut Oil', 'Olive Oil'],
    benefits: ['Relaxing', 'Moisturizing'],
    scent: 'lavender',
    inStock: true,
    featured: true,
  },
  {
    ...mockProduct,
    id: 'shampoo-1',
    name: 'Tea Tree Shampoo',
    category: 'shampoo',
    type: 'liquid' as const,
    price: 18.99,
    ingredients: ['Tea Tree Oil', 'Aloe Vera', 'Coconut Oil'],
    benefits: ['Cleansing', 'Anti-dandruff'],
    scent: 'tea tree',
    inStock: true,
    featured: false,
  },
  {
    ...mockProduct,
    id: 'soap-2',
    name: 'Charcoal Detox Bar',
    category: 'soap',
    type: 'bar' as const,
    price: 15.99,
    ingredients: ['Activated Charcoal', 'Coconut Oil'],
    benefits: ['Detoxifying', 'Deep Cleansing'],
    scent: 'unscented',
    inStock: false,
    featured: true,
  },
];

// Mock the ProductContext
jest.mock('../../../contexts/ProductContext', () => ({
  useProducts: () => ({
    products: mockProducts,
    loading: false,
  }),
  ProductProvider: ({ children }: { children: React.ReactNode }) => children,
}));

describe('SearchContext', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    cleanupTest();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <ProductProvider>
      <SearchProvider>{children}</SearchProvider>
    </ProductProvider>
  );

  describe('Initial State', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      expect(result.current.query).toBe('');
      expect(result.current.filters).toEqual({});
      expect(result.current.sortBy).toEqual({ field: 'name', direction: 'asc' });
      expect(result.current.results).toEqual(mockProducts.sort((a, b) => a.name.localeCompare(b.name)));
      expect(result.current.totalResults).toBe(3);
      expect(result.current.currentPage).toBe(1);
      expect(result.current.itemsPerPage).toBe(12);
      expect(result.current.hasActiveFilters).toBe(false);
    });
  });

  describe('Text Search', () => {
    it('should filter products by search query', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setQuery('lavender');
        // Wait for debounced search
        await new Promise(resolve => setTimeout(resolve, 350));
      });

      expect(result.current.results).toHaveLength(1);
      expect(result.current.results[0].name).toBe('Lavender Soap Bar');
    });

    it('should search across multiple fields', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setQuery('coconut');
        await new Promise(resolve => setTimeout(resolve, 350));
      });

      // Should find products with coconut oil in ingredients
      expect(result.current.results.length).toBeGreaterThan(1);
    });

    it('should handle empty search query', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setQuery('lavender');
        await new Promise(resolve => setTimeout(resolve, 350));
      });

      expect(result.current.results).toHaveLength(1);

      await act(async () => {
        result.current.setQuery('');
        await new Promise(resolve => setTimeout(resolve, 350));
      });

      expect(result.current.results).toHaveLength(3);
    });
  });

  describe('Filtering', () => {
    it('should filter by category', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ category: 'soap' });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(2);
      expect(result.current.results.every(p => p.category === 'soap')).toBe(true);
    });

    it('should filter by price range', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ priceRange: { min: 15, max: 20 } });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(2);
      expect(result.current.results.every(p => p.price >= 15 && p.price <= 20)).toBe(true);
    });

    it('should filter by stock status', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ inStock: true });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(2);
      expect(result.current.results.every(p => p.inStock)).toBe(true);
    });

    it('should filter by featured status', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ featured: true });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(2);
      expect(result.current.results.every(p => p.featured)).toBe(true);
    });

    it('should filter by product type', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ type: 'bar' });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(2);
      expect(result.current.results.every(p => p.type === 'bar')).toBe(true);
    });

    it('should filter by ingredients', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ ingredients: ['Tea Tree Oil'] });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(1);
      expect(result.current.results[0].name).toBe('Tea Tree Shampoo');
    });

    it('should combine multiple filters', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ 
          category: 'soap',
          inStock: true,
          type: 'bar'
        });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.results).toHaveLength(1);
      expect(result.current.results[0].name).toBe('Lavender Soap Bar');
    });
  });

  describe('Sorting', () => {
    it('should sort by name ascending', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setSortBy({ field: 'name', direction: 'asc' });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      const names = result.current.results.map(p => p.name);
      expect(names).toEqual(['Charcoal Detox Bar', 'Lavender Soap Bar', 'Tea Tree Shampoo']);
    });

    it('should sort by price descending', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setSortBy({ field: 'price', direction: 'desc' });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      const prices = result.current.results.map(p => p.price);
      expect(prices).toEqual([18.99, 15.99, 12.99]);
    });

    it('should sort by featured status', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setSortBy({ field: 'featured', direction: 'desc' });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      const featured = result.current.results.map(p => p.featured);
      expect(featured.slice(0, 2)).toEqual([true, true]);
      expect(featured[2]).toBe(false);
    });
  });

  describe('Pagination', () => {
    it('should handle pagination correctly', () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      act(() => {
        result.current.setItemsPerPage(2);
      });

      expect(result.current.paginatedResults).toHaveLength(2);
      expect(result.current.totalPages).toBe(2);

      act(() => {
        result.current.setCurrentPage(2);
      });

      expect(result.current.paginatedResults).toHaveLength(1);
    });
  });

  describe('Search History', () => {
    it('should add queries to search history', () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      act(() => {
        result.current.addToHistory('lavender soap');
      });

      expect(result.current.searchHistory).toContain('lavender soap');
    });

    it('should not add duplicate queries', () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      act(() => {
        result.current.addToHistory('lavender soap');
        result.current.addToHistory('lavender soap');
      });

      expect(result.current.searchHistory.filter(q => q === 'lavender soap')).toHaveLength(1);
    });

    it('should clear search history', () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      act(() => {
        result.current.addToHistory('lavender soap');
        result.current.clearHistory();
      });

      expect(result.current.searchHistory).toHaveLength(0);
    });
  });

  describe('Clear Functions', () => {
    it('should clear filters', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setFilters({ category: 'soap', inStock: true });
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      expect(result.current.hasActiveFilters).toBe(true);

      act(() => {
        result.current.clearFilters();
      });

      expect(result.current.hasActiveFilters).toBe(false);
      expect(result.current.filters).toEqual({});
    });

    it('should clear search and filters', async () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      await act(async () => {
        result.current.setQuery('lavender');
        result.current.setFilters({ category: 'soap' });
        await new Promise(resolve => setTimeout(resolve, 350));
      });

      act(() => {
        result.current.clearSearch();
      });

      expect(result.current.query).toBe('');
      expect(result.current.filters).toEqual({});
    });
  });

  describe('Filter Options', () => {
    it('should provide correct filter options', () => {
      const { result } = renderHook(() => useSearch(), { wrapper });

      const options = result.current.getFilterOptions();

      expect(options.categories).toContain('soap');
      expect(options.categories).toContain('shampoo');
      expect(options.ingredients).toContain('Lavender Oil');
      expect(options.benefits).toContain('Moisturizing');
      expect(options.scents).toContain('lavender');
      expect(options.priceRange.min).toBe(12.99);
      expect(options.priceRange.max).toBe(18.99);
    });
  });
});
