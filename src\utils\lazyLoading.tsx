/**
 * Enhanced lazy loading utilities with error boundaries and loading states
 * Provides optimized code splitting and component lazy loading
 */

import React, { Suspense, ComponentType, LazyExoticComponent } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle } from 'lucide-react';

// Loading component types
export type LoadingComponentType = 'skeleton' | 'spinner' | 'custom';

export interface LazyLoadOptions {
  loadingType?: LoadingComponentType;
  loadingComponent?: ComponentType;
  errorComponent?: ComponentType<{ error: Error; retry: () => void }>;
  retryAttempts?: number;
  preload?: boolean;
  chunkName?: string;
}

/**
 * Default loading components
 */
const DefaultSkeletonLoader = () => (
  <div className="space-y-4 p-6">
    <Skeleton className="h-8 w-3/4" />
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-5/6" />
    <Skeleton className="h-32 w-full" />
  </div>
);

const DefaultSpinnerLoader = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sabone-gold"></div>
  </div>
);

const DefaultErrorComponent = ({ error, retry }: { error: Error; retry: () => void }) => (
  <div className="p-6">
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex flex-col gap-3">
        <span>Failed to load component: {error.message}</span>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={retry}
          className="w-fit"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </AlertDescription>
    </Alert>
  </div>
);

/**
 * Enhanced lazy loading wrapper with error handling and retry logic
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> {
  const {
    loadingType: _loadingType = 'skeleton',
    loadingComponent: _loadingComponent,
    errorComponent: _errorComponent = DefaultErrorComponent,
    retryAttempts = 3,
    preload = false,
    chunkName
  } = options;

  // Create the lazy component
  const LazyComponent = React.lazy(() => {
    let retryCount = 0;
    
    const loadWithRetry = async (): Promise<{ default: T }> => {
      try {
        return await importFn();
      } catch (error) {
        retryCount++;
        
        if (retryCount <= retryAttempts) {
          console.warn(`Failed to load component (attempt ${retryCount}/${retryAttempts}):`, error);
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
          return loadWithRetry();
        }
        
        throw error;
      }
    };

    return loadWithRetry();
  });

  // Add chunk name for better debugging
  if (chunkName) {
    LazyComponent.displayName = `Lazy(${chunkName})`;
  }

  // Preload if requested
  if (preload && typeof window !== 'undefined') {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      importFn().catch(console.warn);
    }, 100);
  }

  return LazyComponent;
}

/**
 * Wrapper component with Suspense and Error Boundary
 */
export function LazyWrapper<T extends ComponentType<any>>({
  component: Component,
  loadingType = 'skeleton',
  loadingComponent,
  errorComponent = DefaultErrorComponent,
  fallbackProps = {},
  ...props
}: {
  component: LazyExoticComponent<T>;
  loadingType?: LoadingComponentType;
  loadingComponent?: ComponentType;
  errorComponent?: ComponentType<{ error: Error; retry: () => void }>;
  fallbackProps?: Record<string, any>;
} & React.ComponentProps<T>) {
  
  // Select loading component
  const LoadingComponent = loadingComponent || 
    (loadingType === 'spinner' ? DefaultSpinnerLoader : DefaultSkeletonLoader);

  return (
    <ErrorBoundary
      FallbackComponent={errorComponent}
      onError={(error, errorInfo) => {
        console.error('Lazy component error:', error, errorInfo);
      }}
    >
      <Suspense fallback={<LoadingComponent {...fallbackProps} />}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Hook for preloading components
 */
export function usePreloadComponent(
  importFn: () => Promise<{ default: ComponentType<any> }>,
  condition: boolean = true
) {
  React.useEffect(() => {
    if (condition && typeof window !== 'undefined') {
      const timer = setTimeout(() => {
        importFn().catch(console.warn);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [importFn, condition]);
}

/**
 * Route-based lazy loading utility
 */
export function createLazyRoute<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions & {
    preloadCondition?: () => boolean;
  } = {}
) {
  const { preloadCondition, ...lazyOptions } = options;
  
  const LazyComponent = createLazyComponent(importFn, lazyOptions);
  
  const WrappedComponent = (props: React.ComponentProps<T>) => {
    // Conditional preloading
    usePreloadComponent(
      importFn, 
      preloadCondition ? preloadCondition() : false
    );
    
    return (
      <LazyWrapper
        component={LazyComponent}
        {...lazyOptions}
        {...props}
      />
    );
  };
  
  return WrappedComponent;
}

/**
 * Image lazy loading utility
 */
export function createLazyImage(src: string, options: {
  placeholder?: string;
  threshold?: number;
  rootMargin?: string;
} = {}) {
  const {
    placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y0ZjRmNCIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
    threshold: _threshold = 0.1,
    rootMargin: _rootMargin = '50px'
  } = options;

  return {
    src: placeholder,
    'data-src': src,
    loading: 'lazy' as const,
    onLoad: (e: React.SyntheticEvent<HTMLImageElement>) => {
      const img = e.currentTarget;
      img.classList.add('loaded');
    },
    onError: (e: React.SyntheticEvent<HTMLImageElement>) => {
      const img = e.currentTarget;
      img.src = placeholder;
      img.classList.add('error');
    }
  };
}

/**
 * Intersection Observer based lazy loading hook
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const [hasIntersected, setHasIntersected] = React.useState(false);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, hasIntersected, options]);

  return { isIntersecting, hasIntersected };
}

/**
 * Performance monitoring for lazy loaded components
 */
export function withPerformanceMonitoring<T extends ComponentType<any>>(
  Component: T,
  componentName: string
): T {
  const WrappedComponent = (props: React.ComponentProps<T>) => {
    React.useEffect(() => {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        // Log performance metrics
        if (process.env.NODE_ENV === 'development') {
          console.log(`${componentName} render time: ${loadTime.toFixed(2)}ms`);
        }
        
        // Send to analytics in production
        if (process.env.NODE_ENV === 'production' && 'gtag' in window) {
          (window as any).gtag('event', 'component_load_time', {
            component_name: componentName,
            load_time: Math.round(loadTime),
          });
        }
      };
    }, []);

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName})`;
  return WrappedComponent as T;
}

// Export default utilities
export default {
  createLazyComponent,
  createLazyRoute,
  LazyWrapper,
  usePreloadComponent,
  useIntersectionObserver,
  withPerformanceMonitoring,
};
