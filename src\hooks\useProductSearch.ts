import { useState, useEffect, useMemo, useCallback } from 'react';
import { Product } from '@/data/products';
import { useProducts } from '@/contexts/ProductContext';
import { 
  searchProducts, 
  filterProducts, 
  sortProducts, 
  generateSearchSuggestions,
  debounce 
} from '@/utils/searchUtils';
import { SearchFilters, SortOption } from '@/contexts/SearchContext';

interface UseProductSearchOptions {
  initialQuery?: string;
  initialFilters?: SearchFilters;
  initialSort?: SortOption;
  debounceMs?: number;
  enableHistory?: boolean;
  maxSuggestions?: number;
}

interface UseProductSearchReturn {
  // State
  query: string;
  filters: SearchFilters;
  sortBy: SortOption;
  results: Product[];
  suggestions: string[];
  isSearching: boolean;
  searchHistory: string[];
  
  // Computed values
  totalResults: number;
  hasActiveFilters: boolean;
  hasQuery: boolean;
  
  // Actions
  setQuery: (query: string) => void;
  setFilters: (filters: Partial<SearchFilters>) => void;
  setSortBy: (sort: SortOption) => void;
  clearFilters: () => void;
  clearSearch: () => void;
  clearHistory: () => void;
  addToHistory: (query: string) => void;
  
  // Quick actions
  searchByCategory: (category: string) => void;
  searchByType: (type: 'bar' | 'liquid') => void;
  searchByPriceRange: (min: number, max: number) => void;
  searchByIngredient: (ingredient: string) => void;
  searchByBenefit: (benefit: string) => void;
  
  // Utility functions
  getFilterOptions: () => {
    categories: string[];
    priceRange: { min: number; max: number };
    ingredients: string[];
    benefits: string[];
    scents: string[];
  };
}

const DEFAULT_OPTIONS: UseProductSearchOptions = {
  initialQuery: '',
  initialFilters: {},
  initialSort: { field: 'name', direction: 'asc' },
  debounceMs: 300,
  enableHistory: true,
  maxSuggestions: 5,
};

export function useProductSearch(options: UseProductSearchOptions = {}): UseProductSearchReturn {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const { products } = useProducts();
  
  // State
  const [query, setQueryState] = useState(opts.initialQuery || '');
  const [filters, setFiltersState] = useState<SearchFilters>(opts.initialFilters || {});
  const [sortBy, setSortByState] = useState<SortOption>(opts.initialSort || { field: 'name', direction: 'asc' });
  const [results, setResults] = useState<Product[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  // Load search history from localStorage
  useEffect(() => {
    if (opts.enableHistory) {
      const savedHistory = localStorage.getItem('sabone-search-history');
      if (savedHistory) {
        try {
          const history = JSON.parse(savedHistory);
          setSearchHistory(history);
        } catch (error) {
          console.error('Error loading search history:', error);
        }
      }
    }
  }, [opts.enableHistory]);

  // Save search history to localStorage
  useEffect(() => {
    if (opts.enableHistory) {
      localStorage.setItem('sabone-search-history', JSON.stringify(searchHistory));
    }
  }, [searchHistory, opts.enableHistory]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((searchQuery: string, searchFilters: SearchFilters, searchSort: SortOption) => {
      setIsSearching(true);
      
      try {
        let searchResults = [...products];

        // Apply text search
        if (searchQuery.trim()) {
          searchResults = searchProducts(searchResults, searchQuery);
        }

        // Apply filters
        searchResults = filterProducts(searchResults, searchFilters);

        // Apply sorting
        searchResults = sortProducts(searchResults, searchSort);

        setResults(searchResults);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsSearching(false);
      }
    }, opts.debounceMs || 300),
    [products, opts.debounceMs]
  );

  // Perform search when query, filters, or sort changes
  useEffect(() => {
    debouncedSearch(query, filters, sortBy);
  }, [query, filters, sortBy, debouncedSearch]);

  // Generate suggestions
  useEffect(() => {
    if (query.length > 1) {
      const newSuggestions = generateSearchSuggestions(
        products, 
        query, 
        opts.maxSuggestions
      );
      setSuggestions(newSuggestions);
    } else {
      setSuggestions([]);
    }
  }, [query, products, opts.maxSuggestions]);

  // Computed values
  const totalResults = results.length;
  const hasActiveFilters = Object.keys(filters).length > 0;
  const hasQuery = query.trim().length > 0;

  // Filter options
  const filterOptions = useMemo(() => {
    const categories = [...new Set(products.map(p => p.category))].filter(Boolean);
    const ingredients = [...new Set(products.flatMap(p => p.ingredients))];
    const benefits = [...new Set(products.flatMap(p => p.benefits))];
    const scents = [...new Set(products.map(p => p.scent))].filter(Boolean);
    
    const prices = products.map(p => p.price);
    const priceRange = {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };

    return { categories, priceRange, ingredients, benefits, scents };
  }, [products]);

  // Action handlers
  const setQuery = useCallback((newQuery: string) => {
    setQueryState(newQuery);
  }, []);

  const setFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
  }, []);

  const setSortBy = useCallback((sort: SortOption) => {
    setSortByState(sort);
  }, []);

  const clearFilters = useCallback(() => {
    setFiltersState({});
  }, []);

  const clearSearch = useCallback(() => {
    setQueryState('');
    setFiltersState({});
  }, []);

  const clearHistory = useCallback(() => {
    setSearchHistory([]);
  }, []);

  const addToHistory = useCallback((searchQuery: string) => {
    if (opts.enableHistory && searchQuery.trim() && !searchHistory.includes(searchQuery)) {
      setSearchHistory(prev => [searchQuery, ...prev.slice(0, 9)]); // Keep last 10
    }
  }, [searchHistory, opts.enableHistory]);

  // Quick action handlers
  const searchByCategory = useCallback((category: string) => {
    setFilters({ category });
  }, [setFilters]);

  const searchByType = useCallback((type: 'bar' | 'liquid') => {
    setFilters({ type });
  }, [setFilters]);

  const searchByPriceRange = useCallback((min: number, max: number) => {
    setFilters({ priceRange: { min, max } });
  }, [setFilters]);

  const searchByIngredient = useCallback((ingredient: string) => {
    const currentIngredients = filters.ingredients || [];
    if (!currentIngredients.includes(ingredient)) {
      setFilters({ ingredients: [...currentIngredients, ingredient] });
    }
  }, [filters.ingredients, setFilters]);

  const searchByBenefit = useCallback((benefit: string) => {
    const currentBenefits = filters.benefits || [];
    if (!currentBenefits.includes(benefit)) {
      setFilters({ benefits: [...currentBenefits, benefit] });
    }
  }, [filters.benefits, setFilters]);

  const getFilterOptions = useCallback(() => filterOptions, [filterOptions]);

  return {
    // State
    query,
    filters,
    sortBy,
    results,
    suggestions,
    isSearching,
    searchHistory,
    
    // Computed values
    totalResults,
    hasActiveFilters,
    hasQuery,
    
    // Actions
    setQuery,
    setFilters,
    setSortBy,
    clearFilters,
    clearSearch,
    clearHistory,
    addToHistory,
    
    // Quick actions
    searchByCategory,
    searchByType,
    searchByPriceRange,
    searchByIngredient,
    searchByBenefit,
    
    // Utility functions
    getFilterOptions,
  };
}
