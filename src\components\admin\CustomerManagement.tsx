import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Search,
  Filter,
  RefreshCw,
  Eye,
  User,
  Mail,
  Calendar,
  ShoppingBag,
  MapPin,
  Send
} from "lucide-react";


// Mock customer data
const mockCustomers = [
  {
    id: "CUST-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    joinDate: "2023-01-15",
    totalOrders: 5,
    totalSpent: 249.95,
    status: "active",
    lastPurchase: "2023-05-15",
    address: {
      line1: "123 Main St",
      line2: "Apt 4B",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "USA",
    },
    segment: "regular",
  },
  {
    id: "CUST-002",
    name: "Jane Smith",
    email: "<EMAIL>",
    joinDate: "2023-02-20",
    totalOrders: 3,
    totalSpent: 149.97,
    status: "active",
    lastPurchase: "2023-05-16",
    address: {
      line1: "456 Oak Ave",
      line2: "",
      city: "Los Angeles",
      state: "CA",
      zipCode: "90001",
      country: "USA",
    },
    segment: "new",
  },
  {
    id: "CUST-003",
    name: "Robert Johnson",
    email: "<EMAIL>",
    joinDate: "2022-11-05",
    totalOrders: 8,
    totalSpent: 399.92,
    status: "active",
    lastPurchase: "2023-05-17",
    address: {
      line1: "789 Pine St",
      line2: "",
      city: "Chicago",
      state: "IL",
      zipCode: "60007",
      country: "USA",
    },
    segment: "vip",
  },
  {
    id: "CUST-004",
    name: "Emily Davis",
    email: "<EMAIL>",
    joinDate: "2023-03-10",
    totalOrders: 2,
    totalSpent: 74.98,
    status: "inactive",
    lastPurchase: "2023-03-18",
    address: {
      line1: "321 Elm St",
      line2: "Suite 101",
      city: "Miami",
      state: "FL",
      zipCode: "33101",
      country: "USA",
    },
    segment: "at_risk",
  },
  {
    id: "CUST-005",
    name: "Michael Wilson",
    email: "<EMAIL>",
    joinDate: "2022-09-25",
    totalOrders: 12,
    totalSpent: 589.88,
    status: "active",
    lastPurchase: "2023-05-19",
    address: {
      line1: "654 Maple Ave",
      line2: "",
      city: "Seattle",
      state: "WA",
      zipCode: "98101",
      country: "USA",
    },
    segment: "vip",
  },
];

// Mock order history
const mockOrderHistory = {
  "CUST-001": [
    { id: "ORD-001", date: "2023-05-15", total: 89.97, status: "processing" },
    { id: "ORD-006", date: "2023-04-10", total: 49.98, status: "delivered" },
    { id: "ORD-012", date: "2023-03-05", total: 34.99, status: "delivered" },
    { id: "ORD-018", date: "2023-02-20", total: 39.99, status: "delivered" },
    { id: "ORD-024", date: "2023-01-15", total: 34.99, status: "delivered" },
  ],
  "CUST-002": [
    { id: "ORD-002", date: "2023-05-16", total: 49.98, status: "shipped" },
    { id: "ORD-013", date: "2023-04-02", total: 64.99, status: "delivered" },
    { id: "ORD-019", date: "2023-03-15", total: 34.99, status: "delivered" },
  ],
  "CUST-003": [
    { id: "ORD-003", date: "2023-05-17", total: 104.97, status: "delivered" },
    { id: "ORD-007", date: "2023-04-25", total: 49.98, status: "delivered" },
    { id: "ORD-014", date: "2023-04-10", total: 34.99, status: "delivered" },
    { id: "ORD-020", date: "2023-03-20", total: 74.98, status: "delivered" },
    { id: "ORD-025", date: "2023-02-15", total: 34.99, status: "delivered" },
    { id: "ORD-030", date: "2023-01-30", total: 34.99, status: "delivered" },
    { id: "ORD-035", date: "2023-01-15", total: 29.99, status: "delivered" },
    { id: "ORD-040", date: "2022-12-20", total: 34.99, status: "delivered" },
  ],
  "CUST-004": [
    { id: "ORD-004", date: "2023-03-18", total: 74.98, status: "delivered" },
    { id: "ORD-021", date: "2023-03-10", total: 0, status: "cancelled" },
  ],
  "CUST-005": [
    { id: "ORD-005", date: "2023-05-19", total: 89.97, status: "cancelled" },
    { id: "ORD-008", date: "2023-05-05", total: 49.98, status: "delivered" },
    { id: "ORD-015", date: "2023-04-20", total: 34.99, status: "delivered" },
    { id: "ORD-022", date: "2023-04-05", total: 74.98, status: "delivered" },
    { id: "ORD-026", date: "2023-03-20", total: 34.99, status: "delivered" },
    { id: "ORD-031", date: "2023-03-05", total: 34.99, status: "delivered" },
    { id: "ORD-036", date: "2023-02-20", total: 29.99, status: "delivered" },
    { id: "ORD-041", date: "2023-02-05", total: 34.99, status: "delivered" },
    { id: "ORD-045", date: "2023-01-20", total: 34.99, status: "delivered" },
    { id: "ORD-050", date: "2023-01-05", total: 29.99, status: "delivered" },
    { id: "ORD-055", date: "2022-12-20", total: 34.99, status: "delivered" },
    { id: "ORD-060", date: "2022-12-05", total: 104.97, status: "delivered" },
  ],
};

const CustomerManagement = () => {
  const [customers] = useState(mockCustomers);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterSegment, setFilterSegment] = useState("all");
  const [emailSubject, setEmailSubject] = useState("");
  const [emailBody, setEmailBody] = useState("");
  const [isSendingEmail, setIsSendingEmail] = useState(false);

  // Simulate loading customers
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  // Handle sending email
  const handleSendEmail = (_customerId) => {
    if (!emailSubject || !emailBody) {
      toast.error("Please provide both subject and message");
      return;
    }

    setIsSendingEmail(true);

    // Simulate API call
    setTimeout(() => {
      toast.success("Email sent successfully");
      setIsSendingEmail(false);
      setEmailSubject("");
      setEmailBody("");
    }, 1500);
  };

  // Filter and search customers
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch =
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.id.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterSegment === "all") return matchesSearch;
    return matchesSearch && customer.segment === filterSegment;
  });

  // Get segment badge
  const getSegmentBadge = (segment) => {
    switch (segment) {
      case "new":
        return <Badge variant="outline" className="border-blue-600 text-blue-400">New</Badge>;
      case "regular":
        return <Badge variant="outline" className="border-green-600 text-green-400">Regular</Badge>;
      case "vip":
        return <Badge variant="outline" className="border-purple-600 text-purple-400">VIP</Badge>;
      case "at_risk":
        return <Badge variant="outline" className="border-yellow-600 text-yellow-400">At Risk</Badge>;
      default:
        return <Badge variant="outline" className="border-sabone-gold/30 text-sabone-cream">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Customer Management</h2>
          <p className="text-sabone-cream/70 mt-1">View and manage customer information</p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => {
              setLoading(true);
              setTimeout(() => setLoading(false), 1000);
            }}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sabone-cream/50 h-4 w-4" />
          <Input
            placeholder="Search customers by name, email, or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
          />
        </div>

        <div className="flex items-center gap-2">
          <Filter className="text-sabone-cream/50 h-4 w-4" />
          <Select value={filterSegment} onValueChange={setFilterSegment}>
            <SelectTrigger className="w-[180px] bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
              <SelectValue placeholder="Filter by segment" />
            </SelectTrigger>
            <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
              <SelectItem value="all" className="text-sabone-cream">All Customers</SelectItem>
              <SelectItem value="new" className="text-sabone-cream">New</SelectItem>
              <SelectItem value="regular" className="text-sabone-cream">Regular</SelectItem>
              <SelectItem value="vip" className="text-sabone-cream">VIP</SelectItem>
              <SelectItem value="at_risk" className="text-sabone-cream">At Risk</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      <div className="rounded-md border border-sabone-gold/20 overflow-hidden">
        <Table>
          <TableHeader className="bg-sabone-dark-olive/60">
            <TableRow>
              <TableHead className="text-sabone-gold">Customer</TableHead>
              <TableHead className="text-sabone-gold">Joined</TableHead>
              <TableHead className="text-sabone-gold text-center">Orders</TableHead>
              <TableHead className="text-sabone-gold text-right">Total Spent</TableHead>
              <TableHead className="text-sabone-gold text-center">Segment</TableHead>
              <TableHead className="text-sabone-gold text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell colSpan={6} className="py-4">
                    <div className="h-6 bg-sabone-dark-olive/40 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
              ))
            ) : filteredCustomers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-sabone-cream/70">
                  No customers found matching your criteria
                </TableCell>
              </TableRow>
            ) : (
              filteredCustomers.map((customer) => (
                <TableRow key={customer.id} className="hover:bg-sabone-dark-olive/30">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-sabone-dark-olive/60 flex items-center justify-center">
                        <User className="h-5 w-5 text-sabone-gold" />
                      </div>
                      <div>
                        <div className="font-medium text-sabone-cream">{customer.name}</div>
                        <div className="text-sabone-cream/70 text-sm">{customer.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-sabone-cream">{customer.joinDate}</TableCell>
                  <TableCell className="text-sabone-cream text-center">{customer.totalOrders}</TableCell>
                  <TableCell className="text-sabone-cream text-right">${customer.totalSpent.toFixed(2)}</TableCell>
                  <TableCell className="text-center">
                    {getSegmentBadge(customer.segment)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"

                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-3xl">
                        <DialogHeader>
                          <DialogTitle className="text-sabone-gold">Customer Profile - {customer.name}</DialogTitle>
                        </DialogHeader>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                          <div className="space-y-4">
                            <div className="flex items-center">
                              <User className="h-5 w-5 text-sabone-gold mr-2" />
                              <h3 className="text-sabone-gold font-medium">Customer Information</h3>
                            </div>

                            <div className="bg-sabone-charcoal/30 p-4 rounded-md space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Customer ID:</span>
                                <span className="text-sabone-cream">{customer.id}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Name:</span>
                                <span className="text-sabone-cream">{customer.name}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Email:</span>
                                <span className="text-sabone-cream">{customer.email}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Join Date:</span>
                                <span className="text-sabone-cream">{customer.joinDate}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Status:</span>
                                <span className="text-sabone-cream capitalize">{customer.status}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Segment:</span>
                                <span>{getSegmentBadge(customer.segment)}</span>
                              </div>
                            </div>

                            <div className="flex items-center">
                              <MapPin className="h-5 w-5 text-sabone-gold mr-2" />
                              <h3 className="text-sabone-gold font-medium">Address</h3>
                            </div>

                            <div className="bg-sabone-charcoal/30 p-4 rounded-md">
                              <p className="text-sabone-cream">{customer.address.line1}</p>
                              {customer.address.line2 && (
                                <p className="text-sabone-cream">{customer.address.line2}</p>
                              )}
                              <p className="text-sabone-cream">
                                {customer.address.city}, {customer.address.state} {customer.address.zipCode}
                              </p>
                              <p className="text-sabone-cream">{customer.address.country}</p>
                            </div>

                            <div className="flex items-center">
                              <Mail className="h-5 w-5 text-sabone-gold mr-2" />
                              <h3 className="text-sabone-gold font-medium">Contact Customer</h3>
                            </div>

                            <div className="bg-sabone-charcoal/30 p-4 rounded-md space-y-4">
                              <div className="space-y-2">
                                <label className="text-sabone-cream/70 text-sm">Email Subject:</label>
                                <Input
                                  value={emailSubject}
                                  onChange={(e) => setEmailSubject(e.target.value)}
                                  className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                                  placeholder="Enter email subject"
                                />
                              </div>

                              <div className="space-y-2">
                                <label className="text-sabone-cream/70 text-sm">Email Message:</label>
                                <textarea
                                  value={emailBody}
                                  onChange={(e) => setEmailBody(e.target.value)}
                                  className="w-full h-24 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream rounded-md p-2 resize-none"
                                  placeholder="Enter your message"
                                />
                              </div>

                              <Button
                                onClick={() => handleSendEmail(customer.id)}
                                className="w-full bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                                disabled={isSendingEmail || !emailSubject || !emailBody}
                              >
                                {isSendingEmail ? (
                                  <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    Sending...
                                  </>
                                ) : (
                                  <>
                                    <Send className="h-4 w-4 mr-2" />
                                    Send Email
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div className="flex items-center">
                              <ShoppingBag className="h-5 w-5 text-sabone-gold mr-2" />
                              <h3 className="text-sabone-gold font-medium">Purchase Summary</h3>
                            </div>

                            <div className="bg-sabone-charcoal/30 p-4 rounded-md space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Total Orders:</span>
                                <span className="text-sabone-cream">{customer.totalOrders}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Total Spent:</span>
                                <span className="text-sabone-gold font-medium">${customer.totalSpent.toFixed(2)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Last Purchase:</span>
                                <span className="text-sabone-cream">{customer.lastPurchase}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sabone-cream/70">Average Order Value:</span>
                                <span className="text-sabone-cream">
                                  ${(customer.totalSpent / customer.totalOrders).toFixed(2)}
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center">
                              <Calendar className="h-5 w-5 text-sabone-gold mr-2" />
                              <h3 className="text-sabone-gold font-medium">Order History</h3>
                            </div>

                            <div className="bg-sabone-charcoal/30 p-4 rounded-md">
                              {mockOrderHistory[customer.id]?.length > 0 ? (
                                <div className="space-y-3 max-h-64 overflow-y-auto pr-2">
                                  {mockOrderHistory[customer.id].map((order) => (
                                    <div key={order.id} className="flex justify-between items-center border-b border-sabone-gold/10 pb-2 last:border-0 last:pb-0">
                                      <div>
                                        <p className="text-sabone-cream">{order.id}</p>
                                        <p className="text-sabone-cream/70 text-sm">{order.date}</p>
                                      </div>
                                      <div className="text-right">
                                        <p className="text-sabone-gold">${order.total.toFixed(2)}</p>
                                        <p className="text-sabone-cream/70 text-sm capitalize">{order.status}</p>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-sabone-cream/70 text-center py-4">No order history available</p>
                              )}
                            </div>
                          </div>
                        </div>

                        <DialogFooter className="mt-6">
                          <DialogClose asChild>
                            <Button variant="outline" className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10">
                              Close
                            </Button>
                          </DialogClose>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default CustomerManagement;
