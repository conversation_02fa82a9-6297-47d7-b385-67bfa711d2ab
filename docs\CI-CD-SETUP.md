# CI/CD Pipeline Setup Guide

## Overview

The Sabone e-commerce project includes a comprehensive CI/CD pipeline built with GitHub Actions that provides:

- **Automated testing** (unit, integration, E2E)
- **Code quality checks** (linting, formatting, type checking)
- **Security scanning** (dependency audit, vulnerability checks)
- **Multi-environment builds** (staging, production)
- **Automated deployment** (Netlify, with Vercel support)
- **Team notifications** (Slack integration)

## Pipeline Structure

### Workflows

1. **Code Quality** (`.github/workflows/code-quality.yml`)
   - ESLint checks
   - TypeScript type checking
   - Prettier formatting validation

2. **Tests** (`.github/workflows/test.yml`)
   - Unit tests with coverage
   - Server-side tests with MongoDB
   - Coverage reporting to Codecov

3. **Build & Deploy** (`.github/workflows/build-deploy.yml`)
   - Multi-environment builds
   - Staging and production deployments
   - Team notifications

4. **Main CI/CD** (`.github/workflows/ci.yml`)
   - Comprehensive pipeline with all checks
   - Security scanning
   - E2E testing
   - Artifact management

## Required Secrets

### Authentication & APIs
```bash
# Auth0 Configuration
AUTH0_DOMAIN=your-tenant.auth0.com
AUTH0_CLIENT_ID=your_client_id
AUTH0_AUDIENCE=your_api_audience

# Payment Providers
STRIPE_PUBLISHABLE_KEY=pk_live_...
PAYPAL_CLIENT_ID=your_paypal_client_id
```

### Deployment
```bash
# Netlify Deployment
NETLIFY_AUTH_TOKEN=your_netlify_token
NETLIFY_STAGING_SITE_ID=staging_site_id
NETLIFY_PRODUCTION_SITE_ID=production_site_id

# Optional: Vercel Deployment
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id
```

### Code Quality & Testing
```bash
# Code Coverage
CODECOV_TOKEN=your_codecov_token

# Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
```

## Setup Instructions

### 1. GitHub Repository Setup

1. **Enable Actions**: Go to Settings → Actions → Allow all actions
2. **Add Secrets**: Go to Settings → Secrets and variables → Actions
3. **Add Environment Protection**: Go to Settings → Environments

### 2. Environment Configuration

Create two environments in GitHub:

#### Staging Environment
- **Name**: `staging`
- **URL**: `https://sabone-staging.netlify.app`
- **Protection rules**: None (auto-deploy on main)

#### Production Environment
- **Name**: `production`
- **URL**: `https://sabone.netlify.app`
- **Protection rules**: 
  - Required reviewers (optional)
  - Wait timer (optional)

### 3. Local Development Setup

Install required dependencies:

```bash
# Install CI/CD development tools
npm install --save-dev @playwright/test prettier husky

# Setup Git hooks (optional)
npm run prepare

# Install Playwright browsers
npx playwright install
```

### 4. Environment Variables

Create a `.env.example` file with required variables:

```bash
# Auth0
VITE_AUTH0_DOMAIN=your-tenant.auth0.com
VITE_AUTH0_CLIENT_ID=your_client_id
VITE_AUTH0_AUDIENCE=your_api_audience

# Development mode (skip Auth0)
VITE_SKIP_AUTH=true

# Payment providers
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id

# Database
MONGODB_URI=mongodb://localhost:27017/sabone_dev
```

## Pipeline Features

### Security Scanning

- **Dependency Audit**: Checks for known vulnerabilities
- **Security Policy**: Moderate severity threshold
- **Audit CI**: Automated vulnerability reporting

### Code Quality

- **ESLint**: JavaScript/TypeScript linting
- **Prettier**: Code formatting
- **TypeScript**: Type checking
- **Import validation**: Circular dependency detection

### Testing Strategy

#### Unit Tests
- **Framework**: Jest + React Testing Library
- **Coverage**: 80% minimum target
- **Reports**: Codecov integration

#### Integration Tests
- **Backend**: Express.js + MongoDB
- **Database**: Containerized MongoDB for CI
- **Environment**: Isolated test database

#### E2E Tests
- **Framework**: Playwright
- **Browsers**: Chrome, Firefox, Safari
- **Mobile**: iOS Safari, Android Chrome
- **Features**:
  - Authentication flows
  - Checkout process
  - Error handling
  - Mobile responsiveness

### Build Strategy

#### Development Build
- **Environment**: Staging
- **Auth**: Development mode (skip Auth0)
- **Features**: Debug tools enabled
- **Source maps**: Included

#### Production Build
- **Environment**: Production
- **Auth**: Full Auth0 integration
- **Optimization**: Minified and tree-shaken
- **Source maps**: Excluded

### Deployment

#### Staging
- **Trigger**: Every push to `main`
- **Environment**: Development build
- **URL**: Staging subdomain
- **Purpose**: QA and testing

#### Production
- **Trigger**: Manual approval or tags
- **Environment**: Production build
- **URL**: Production domain
- **Purpose**: Live application

## Monitoring & Alerts

### Build Status
- **GitHub Status**: Commit status checks
- **Pull Request**: Automatic checks on PRs
- **Branch Protection**: Require passing checks

### Notifications
- **Slack Integration**: Deployment status
- **Email**: Failed build notifications
- **Dashboard**: GitHub Actions tab

### Coverage Reports
- **Codecov**: Coverage trends and reports
- **PR Comments**: Coverage diff on pull requests
- **Quality Gates**: Coverage thresholds

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check build logs
gh run list --workflow=ci.yml
gh run view [run-id]

# Local reproduction
npm run build
npm run test:coverage
```

#### Deployment Issues
```bash
# Check deployment logs
netlify deploy --dry-run
netlify sites:list

# Manual deployment
npm run build
netlify deploy --prod --dir=dist
```

#### Test Failures
```bash
# Run tests locally
npm run test
npm run test:e2e
npm run test:server

# Debug specific test
npm run test -- --verbose
```

### Environment Debugging

#### Check Environment Variables
```bash
# In GitHub Actions
echo ${{ secrets.AUTH0_DOMAIN }}

# Locally
echo $VITE_AUTH0_DOMAIN
```

#### Validate Configuration
```bash
# Build validation
npm run build:dev
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
```

## Best Practices

### Commit Messages
- Use conventional commits format
- Include scope: `feat(auth): add token refresh`
- Reference issues: `fix(#123): resolve login error`

### Pull Requests
- **Draft PRs**: For work in progress
- **Reviews**: Require at least one approval
- **Checks**: All CI checks must pass
- **Testing**: Include test cases for new features

### Security
- **Secrets**: Never commit sensitive data
- **Dependencies**: Regular security updates
- **Scanning**: Monitor vulnerability reports
- **Access**: Limit deployment permissions

### Performance
- **Bundle Size**: Monitor build output
- **Dependencies**: Audit package additions
- **Caching**: Utilize GitHub Actions cache
- **Parallel Jobs**: Optimize workflow execution

## Maintenance

### Regular Tasks
- **Weekly**: Review dependency updates
- **Monthly**: Security audit and updates
- **Quarterly**: Pipeline optimization review

### Updates
- **GitHub Actions**: Keep actions up to date
- **Dependencies**: Regular npm audit and updates
- **Configuration**: Review and optimize settings

### Monitoring
- **Build Times**: Track and optimize slow builds
- **Success Rate**: Monitor failure patterns
- **Resource Usage**: Optimize CI resource consumption 