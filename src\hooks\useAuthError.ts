import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface AuthError extends Error {
  message: string;
}

interface UseAuthErrorResult {
  authError: AuthError | null;
  setAuthError: (error: AuthError | null) => void;
  clearAuthError: () => void;
  handleAuth0Error: (error: Error | null) => void;
}

export const useAuthError = (): UseAuthErrorResult => {
  const [authError, setAuthError] = useState<AuthError | null>(null);

  const clearAuthError = useCallback(() => {
    setAuthError(null);
  }, []);

  const handleAuth0Error = useCallback((error: Error | null) => {
    if (error) {
      console.error('Auth0 Error:', error);
      setAuthError(error as AuthError);
      
      // Handle specific Auth0 errors with user-friendly messages
      if (error.message?.includes('Login required')) {
        toast.error('Please log in to continue');
      } else if (error.message?.includes('consent_required')) {
        toast.error('Additional consent required. Please log in again.');
      } else if (error.message?.includes('login_required')) {
        toast.error('Session expired. Please log in again.');
      } else if (error.message?.includes('network')) {
        toast.error('Network error. Please check your connection and try again.');
      } else {
        toast.error(`Authentication error: ${error.message}`);
      }
    } else if (authError && !error) {
      // Clear error if Auth0 error is resolved
      setAuthError(null);
    }
  }, [authError]);

  return {
    authError,
    setAuthError,
    clearAuthError,
    handleAuth0Error
  };
};

export default useAuthError;
