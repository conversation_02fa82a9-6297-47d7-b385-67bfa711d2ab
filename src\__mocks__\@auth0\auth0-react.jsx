// Mock for @auth0/auth0-react library
import { jest } from '@jest/globals';

// Mock user object
const mockUser = {
  sub: 'auth0|test-user-123',
  name: 'Test User',
  email: '<EMAIL>',
  picture: 'https://example.com/avatar.jpg',
  email_verified: true,
  updated_at: '2023-01-01T00:00:00.000Z',
};

// Mock Auth0 context state
let mockAuthState = {
  isAuthenticated: false,
  isLoading: false,
  user: null,
  error: null,
};

// Mo<PERSON> functions
const mockLoginWithRedirect = jest.fn(() => Promise.resolve());
const mockLogout = jest.fn(() => Promise.resolve());
const mockGetAccessTokenSilently = jest.fn(() => Promise.resolve('mock-access-token'));
const mockGetAccessTokenWithPopup = jest.fn(() => Promise.resolve('mock-access-token'));
const mockGetIdTokenClaims = jest.fn(() => Promise.resolve({
  __raw: 'mock-id-token',
  name: mockUser.name,
  email: mockUser.email,
  picture: mockUser.picture,
}));

// Mock useAuth0 hook
export const useAuth0 = jest.fn(() => ({
  ...mockAuthState,
  loginWithRedirect: mockLoginWithRedirect,
  logout: mockLogout,
  getAccessTokenSilently: mockGetAccessTokenSilently,
  getAccessTokenWithPopup: mockGetAccessTokenWithPopup,
  getIdTokenClaims: mockGetIdTokenClaims,
}));

// Mock Auth0Provider component
export const Auth0Provider = ({ children }) => children;

// Mock withAuth0 HOC
export const withAuth0 = (Component) => {
  const WrappedComponent = (props) => {
    const auth0 = useAuth0();
    return <Component {...props} auth0={auth0} />;
  };
  WrappedComponent.displayName = `withAuth0(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Mock withAuthenticationRequired HOC
export const withAuthenticationRequired = (Component, options = {}) => {
  const WrappedComponent = (props) => {
    const { isAuthenticated, isLoading } = useAuth0();
    
    if (isLoading) {
      return options.onRedirecting ? options.onRedirecting() : <div>Loading...</div>;
    }
    
    if (!isAuthenticated) {
      return options.onRedirecting ? options.onRedirecting() : <div>Redirecting to login...</div>;
    }
    
    return <Component {...props} />;
  };
  WrappedComponent.displayName = `withAuthenticationRequired(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Helper functions for testing
export const __setMockAuthState = (newState) => {
  mockAuthState = { ...mockAuthState, ...newState };
  useAuth0.mockReturnValue({
    ...mockAuthState,
    loginWithRedirect: mockLoginWithRedirect,
    logout: mockLogout,
    getAccessTokenSilently: mockGetAccessTokenSilently,
    getAccessTokenWithPopup: mockGetAccessTokenWithPopup,
    getIdTokenClaims: mockGetIdTokenClaims,
  });
};

export const __setMockUser = (user) => {
  mockAuthState.user = user;
  mockAuthState.isAuthenticated = !!user;
  __setMockAuthState(mockAuthState);
};

export const __setMockLoading = (isLoading) => {
  __setMockAuthState({ isLoading });
};

export const __setMockError = (error) => {
  __setMockAuthState({ error });
};

export const __getMockFunctions = () => ({
  loginWithRedirect: mockLoginWithRedirect,
  logout: mockLogout,
  getAccessTokenSilently: mockGetAccessTokenSilently,
  getAccessTokenWithPopup: mockGetAccessTokenWithPopup,
  getIdTokenClaims: mockGetIdTokenClaims,
});

export const __resetMocks = () => {
  mockAuthState = {
    isAuthenticated: false,
    isLoading: false,
    user: null,
    error: null,
  };
  
  mockLoginWithRedirect.mockClear();
  mockLogout.mockClear();
  mockGetAccessTokenSilently.mockClear();
  mockGetAccessTokenWithPopup.mockClear();
  mockGetIdTokenClaims.mockClear();
  
  useAuth0.mockReturnValue({
    ...mockAuthState,
    loginWithRedirect: mockLoginWithRedirect,
    logout: mockLogout,
    getAccessTokenSilently: mockGetAccessTokenSilently,
    getAccessTokenWithPopup: mockGetAccessTokenWithPopup,
    getIdTokenClaims: mockGetIdTokenClaims,
  });
};

// Initialize with default mock implementation
__resetMocks();

export default {
  useAuth0,
  Auth0Provider,
  withAuth0,
  withAuthenticationRequired,
  __setMockAuthState,
  __setMockUser,
  __setMockLoading,
  __setMockError,
  __getMockFunctions,
  __resetMocks,
};
