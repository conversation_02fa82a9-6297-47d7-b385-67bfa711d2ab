import React from 'react';
import { useLocale } from 'next-intl';
import { localeNames } from '@/i18n/config';
import { useTranslations } from 'next-intl';
import { Badge } from '@/components/ui/badge';
import { useRTL } from '@/hooks/rtl-hooks';
import { Globe } from 'lucide-react';

interface CurrentLanguageProps {
  showIcon?: boolean;
  showLabel?: boolean;
  className?: string;
}

/**
 * Component to display the current language
 * Can be configured to show/hide the icon and label
 */
export const CurrentLanguage: React.FC<CurrentLanguageProps> = ({
  showIcon = true,
  showLabel = true,
  className = ''
}) => {
  const locale = useLocale();
  const t = useTranslations('common');
  const { isRTL } = useRTL();

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showIcon && <Globe className="h-4 w-4 text-sabone-gold" />}
      {showLabel && (
        <span className="text-sabone-cream">{t('languageSwitcher.currentLanguage')}:</span>
      )}
      <Badge
        variant="outline"
        className="bg-sabone-dark-olive/50 border-sabone-gold/30 text-sabone-gold"
      >
        {localeNames[locale as keyof typeof localeNames]}
        {isRTL && (
          <span className="ml-1 text-xs text-sabone-cream/70">(RTL)</span>
        )}
      </Badge>
    </div>
  );
};

export default CurrentLanguage;
