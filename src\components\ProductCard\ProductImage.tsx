import { AspectRatio } from "@/components/ui/aspect-ratio";
import { ProductImageProps } from "./types";
import OptimizedImage from "@/components/OptimizedImage";

const ProductImage = ({ product, isHovered }: ProductImageProps) => {
  return (
    <figure className="w-full h-full">
      <AspectRatio ratio={3/4} className="overflow-hidden">
        {/* Radial gradient ambient lighting effect */}
        <div
          className={`absolute inset-0 bg-radial-glow bg-center z-10 transition-opacity duration-500 ${
            isHovered ? 'opacity-100' : 'opacity-0'
          }`}
          aria-hidden="true"
        />

        <OptimizedImage
          src={product.image}
          alt={product.name}
          className="product-image w-full h-full transition-transform duration-500"
          objectFit="cover"
          priority={true} // Add priority to ensure images load quickly
          onError={() => {
            console.error(`Failed to load image for ${product.name}`);
          }}
        />

        {/* Visual enhancement for hover state */}
        <div
          className={`absolute inset-0 bg-sabone-gold/10 transition-all duration-300 ${
            isHovered ? 'opacity-100 scale-105' : 'opacity-0 scale-100'
          }`}
          aria-hidden="true"
        />
      </AspectRatio>
    </figure>
  );
};

export default ProductImage;
