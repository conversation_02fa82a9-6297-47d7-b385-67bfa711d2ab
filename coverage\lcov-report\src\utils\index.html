
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.81% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>159/616</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.52% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>62/210</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.47% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>19/141</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.43% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>140/573</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="formatCurrency.ts"><a href="formatCurrency.ts.html">formatCurrency.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	</tr>

<tr>
	<td class="file low" data-value="formatDate.ts"><a href="formatDate.ts.html">formatDate.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file high" data-value="inputSanitization.ts"><a href="inputSanitization.ts.html">inputSanitization.ts</a></td>
	<td data-value="97.95" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.95" class="pct high">97.95%</td>
	<td data-value="98" class="abs high">96/98</td>
	<td data-value="92.1" class="pct high">92.1%</td>
	<td data-value="38" class="abs high">35/38</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="97.8" class="pct high">97.8%</td>
	<td data-value="91" class="abs high">89/91</td>
	</tr>

<tr>
	<td class="file low" data-value="lazy-load.tsx"><a href="lazy-load.tsx.html">lazy-load.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	</tr>

<tr>
	<td class="file low" data-value="logger.ts"><a href="logger.ts.html">logger.ts</a></td>
	<td data-value="17.74" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.74" class="pct low">17.74%</td>
	<td data-value="62" class="abs low">11/62</td>
	<td data-value="10.71" class="pct low">10.71%</td>
	<td data-value="28" class="abs low">3/28</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="15" class="abs low">1/15</td>
	<td data-value="16.39" class="pct low">16.39%</td>
	<td data-value="61" class="abs low">10/61</td>
	</tr>

<tr>
	<td class="file low" data-value="performanceUtils.ts"><a href="performanceUtils.ts.html">performanceUtils.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="124" class="abs low">0/124</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="119" class="abs low">0/119</td>
	</tr>

<tr>
	<td class="file low" data-value="preload.ts"><a href="preload.ts.html">preload.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="89" class="abs low">0/89</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="83" class="abs low">0/83</td>
	</tr>

<tr>
	<td class="file medium" data-value="securityUtils.ts"><a href="securityUtils.ts.html">securityUtils.ts</a></td>
	<td data-value="52.52" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 52%"></div><div class="cover-empty" style="width: 48%"></div></div>
	</td>
	<td data-value="52.52" class="pct medium">52.52%</td>
	<td data-value="99" class="abs medium">52/99</td>
	<td data-value="52.17" class="pct medium">52.17%</td>
	<td data-value="46" class="abs medium">24/46</td>
	<td data-value="35.29" class="pct low">35.29%</td>
	<td data-value="17" class="abs low">6/17</td>
	<td data-value="49.39" class="pct low">49.39%</td>
	<td data-value="83" class="abs low">41/83</td>
	</tr>

<tr>
	<td class="file low" data-value="testHelpers.tsx"><a href="testHelpers.tsx.html">testHelpers.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="97" class="abs low">0/97</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="29" class="abs low">0/29</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="91" class="abs low">0/91</td>
	</tr>

<tr>
	<td class="file low" data-value="uiHelpers.ts"><a href="uiHelpers.ts.html">uiHelpers.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file low" data-value="validationSchemas.ts"><a href="validationSchemas.ts.html">validationSchemas.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T02:10:10.584Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    