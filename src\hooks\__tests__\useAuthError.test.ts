import { renderHook, act } from '@testing-library/react';
import { toast } from 'sonner';
import { useAuthError } from '../useAuthError';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
  },
}));

describe('useAuthError', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with no error', () => {
    const { result } = renderHook(() => useAuthError());
    
    expect(result.current.authError).toBeNull();
  });

  it('should set and clear auth errors', () => {
    const { result } = renderHook(() => useAuthError());
    const testError = new Error('Test error');

    act(() => {
      result.current.setAuthError(testError);
    });

    expect(result.current.authError).toBe(testError);

    act(() => {
      result.current.clearAuthError();
    });

    expect(result.current.authError).toBeNull();
  });

  describe('handleAuth0Error', () => {
    it('should handle login required error with appropriate message', () => {
      const { result } = renderHook(() => useAuthError());
      const error = new Error('Login required');

      act(() => {
        result.current.handleAuth0Error(error);
      });

      expect(result.current.authError).toBe(error);
      expect(toast.error).toHaveBeenCalledWith('Please log in to continue');
    });

    it('should handle consent_required error', () => {
      const { result } = renderHook(() => useAuthError());
      const error = new Error('consent_required');

      act(() => {
        result.current.handleAuth0Error(error);
      });

      expect(result.current.authError).toBe(error);
      expect(toast.error).toHaveBeenCalledWith('Additional consent required. Please log in again.');
    });

    it('should handle login_required error', () => {
      const { result } = renderHook(() => useAuthError());
      const error = new Error('login_required');

      act(() => {
        result.current.handleAuth0Error(error);
      });

      expect(result.current.authError).toBe(error);
      expect(toast.error).toHaveBeenCalledWith('Session expired. Please log in again.');
    });

    it('should handle network errors', () => {
      const { result } = renderHook(() => useAuthError());
      const error = new Error('network error occurred');

      act(() => {
        result.current.handleAuth0Error(error);
      });

      expect(result.current.authError).toBe(error);
      expect(toast.error).toHaveBeenCalledWith('Network error. Please check your connection and try again.');
    });

    it('should handle generic errors', () => {
      const { result } = renderHook(() => useAuthError());
      const error = new Error('Generic auth error');

      act(() => {
        result.current.handleAuth0Error(error);
      });

      expect(result.current.authError).toBe(error);
      expect(toast.error).toHaveBeenCalledWith('Authentication error: Generic auth error');
    });

    it('should clear error when null is passed after having an error', () => {
      const { result } = renderHook(() => useAuthError());
      const error = new Error('Test error');

      // Set error first
      act(() => {
        result.current.handleAuth0Error(error);
      });

      expect(result.current.authError).toBe(error);

      // Clear error by passing null
      act(() => {
        result.current.handleAuth0Error(null);
      });

      expect(result.current.authError).toBeNull();
    });

    it('should not clear error when null is passed without existing error', () => {
      const { result } = renderHook(() => useAuthError());

      // Should not cause any issues when no error exists
      act(() => {
        result.current.handleAuth0Error(null);
      });

      expect(result.current.authError).toBeNull();
    });
  });

  it('should maintain error state until explicitly cleared', () => {
    const { result } = renderHook(() => useAuthError());
    const error1 = new Error('First error');
    const error2 = new Error('Second error');

    act(() => {
      result.current.handleAuth0Error(error1);
    });

    expect(result.current.authError).toBe(error1);

    act(() => {
      result.current.handleAuth0Error(error2);
    });

    expect(result.current.authError).toBe(error2);

    act(() => {
      result.current.clearAuthError();
    });

    expect(result.current.authError).toBeNull();
  });
}); 