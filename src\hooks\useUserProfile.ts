import { useCallback } from 'react';
import { toast } from 'sonner';
import { uploadProfilePicture, getUserProfile, saveUserProfile } from '@/services/userService';

interface User {
  sub?: string;
  name?: string;
  email?: string;
  picture?: string;
  phone_number?: string;
  role?: 'user' | 'admin' | 'affiliate';
}

interface UseUserProfileResult {
  updateProfile: (
    userId: string,
    data: { name?: string; phone_number?: string },
    isDevelopmentMode: boolean,
    updateDevUser?: (updates: Partial<User>) => void
  ) => Promise<boolean>;
  updateProfilePicture: (
    userId: string,
    file: File,
    isDevelopmentMode: boolean,
    updateDevUser?: (updates: Partial<User>) => void
  ) => Promise<string | null>;
}

export const useUserProfile = (): UseUserProfileResult => {
  const updateProfile = useCallback(async (
    userId: string,
    data: { name?: string; phone_number?: string },
    isDevelopmentMode: boolean,
    updateDevUser?: (updates: Partial<User>) => void
  ): Promise<boolean> => {
    try {
      if (isDevelopmentMode && updateDevUser) {
        // In development mode, update the dev user state
        updateDevUser(data);

        // Also update the user profile in localStorage
        const profile = getUserProfile(userId) || {
          userId,
          lastUpdated: new Date().toISOString()
        };

        saveUserProfile({
          ...profile,
          name: data.name || profile.name,
          phone: data.phone_number || profile.phone
        });

        toast.success('Profile updated successfully');
        return true;
      }

      // In production, update the local profile storage
      const profile = getUserProfile(userId) || {
        userId,
        lastUpdated: new Date().toISOString()
      };

      const success = saveUserProfile({
        ...profile,
        name: data.name || profile.name,
        phone: data.phone_number || profile.phone
      });

      if (success) {
        toast.success('Profile updated successfully');
      } else {
        toast.error('Failed to update profile');
      }

      return success;
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
      return false;
    }
  }, []);

  const updateProfilePicture = useCallback(async (
    userId: string,
    file: File,
    isDevelopmentMode: boolean,
    updateDevUser?: (updates: Partial<User>) => void
  ): Promise<string | null> => {
    try {
      // Upload the profile picture
      const picturePath = await uploadProfilePicture(userId, file);

      if (isDevelopmentMode && updateDevUser) {
        // In development mode, update the dev user state
        updateDevUser({ picture: picturePath });
      }

      toast.success('Profile picture updated successfully');
      return picturePath;
    } catch (error) {
      console.error('Error updating profile picture:', error);
      toast.error('Failed to update profile picture');
      return null;
    }
  }, []);

  return {
    updateProfile,
    updateProfilePicture
  };
};

export default useUserProfile; 