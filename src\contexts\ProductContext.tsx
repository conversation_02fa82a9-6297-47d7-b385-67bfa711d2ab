import React, { createContext, useContext, useState, useEffect } from 'react';
import { Product, products as initialProducts } from '@/data/products';
import { toast } from 'sonner';

interface ProductContextType {
  products: Product[];
  loading: boolean;
  getProductById: (id: string) => Product | undefined;
  getProductsByType: (type: 'bar' | 'liquid') => Product[];
  createProduct: (product: Omit<Product, 'id'>) => Promise<Product | null>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<Product | null>;
  deleteProduct: (id: string) => Promise<boolean>;
  uploadProductImage: (file: File) => Promise<string>;
  deleteProductImage: (imagePath: string) => Promise<boolean>;
  refreshProducts: () => Promise<void>;
  resetProducts: () => Promise<void>;
  // Recommendation-related methods
  getProductsByIds: (ids: string[]) => Product[];
  getProductsByCategory: (category: string) => Product[];
  getProductsByIngredients: (ingredients: string[]) => Product[];
  getProductsByBenefits: (benefits: string[]) => Product[];
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const ProductProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  // Load products on mount
  useEffect(() => {
    refreshProducts();
  }, []);

  // Refresh products from storage/API
  const refreshProducts = async (): Promise<void> => {
    setLoading(true);
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));
      setProducts(initialProducts);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  // Reset products to initial state
  const resetProducts = async (): Promise<void> => {
    setLoading(true);
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));
      setProducts(initialProducts);
      toast.success('Products reset to initial state');
    } catch (error) {
      console.error('Error resetting products:', error);
      toast.error('Failed to reset products');
    } finally {
      setLoading(false);
    }
  };

  // Get a product by ID
  const getProductById = (id: string): Product | undefined => {
    return products.find(product => product.id === id);
  };

  // Get products by type
  const getProductsByType = (type: 'bar' | 'liquid'): Product[] => {
    return products.filter(product => product.type === type);
  };

  // Create a new product
  const createProduct = async (product: Omit<Product, 'id'>): Promise<Product | null> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));
      const newProduct: Product = {
        ...product,
        id: `product-${Date.now()}`, // Generate simple ID
      };
      setProducts(prev => [...prev, newProduct]);
      toast.success(`Product "${newProduct.name}" created successfully`);
      return newProduct;
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error('Failed to create product');
      return null;
    }
  };

  // Update an existing product
  const updateProduct = async (id: string, product: Partial<Product>): Promise<Product | null> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));
      const existingProduct = products.find(p => p.id === id);
      if (!existingProduct) {
        throw new Error('Product not found');
      }
      const updatedProduct: Product = { ...existingProduct, ...product };
      setProducts(prev => prev.map(p => p.id === id ? updatedProduct : p));
      toast.success(`Product "${updatedProduct.name}" updated successfully`);
      return updatedProduct;
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('Failed to update product');
      return null;
    }
  };

  // Delete a product
  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));
      const productExists = products.some(p => p.id === id);
      if (!productExists) {
        throw new Error('Product not found');
      }
      setProducts(prev => prev.filter(p => p.id !== id));
      toast.success('Product deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
      return false;
    }
  };

  // Upload a product image
  const uploadProductImage = async (file: File): Promise<string> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      // Simulate image upload - in real app this would upload to cloud storage
      const imagePath = `/images/products/${file.name}`;
      toast.success('Image uploaded successfully');
      return imagePath;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
      throw error;
    }
  };

  // Delete a product image
  const deleteProductImage = async (imagePath: string): Promise<boolean> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 200));
      // Simulate image deletion - in real app this would delete from cloud storage
      console.log('Deleting image:', imagePath); // Use the parameter to avoid warning
      toast.success('Image deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image');
      return false;
    }
  };

  // Recommendation-related methods
  const getProductsByIds = (ids: string[]): Product[] => {
    return products.filter(product => ids.includes(product.id));
  };

  const getProductsByCategory = (category: string): Product[] => {
    // Since our current Product interface doesn't have category, we'll use type as category
    return products.filter(product => product.type === category);
  };

  const getProductsByIngredients = (ingredients: string[]): Product[] => {
    return products.filter(product =>
      ingredients.some(ingredient =>
        product.ingredients.some(productIngredient =>
          productIngredient.toLowerCase().includes(ingredient.toLowerCase())
        )
      )
    );
  };

  const getProductsByBenefits = (benefits: string[]): Product[] => {
    return products.filter(product =>
      benefits.some(benefit =>
        product.benefits.some(productBenefit =>
          productBenefit.toLowerCase().includes(benefit.toLowerCase())
        )
      )
    );
  };

  return (
    <ProductContext.Provider
      value={{
        products,
        loading,
        getProductById,
        getProductsByType,
        createProduct,
        updateProduct,
        deleteProduct,
        uploadProductImage,
        deleteProductImage,
        refreshProducts,
        resetProducts,
        getProductsByIds,
        getProductsByCategory,
        getProductsByIngredients,
        getProductsByBenefits
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};

export default ProductContext;
