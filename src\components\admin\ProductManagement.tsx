import { useState, useEffect } from "react";
import { useProducts } from "@/contexts/ProductContext";
import { Product } from "@/data/products";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Search,
  Filter,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Eye,
  ArrowUpDown,
  Package2
} from "lucide-react";

import ProductForm from "./ProductForm";
import ConfirmationDialog from "./ConfirmationDialog";

const ProductManagement = () => {
  const { products, loading, refreshProducts, deleteProduct } = useProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [sortField, setSortField] = useState<keyof Product>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);

  // Refresh products data on mount
  useEffect(() => {
    refreshProducts();
  }, [refreshProducts]);

  // Handle product deletion
  const handleDeleteProduct = async () => {
    if (!selectedProduct) return;

    try {
      const success = await deleteProduct(selectedProduct.id);
      if (success) {
        setShowDeleteDialog(false);
        setSelectedProduct(null);
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    }
  };

  // Handle sort toggle
  const toggleSort = (field: keyof Product) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      // Filter by type
      if (filterType !== "all" && product.type !== filterType) {
        return false;
      }

      // Filter by search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          product.name.toLowerCase().includes(searchLower) ||
          product.description.toLowerCase().includes(searchLower) ||
          product.ingredients.some(i => i.toLowerCase().includes(searchLower)) ||
          product.benefits.some(b => b.toLowerCase().includes(searchLower))
        );
      }

      return true;
    })
    .sort((a, b) => {
      // Sort by selected field
      const fieldA = a[sortField];
      const fieldB = b[sortField];

      if (typeof fieldA === "string" && typeof fieldB === "string") {
        return sortDirection === "asc"
          ? fieldA.localeCompare(fieldB)
          : fieldB.localeCompare(fieldA);
      }

      if (typeof fieldA === "number" && typeof fieldB === "number") {
        return sortDirection === "asc"
          ? fieldA - fieldB
          : fieldB - fieldA;
      }

      return 0;
    });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Product Management</h2>
          <p className="text-sabone-cream/70 mt-1">Add, edit, and manage your product catalog</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => refreshProducts()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            variant="default"
            className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
            onClick={() => setShowAddDialog(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-sabone-cream/50" />
          <Input
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream w-full"
          />
        </div>

        <div className="flex gap-2 w-full sm:w-auto">
          <Select
            value={filterType}
            onValueChange={setFilterType}
          >
            <SelectTrigger className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream w-full sm:w-[150px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
              <SelectItem value="all" className="text-sabone-cream">All Types</SelectItem>
              <SelectItem value="bar" className="text-sabone-cream">Soap Bars</SelectItem>
              <SelectItem value="liquid" className="text-sabone-cream">Liquid Products</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <RefreshCw className="h-8 w-8 text-sabone-gold animate-spin" />
        </div>
      ) : filteredProducts.length === 0 ? (
        <div className="text-center py-12 bg-sabone-charcoal/30 rounded-md">
          <Package2 className="h-12 w-12 text-sabone-gold/40 mx-auto mb-4" />
          <h3 className="text-sabone-gold text-lg font-medium">No products found</h3>
          <p className="text-sabone-cream/70 mt-1">
            {searchTerm || filterType !== "all"
              ? "Try adjusting your search or filters"
              : "Add your first product to get started"}
          </p>
          {(searchTerm || filterType !== "all") && (
            <Button
              variant="outline"
              className="mt-4 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              onClick={() => {
                setSearchTerm("");
                setFilterType("all");
              }}
            >
              Clear Filters
            </Button>
          )}
        </div>
      ) : (
        <div className="rounded-md border border-sabone-gold/20 overflow-hidden">
          <Table>
            <TableHeader className="bg-sabone-dark-olive/60">
              <TableRow>
                <TableHead className="text-sabone-gold w-[300px]">
                  <button
                    className="flex items-center"
                    onClick={() => toggleSort("name")}
                  >
                    Product
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </button>
                </TableHead>
                <TableHead className="text-sabone-gold">
                  <button
                    className="flex items-center"
                    onClick={() => toggleSort("type")}
                  >
                    Type
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </button>
                </TableHead>
                <TableHead className="text-sabone-gold text-right">
                  <button
                    className="flex items-center justify-end"
                    onClick={() => toggleSort("price")}
                  >
                    Price
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </button>
                </TableHead>
                <TableHead className="text-sabone-gold text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id} className="hover:bg-sabone-dark-olive/30">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded overflow-hidden bg-sabone-dark-olive/60">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="font-medium text-sabone-cream">{product.name}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-sabone-cream/70">
                    <Badge className={`
                      ${product.type === 'bar'
                        ? 'bg-amber-950/30 text-amber-200 hover:bg-amber-950/40'
                        : 'bg-blue-950/30 text-blue-200 hover:bg-blue-950/40'
                      }
                    `}>
                      {product.type === 'bar' ? 'Soap Bar' : 'Liquid'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sabone-cream text-right">
                    ${product.price.toFixed(2)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowViewDialog(true);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowEditDialog(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 border-red-500/30 text-red-500 hover:bg-red-500/10"
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowDeleteDialog(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Product Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-sabone-gold text-xl">Add New Product</DialogTitle>
            <DialogDescription className="text-sabone-cream/70">
              Create a new product in your catalog
            </DialogDescription>
          </DialogHeader>
          <ProductForm
            onSuccess={() => {
              setShowAddDialog(false);
              refreshProducts();
            }}
            onCancel={() => setShowAddDialog(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Product Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-sabone-gold text-xl">Edit Product</DialogTitle>
            <DialogDescription className="text-sabone-cream/70">
              Update product information
            </DialogDescription>
          </DialogHeader>
          {selectedProduct && (
            <ProductForm
              product={selectedProduct}
              onSuccess={() => {
                setShowEditDialog(false);
                refreshProducts();
              }}
              onCancel={() => setShowEditDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Confirm Deletion"
        description="This action cannot be undone. This will permanently delete the product."
        confirmText="Delete Product"
        variant="destructive"
        onConfirm={handleDeleteProduct}
      >
        {selectedProduct && (
          <div className="flex items-center space-x-3 my-4">
            <div className="h-12 w-12 rounded overflow-hidden bg-sabone-dark-olive/60">
              <img
                src={selectedProduct.image}
                alt={selectedProduct.name}
                className="h-full w-full object-cover"
              />
            </div>
            <div>
              <p className="font-medium text-sabone-cream">{selectedProduct.name}</p>
              <p className="text-sabone-cream/70 text-sm">{selectedProduct.type === 'bar' ? 'Soap Bar' : 'Liquid'} - ${selectedProduct.price.toFixed(2)}</p>
            </div>
          </div>
        )}
      </ConfirmationDialog>

      {/* View Product Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-3xl">
          <DialogHeader>
            <DialogTitle className="text-sabone-gold text-xl">Product Details</DialogTitle>
          </DialogHeader>

          {selectedProduct && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <img
                    src={selectedProduct.image}
                    alt={selectedProduct.name}
                    className="w-full h-auto rounded-md object-cover"
                  />
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-sabone-gold text-lg font-medium">{selectedProduct.name}</h3>
                    <p className="text-sabone-cream/70 mt-1">{selectedProduct.description}</p>
                  </div>

                  <div className="flex justify-between items-center">
                    <Badge className={`
                      ${selectedProduct.type === 'bar'
                        ? 'bg-amber-950/30 text-amber-200'
                        : 'bg-blue-950/30 text-blue-200'
                      }
                    `}>
                      {selectedProduct.type === 'bar' ? 'Soap Bar' : 'Liquid'}
                    </Badge>
                    <span className="text-sabone-gold text-lg font-medium">${selectedProduct.price.toFixed(2)}</span>
                  </div>

                  <Separator className="bg-sabone-gold/20" />

                  <div>
                    <h4 className="text-sabone-gold font-medium mb-2">Full Description</h4>
                    <p className="text-sabone-cream/90">{selectedProduct.fullDescription}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sabone-gold font-medium mb-2">Ingredients</h4>
                      <ul className="list-disc list-inside text-sabone-cream/90">
                        {selectedProduct.ingredients.map((ingredient, index) => (
                          <li key={index}>{ingredient}</li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-sabone-gold font-medium mb-2">Benefits</h4>
                      <ul className="list-disc list-inside text-sabone-cream/90">
                        {selectedProduct.benefits.map((benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {(selectedProduct.skinType || selectedProduct.application || selectedProduct.storage) && (
                    <>
                      <Separator className="bg-sabone-gold/20" />

                      <div className="grid grid-cols-1 gap-2">
                        {selectedProduct.skinType && (
                          <div>
                            <span className="text-sabone-gold font-medium">Skin/Hair Type:</span>{" "}
                            <span className="text-sabone-cream/90">{selectedProduct.skinType}</span>
                          </div>
                        )}

                        {selectedProduct.application && (
                          <div>
                            <span className="text-sabone-gold font-medium">Application:</span>{" "}
                            <span className="text-sabone-cream/90">{selectedProduct.application}</span>
                          </div>
                        )}

                        {selectedProduct.storage && (
                          <div>
                            <span className="text-sabone-gold font-medium">Storage:</span>{" "}
                            <span className="text-sabone-cream/90">{selectedProduct.storage}</span>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                  onClick={() => {
                    setShowViewDialog(false);
                    setShowEditDialog(true);
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Product
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductManagement;
