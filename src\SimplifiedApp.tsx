import React from 'react';

const SimplifiedApp: React.FC = () => {
  return (
    <div style={{
      backgroundColor: '#1c1c1c',
      color: '#e5dcc5',
      minHeight: '100vh',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: '#2a2a1f',
        borderRadius: '8px',
        border: '1px solid #c6a870',
        padding: '20px'
      }}>
        <h1 style={{ color: '#c6a870' }}>Sabone - Simplified App</h1>
        <p>If you can see this content, the basic React rendering is working correctly.</p>
        <p>The issue might be with one of the context providers or components in the full app.</p>
        
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#333328',
          borderRadius: '4px'
        }}>
          <h2 style={{ color: '#c6a870', marginTop: 0 }}>Next Steps</h2>
          <ol style={{ color: '#e5dcc5' }}>
            <li>Try adding each context provider one by one to identify which one is causing the issue</li>
            <li>Check for circular dependencies in your imports</li>
            <li>Verify that all required environment variables are set correctly</li>
            <li>Look for any errors in the browser console</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedApp;
