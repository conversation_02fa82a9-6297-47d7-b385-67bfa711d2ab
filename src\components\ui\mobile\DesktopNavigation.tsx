import { Search, ShoppingCart, Heart, LogIn } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Link, useLocation } from "react-router-dom";
import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/contexts/AuthContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { useTranslations } from "next-intl";
import LanguageSwitcher from "@/components/i18n/LanguageSwitcher";
import DirectLoginButton from "@/components/auth/DirectLoginButton";
import useMobile from "@/hooks/useMobile";

interface DesktopNavigationProps {
  onSignInClick: () => void;
}

const DesktopNavigation = ({ onSignInClick }: DesktopNavigationProps) => {
  const { itemCount } = useCart();
  const { wishlistCount } = useWishlist();
  const { isAuthenticated, user, logout } = useAuth();
  const location = useLocation();
  const tCommon = useTranslations('common');
  const tAccount = useTranslations('account');
  const { isDesktop } = useMobile();

  // Helper function to safely get translations with fallback
  const getTranslation = (translationFn: (key: string) => string, key: string, fallback: string) => {
    try {
      const translation = translationFn(key);
      // If translation returns the key itself, it means translation failed
      return translation === key ? fallback : translation;
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error);
      return fallback;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  const scrollToSection = (id: string) => {
    if (location.pathname === '/') {
      document.getElementById(id)?.scrollIntoView({ behavior: "smooth" });
    } else {
      window.location.href = `/#${id}`;
    }
  };

  if (!isDesktop) return null;

  return (
    <div className="hidden md:flex justify-between items-center h-20 w-full">
      {/* Logo */}
      <div className="flex items-center">
        <div className="h-10">
          <Link to="/">
            <img
              src="/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png"
              alt="Sabone Logo"
              className="h-full w-auto"
            />
          </Link>
        </div>
      </div>

      {/* Desktop Navigation Links */}
      <nav className="flex items-center space-x-8">
        <button
          type="button"
          onClick={() => scrollToSection("products-section")}
          className="text-sabone-cream hover:text-sabone-gold-accent cursor-pointer transition-all duration-300 text-sm uppercase tracking-wide font-medium relative group"
        >
          <span>{getTranslation(tCommon, 'navigation.shop', 'Shop')}</span>
          <span className="absolute -bottom-1 left-0 w-0 h-[1px] bg-sabone-gold-accent transition-all duration-300 group-hover:w-full"></span>
        </button>
        <button
          type="button"
          onClick={() => scrollToSection("about-section")}
          className="text-sabone-cream hover:text-sabone-gold-accent cursor-pointer transition-all duration-300 text-sm uppercase tracking-wide font-medium relative group"
        >
          <span>{getTranslation(tCommon, 'navigation.about', 'About')}</span>
          <span className="absolute -bottom-1 left-0 w-0 h-[1px] bg-sabone-gold-accent transition-all duration-300 group-hover:w-full"></span>
        </button>
        <button
          type="button"
          onClick={() => scrollToSection("contact-section")}
          className="text-sabone-cream hover:text-sabone-gold-accent cursor-pointer transition-all duration-300 text-sm uppercase tracking-wide font-medium relative group"
        >
          <span>{getTranslation(tCommon, 'navigation.contact', 'Contact')}</span>
          <span className="absolute -bottom-1 left-0 w-0 h-[1px] bg-sabone-gold-accent transition-all duration-300 group-hover:w-full"></span>
        </button>
      </nav>

      {/* Desktop Actions */}
      <div className="flex items-center space-x-4">
        {!isAuthenticated && (
          <DirectLoginButton className="hidden md:flex" />
        )}
        {isAuthenticated && !user?.role && (
          <Link
            to="/dev-admin"
            className="hidden md:flex items-center px-4 py-2 bg-sabone-gold hover:bg-sabone-gold-rich text-sabone-charcoal rounded-md transition-colors duration-200"
          >
            Admin Access
          </Link>
        )}

        {/* Language Switcher */}
        <LanguageSwitcher />

        {/* Search Button */}
        <Link
          to="/search"
          className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30 inline-block"
          aria-label="Search products"
        >
          <Search size={18} />
          <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
        </Link>

        {/* Wishlist Button */}
        <div className="relative">
          <Link
            to="/wishlist"
            className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 inline-block relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30"
            aria-label="View wishlist"
          >
            <Heart size={18} />
            <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            {wishlistCount > 0 && (
              <Badge className="absolute -top-1 -right-1 bg-sabone-gold-rich text-sabone-charcoal-deep h-5 w-5 flex items-center justify-center p-0 text-xs font-medium shadow-sm animate-soft-glow">
                {wishlistCount}
              </Badge>
            )}
          </Link>
        </div>

        {/* Cart Button */}
        <div className="relative">
          <Link
            to="/checkout"
            className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 inline-block relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30"
            aria-label="View cart"
          >
            <ShoppingCart size={18} />
            <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            {itemCount > 0 && (
              <Badge className="absolute -top-1 -right-1 bg-sabone-gold-rich text-sabone-charcoal-deep h-5 w-5 flex items-center justify-center p-0 text-xs font-medium shadow-sm animate-soft-glow">
                {itemCount}
              </Badge>
            )}
          </Link>
        </div>

        {/* User Authentication */}
        {isAuthenticated ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0 overflow-hidden group">
                <Avatar className="h-8 w-8 border border-sabone-gold/30 transition-all duration-300 group-hover:border-sabone-gold/60 group-hover:shadow-[0_0_10px_rgba(198,168,112,0.2)]">
                  <AvatarImage src={user?.picture} alt={user?.name} />
                  <AvatarFallback className="bg-sabone-gold/20 text-sabone-gold-rich text-xs font-medium">
                    {user?.name ? getInitials(user.name) : 'U'}
                  </AvatarFallback>
                </Avatar>
                <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 bg-sabone-charcoal-deep/95 backdrop-blur-[4px] border-sabone-gold/30 shadow-[0_5px_15px_rgba(0,0,0,0.3)] z-[100]"
              align="end"
              forceMount
              sideOffset={8}
            >
              <DropdownMenuLabel className="text-sabone-gold-accent font-medium">
                {getTranslation(tAccount, 'title', 'My Account')}
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-sabone-gold/20" />
              <DropdownMenuItem className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15" asChild>
                <Link to="/account">{getTranslation(tAccount, 'profile', 'Profile')}</Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15" asChild>
                <Link to="/account/orders">{getTranslation(tAccount, 'orders', 'Orders')}</Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15" asChild>
                <Link to="/account/addresses">{getTranslation(tAccount, 'addresses', 'Addresses')}</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-sabone-gold/20" />
              <DropdownMenuItem
                className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15"
                onClick={() => logout(() => onSignInClick())}
              >
                {getTranslation(tAccount, 'logout', 'Logout')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30"
            onClick={onSignInClick}
          >
            <LogIn size={18} />
            <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
          </Button>
        )}
      </div>
    </div>
  );
};

export default DesktopNavigation;