import { Button } from "@/components/ui/button";
import { DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ProductDialogProps } from "./types";
import ProductPrice from "./ProductPrice";
import { Separator } from "@/components/ui/separator";
import OptimizedImage from "@/components/OptimizedImage";
import { useWishlist } from "@/contexts/WishlistContext";
import { Heart } from "lucide-react";

// Ingredient icons mapping
const getIngredientIcon = (ingredient: string): string => {
  const lowerIngredient = ingredient.toLowerCase();

  if (lowerIngredient.includes('oil') || lowerIngredient.includes('butter')) return '🌿';
  if (lowerIngredient.includes('coconut')) return '🥥';
  if (lowerIngredient.includes('rose') || lowerIngredient.includes('flower')) return '🌸';
  if (lowerIngredient.includes('herb') || lowerIngredient.includes('leaf')) return '🍃';
  if (lowerIngredient.includes('clay')) return '🏺';
  if (lowerIngredient.includes('honey')) return '🍯';
  if (lowerIngredient.includes('musk') || lowerIngredient.includes('oud')) return '✨';

  return '•';
};

const ProductDialog = ({ product, onAddToCart }: ProductDialogProps) => {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();

  const toggleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };
  return (
    <DialogContent className="sm:max-w-md md:max-w-2xl lg:max-w-3xl bg-sabone-dark-olive/95 border-sabone-gold/30 text-sabone-cream max-h-[90vh] overflow-auto">
      <DialogHeader className="space-y-2">
        <DialogTitle className="text-2xl md:text-3xl text-sabone-gold font-playfair">{product.name}</DialogTitle>
        <DialogDescription className="text-sabone-cream/90 text-base">
          {product.fullDescription || product.description}
        </DialogDescription>
      </DialogHeader>

      <Separator className="bg-sabone-gold/20 my-4" />

      <div className="grid md:grid-cols-2 gap-8 items-start">
        <div className="flex justify-center bg-sabone-charcoal/30 rounded-lg p-4">
          <OptimizedImage
            src={product.image}
            alt={product.name}
            className="max-h-[400px] w-auto"
            objectFit="contain"
            priority={true}
          />
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-sabone-gold text-xl font-playfair font-semibold mb-3">Ingredients</h3>
            <ul className="space-y-1 text-sabone-cream/90">
              {product.ingredients.map((ingredient, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2 text-sabone-gold">{getIngredientIcon(ingredient)}</span>
                  <span>{ingredient}</span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-sabone-gold text-xl font-playfair font-semibold mb-3">Benefits</h3>
            <ul className="space-y-1 text-sabone-cream/90">
              {product.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2 text-sabone-gold">•</span>
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          {(product.skinType || product.application || product.storage) && (
            <div>
              <h3 className="text-sabone-gold text-xl font-playfair font-semibold mb-3">Notes</h3>
              <ul className="space-y-2 text-sabone-cream/90">
                {product.skinType && (
                  <li className="flex items-start">
                    <span className="mr-2 font-medium text-sabone-gold/90">Skin Type:</span>
                    <span>{product.skinType}</span>
                  </li>
                )}
                {product.application && (
                  <li className="flex items-start">
                    <span className="mr-2 font-medium text-sabone-gold/90">Application:</span>
                    <span>{product.application}</span>
                  </li>
                )}
                {product.storage && (
                  <li className="flex items-start">
                    <span className="mr-2 font-medium text-sabone-gold/90">Storage:</span>
                    <span>{product.storage}</span>
                  </li>
                )}
              </ul>
            </div>
          )}

          <Separator className="bg-sabone-gold/20 my-2" />

          <div className="pt-2">
            <div className="flex justify-between items-center mb-4">
              <ProductPrice price={product.price} className="text-2xl font-semibold" />
              <span className="text-sm text-sabone-cream/60">In Stock</span>
            </div>

            <div className="flex gap-2">
              <Button
                className="flex-1 bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal min-h-[44px] text-base"
                onClick={onAddToCart}
              >
                Add to Cart
              </Button>

              <Button
                variant="outline"
                className={`border-sabone-gold/30 min-h-[44px] min-w-[44px] p-0 ${
                  isInWishlist(product.id)
                    ? 'bg-sabone-gold/10 text-sabone-gold'
                    : 'text-sabone-gold hover:bg-sabone-gold/10'
                }`}
                onClick={toggleWishlist}
                aria-label={isInWishlist(product.id) ? `Remove from wishlist` : `Add to wishlist`}
              >
                <Heart className={isInWishlist(product.id) ? 'fill-sabone-gold' : ''} size={18} />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

export default ProductDialog;
