import React, { useState, useEffect } from 'react';
import { Product } from '@/data/products';
import { useRecommendations } from '@/contexts/RecommendationContext';
import RecommendationSection from './RecommendationSection';

interface RecentlyViewedProps {
  className?: string;
  maxVisible?: number;
  excludeCurrentProduct?: string;
}

const RecentlyViewed: React.FC<RecentlyViewedProps> = ({
  className = '',
  maxVisible = 5,
  excludeCurrentProduct
}) => {
  const [recentProducts, setRecentProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { getRecentlyViewed } = useRecommendations();

  const fetchRecentlyViewed = async () => {
    try {
      setLoading(true);
      setError(null);
      let products = await getRecentlyViewed();
      
      // Exclude current product if specified
      if (excludeCurrentProduct) {
        products = products.filter(product => product.id !== excludeCurrentProduct);
      }
      
      setRecentProducts(products);
    } catch (err) {
      setError('Failed to load recently viewed products');
      console.error('Error fetching recently viewed:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecentlyViewed();
  }, [excludeCurrentProduct, getRecentlyViewed]);

  // Don't render if no recent products
  if (!loading && recentProducts.length === 0) {
    return null;
  }

  return (
    <RecommendationSection
      title="Recently Viewed"
      subtitle="Products you've looked at recently"
      products={recentProducts}
      loading={loading}
      error={error}
      onRefresh={fetchRecentlyViewed}
      maxVisible={maxVisible}
      showRefreshButton={false}
      className={className}
      trackingType="recently_viewed"
    />
  );
};

export default RecentlyViewed;
