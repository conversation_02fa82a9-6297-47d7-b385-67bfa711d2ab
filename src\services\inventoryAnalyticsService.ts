import { InventoryItem, InventoryUpdate as _InventoryUpdate } from '@/types/inventory';
import { Product } from '@/data/products';
import { getInventory, getProductInventory as _getProductInventory } from './inventoryService';
import { logger } from '@/utils/logger';

// Storage keys for analytics data
const _INVENTORY_ANALYTICS_KEY = 'sabone-inventory-analytics';
const INVENTORY_ALERTS_KEY = 'sabone-inventory-alerts';
const _INVENTORY_FORECASTS_KEY = 'sabone-inventory-forecasts';

// Types for inventory analytics
export interface InventoryAnalytics {
  totalProducts: number;
  totalStockValue: number;
  lowStockCount: number;
  outOfStockCount: number;
  averageStockLevel: number;
  topSellingProducts: ProductSalesData[];
  slowMovingProducts: ProductSalesData[];
  stockTurnoverRate: number;
  lastUpdated: string;
}

export interface ProductSalesData {
  productId: string;
  productName: string;
  currentStock: number;
  salesVelocity: number; // units per day
  daysOfStock: number;
  reorderPoint: number;
  suggestedOrderQuantity: number;
}

export interface StockAlert {
  id: string;
  productId: string;
  productName: string;
  alertType: 'low_stock' | 'out_of_stock' | 'overstock' | 'reorder_point';
  currentStock: number;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  createdAt: string;
  acknowledged: boolean;
}

export interface InventoryForecast {
  productId: string;
  productName: string;
  currentStock: number;
  forecastedStock: ForecastData[];
  recommendedActions: RecommendedAction[];
  lastUpdated: string;
}

export interface ForecastData {
  date: string;
  predictedStock: number;
  predictedSales: number;
  confidence: number;
}

export interface RecommendedAction {
  action: 'reorder' | 'reduce_price' | 'promote' | 'discontinue';
  priority: 'low' | 'medium' | 'high';
  description: string;
  estimatedImpact: string;
}

/**
 * Inventory Analytics Service
 * Provides comprehensive analytics and forecasting for inventory management
 */
class InventoryAnalyticsService {
  private salesHistory: Map<string, number[]> = new Map();
  private alertHistory: StockAlert[] = [];

  constructor() {
    this.loadAnalyticsData();
    this.initializeSalesTracking();
  }

  /**
   * Load analytics data from storage
   */
  private loadAnalyticsData(): void {
    try {
      const alertsData = localStorage.getItem(INVENTORY_ALERTS_KEY);
      if (alertsData) {
        this.alertHistory = JSON.parse(alertsData);
      }

      const salesData = localStorage.getItem('sabone-sales-history');
      if (salesData) {
        const parsed = JSON.parse(salesData);
        this.salesHistory = new Map(Object.entries(parsed));
      }
    } catch (error) {
      logger.error('Failed to load inventory analytics data', error);
    }
  }

  /**
   * Initialize sales tracking with mock data
   */
  private initializeSalesTracking(): void {
    const inventory = getInventory();
    
    inventory.forEach(item => {
      if (!this.salesHistory.has(item.productId)) {
        // Generate mock sales history (last 30 days)
        const salesData = this.generateMockSalesHistory();
        this.salesHistory.set(item.productId, salesData);
      }
    });

    this.saveSalesHistory();
  }

  /**
   * Generate mock sales history for demonstration
   */
  private generateMockSalesHistory(): number[] {
    const history: number[] = [];
    for (let i = 0; i < 30; i++) {
      // Generate realistic sales data with some randomness
      const baseSales = Math.floor(Math.random() * 5) + 1;
      const weekendMultiplier = (i % 7 === 0 || i % 7 === 6) ? 1.5 : 1;
      history.push(Math.floor(baseSales * weekendMultiplier));
    }
    return history;
  }

  /**
   * Save sales history to storage
   */
  private saveSalesHistory(): void {
    try {
      const salesObject = Object.fromEntries(this.salesHistory);
      localStorage.setItem('sabone-sales-history', JSON.stringify(salesObject));
    } catch (error) {
      logger.error('Failed to save sales history', error);
    }
  }

  /**
   * Get comprehensive inventory analytics
   */
  getInventoryAnalytics(products: Product[]): InventoryAnalytics {
    const inventory = getInventory();
    const productMap = new Map(products.map(p => [p.id, p]));

    const totalProducts = inventory.length;
    const totalStockValue = inventory.reduce((total, item) => {
      const product = productMap.get(item.productId);
      return total + (product ? product.price * item.stockQuantity : 0);
    }, 0);

    const lowStockCount = inventory.filter(item => 
      item.stockQuantity <= item.lowStockThreshold && item.stockQuantity > 0
    ).length;

    const outOfStockCount = inventory.filter(item => 
      item.stockQuantity === 0
    ).length;

    const averageStockLevel = inventory.reduce((total, item) => 
      total + item.stockQuantity, 0
    ) / totalProducts;

    const productSalesData = this.getProductSalesData(products);
    const topSellingProducts = productSalesData
      .sort((a, b) => b.salesVelocity - a.salesVelocity)
      .slice(0, 5);

    const slowMovingProducts = productSalesData
      .filter(p => p.salesVelocity < 1)
      .sort((a, b) => a.salesVelocity - b.salesVelocity)
      .slice(0, 5);

    const stockTurnoverRate = this.calculateStockTurnoverRate();

    return {
      totalProducts,
      totalStockValue,
      lowStockCount,
      outOfStockCount,
      averageStockLevel,
      topSellingProducts,
      slowMovingProducts,
      stockTurnoverRate,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get product sales data with analytics
   */
  private getProductSalesData(products: Product[]): ProductSalesData[] {
    const inventory = getInventory();
    const productMap = new Map(products.map(p => [p.id, p]));

    return inventory.map(item => {
      const product = productMap.get(item.productId);
      const salesHistory = this.salesHistory.get(item.productId) || [];
      const salesVelocity = this.calculateSalesVelocity(salesHistory);
      const daysOfStock = salesVelocity > 0 ? item.stockQuantity / salesVelocity : 999;
      const reorderPoint = Math.ceil(salesVelocity * 7); // 7 days lead time
      const suggestedOrderQuantity = Math.ceil(salesVelocity * 30); // 30 days supply

      return {
        productId: item.productId,
        productName: product?.name || 'Unknown Product',
        currentStock: item.stockQuantity,
        salesVelocity,
        daysOfStock,
        reorderPoint,
        suggestedOrderQuantity
      };
    });
  }

  /**
   * Calculate sales velocity (units per day)
   */
  private calculateSalesVelocity(salesHistory: number[]): number {
    if (salesHistory.length === 0) return 0;
    const totalSales = salesHistory.reduce((sum, sales) => sum + sales, 0);
    return totalSales / salesHistory.length;
  }

  /**
   * Calculate overall stock turnover rate
   */
  private calculateStockTurnoverRate(): number {
    const inventory = getInventory();
    let totalTurnover = 0;
    let validProducts = 0;

    inventory.forEach(item => {
      const salesHistory = this.salesHistory.get(item.productId) || [];
      if (salesHistory.length > 0 && item.stockQuantity > 0) {
        const totalSales = salesHistory.reduce((sum, sales) => sum + sales, 0);
        const turnover = totalSales / item.stockQuantity;
        totalTurnover += turnover;
        validProducts++;
      }
    });

    return validProducts > 0 ? totalTurnover / validProducts : 0;
  }

  /**
   * Generate stock alerts
   */
  generateStockAlerts(products: Product[]): StockAlert[] {
    const inventory = getInventory();
    const productMap = new Map(products.map(p => [p.id, p]));
    const alerts: StockAlert[] = [];

    inventory.forEach(item => {
      const product = productMap.get(item.productId);
      if (!product) return;

      const salesHistory = this.salesHistory.get(item.productId) || [];
      const salesVelocity = this.calculateSalesVelocity(salesHistory);
      const reorderPoint = Math.ceil(salesVelocity * 7);

      // Out of stock alert
      if (item.stockQuantity === 0) {
        alerts.push({
          id: `${item.productId}-out-of-stock-${Date.now()}`,
          productId: item.productId,
          productName: product.name,
          alertType: 'out_of_stock',
          currentStock: item.stockQuantity,
          threshold: 0,
          severity: 'critical',
          message: `${product.name} is out of stock`,
          createdAt: new Date().toISOString(),
          acknowledged: false
        });
      }
      // Low stock alert
      else if (item.stockQuantity <= item.lowStockThreshold) {
        alerts.push({
          id: `${item.productId}-low-stock-${Date.now()}`,
          productId: item.productId,
          productName: product.name,
          alertType: 'low_stock',
          currentStock: item.stockQuantity,
          threshold: item.lowStockThreshold,
          severity: 'high',
          message: `${product.name} is running low (${item.stockQuantity} remaining)`,
          createdAt: new Date().toISOString(),
          acknowledged: false
        });
      }
      // Reorder point alert
      else if (item.stockQuantity <= reorderPoint) {
        alerts.push({
          id: `${item.productId}-reorder-${Date.now()}`,
          productId: item.productId,
          productName: product.name,
          alertType: 'reorder_point',
          currentStock: item.stockQuantity,
          threshold: reorderPoint,
          severity: 'medium',
          message: `${product.name} has reached reorder point`,
          createdAt: new Date().toISOString(),
          acknowledged: false
        });
      }
    });

    // Save alerts
    this.alertHistory = [...this.alertHistory, ...alerts];
    this.saveAlerts();

    return alerts;
  }

  /**
   * Get inventory forecasts
   */
  getInventoryForecasts(products: Product[]): InventoryForecast[] {
    const inventory = getInventory();
    const productMap = new Map(products.map(p => [p.id, p]));

    return inventory.map(item => {
      const product = productMap.get(item.productId);
      const salesHistory = this.salesHistory.get(item.productId) || [];
      const salesVelocity = this.calculateSalesVelocity(salesHistory);

      const forecastedStock = this.generateStockForecast(item.stockQuantity, salesVelocity);
      const recommendedActions = this.generateRecommendedActions(item, salesVelocity);

      return {
        productId: item.productId,
        productName: product?.name || 'Unknown Product',
        currentStock: item.stockQuantity,
        forecastedStock,
        recommendedActions,
        lastUpdated: new Date().toISOString()
      };
    });
  }

  /**
   * Generate stock forecast for next 30 days
   */
  private generateStockForecast(currentStock: number, salesVelocity: number): ForecastData[] {
    const forecast: ForecastData[] = [];
    let stock = currentStock;

    for (let i = 1; i <= 30; i++) {
      const predictedSales = salesVelocity * (0.8 + Math.random() * 0.4); // Add some variance
      stock = Math.max(0, stock - predictedSales);
      
      forecast.push({
        date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        predictedStock: Math.round(stock),
        predictedSales: Math.round(predictedSales),
        confidence: Math.max(0.5, 1 - (i / 30) * 0.3) // Confidence decreases over time
      });
    }

    return forecast;
  }

  /**
   * Generate recommended actions based on inventory analysis
   */
  private generateRecommendedActions(item: InventoryItem, salesVelocity: number): RecommendedAction[] {
    const actions: RecommendedAction[] = [];
    const daysOfStock = salesVelocity > 0 ? item.stockQuantity / salesVelocity : 999;

    if (item.stockQuantity === 0) {
      actions.push({
        action: 'reorder',
        priority: 'high',
        description: 'Immediate restock required - product is out of stock',
        estimatedImpact: 'Prevent lost sales and customer dissatisfaction'
      });
    } else if (daysOfStock < 7) {
      actions.push({
        action: 'reorder',
        priority: 'high',
        description: `Reorder soon - only ${Math.round(daysOfStock)} days of stock remaining`,
        estimatedImpact: 'Maintain stock availability'
      });
    } else if (daysOfStock > 60) {
      actions.push({
        action: 'reduce_price',
        priority: 'medium',
        description: 'Consider promotion to move excess inventory',
        estimatedImpact: 'Reduce carrying costs and free up capital'
      });
    }

    if (salesVelocity < 0.5) {
      actions.push({
        action: 'promote',
        priority: 'medium',
        description: 'Low sales velocity - consider marketing promotion',
        estimatedImpact: 'Increase product visibility and sales'
      });
    }

    return actions;
  }

  /**
   * Save alerts to storage
   */
  private saveAlerts(): void {
    try {
      localStorage.setItem(INVENTORY_ALERTS_KEY, JSON.stringify(this.alertHistory));
    } catch (error) {
      logger.error('Failed to save inventory alerts', error);
    }
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): StockAlert[] {
    return this.alertHistory.filter(alert => !alert.acknowledged);
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alertHistory.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.saveAlerts();
      return true;
    }
    return false;
  }

  /**
   * Record a sale for analytics
   */
  recordSale(productId: string, quantity: number): void {
    const salesHistory = this.salesHistory.get(productId) || [];
    
    // Add to today's sales (last entry)
    if (salesHistory.length > 0) {
      salesHistory[salesHistory.length - 1] += quantity;
    } else {
      salesHistory.push(quantity);
    }

    this.salesHistory.set(productId, salesHistory);
    this.saveSalesHistory();
  }
}

// Export singleton instance
export const inventoryAnalyticsService = new InventoryAnalyticsService();
export default inventoryAnalyticsService;
