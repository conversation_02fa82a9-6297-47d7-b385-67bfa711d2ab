import { Product } from '@/data/products';
import { Review } from '@/types/review';
import { logger } from '@/utils/logger';
import { getSessionId, getUserBehavior as _getUserBehavior } from '@/utils/recommendationUtils';
import { inventoryAnalyticsService } from './inventoryAnalyticsService';
import { getReviewAnalytics } from './reviewAnalyticsService';

// Storage keys for business analytics
const BUSINESS_ANALYTICS_KEY = 'sabone-business-analytics';
const SALES_DATA_KEY = 'sabone-sales-data';
const USER_BEHAVIOR_ANALYTICS_KEY = 'sabone-user-behavior-analytics';
const CONVERSION_FUNNEL_KEY = 'sabone-conversion-funnel';

// Types for business analytics
export interface SalesMetrics {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  revenueGrowth: number;
  orderGrowth: number;
  topSellingProducts: ProductSalesMetric[];
  salesByCategory: CategorySalesMetric[];
  salesByTimeframe: TimeframeSalesMetric[];
  lastUpdated: string;
}

export interface ProductSalesMetric {
  productId: string;
  productName: string;
  revenue: number;
  unitsSold: number;
  averageRating: number;
  conversionRate: number;
}

export interface CategorySalesMetric {
  category: string;
  revenue: number;
  unitsSold: number;
  growth: number;
}

export interface TimeframeSalesMetric {
  period: string;
  revenue: number;
  orders: number;
  date: string;
}

export interface UserBehaviorMetrics {
  totalSessions: number;
  averageSessionDuration: number;
  bounceRate: number;
  pageViewsPerSession: number;
  topPages: PageMetric[];
  userFlow: UserFlowMetric[];
  deviceBreakdown: DeviceMetric[];
  trafficSources: TrafficSourceMetric[];
  lastUpdated: string;
}

export interface PageMetric {
  page: string;
  views: number;
  uniqueViews: number;
  averageTimeOnPage: number;
  exitRate: number;
}

export interface UserFlowMetric {
  fromPage: string;
  toPage: string;
  count: number;
  conversionRate: number;
}

export interface DeviceMetric {
  deviceType: string;
  sessions: number;
  percentage: number;
}

export interface TrafficSourceMetric {
  source: string;
  sessions: number;
  conversionRate: number;
  percentage: number;
}

export interface ConversionFunnelMetrics {
  stages: FunnelStage[];
  overallConversionRate: number;
  dropOffPoints: DropOffPoint[];
  averageTimeToConvert: number;
  lastUpdated: string;
}

export interface FunnelStage {
  stage: string;
  users: number;
  conversionRate: number;
  dropOffRate: number;
}

export interface DropOffPoint {
  stage: string;
  dropOffRate: number;
  commonReasons: string[];
}

export interface BusinessAnalytics {
  salesMetrics: SalesMetrics;
  userBehaviorMetrics: UserBehaviorMetrics;
  conversionFunnelMetrics: ConversionFunnelMetrics;
  inventoryMetrics: any; // From existing inventory analytics
  reviewMetrics: any; // From existing review analytics
  kpis: KPIMetrics;
  lastUpdated: string;
}

export interface KPIMetrics {
  customerLifetimeValue: number;
  customerAcquisitionCost: number;
  returnCustomerRate: number;
  averageOrderFrequency: number;
  inventoryTurnover: number;
  grossMargin: number;
  netPromoterScore: number;
  customerSatisfactionScore: number;
}

class BusinessAnalyticsService {
  private salesHistory: Map<string, number[]> = new Map();
  private userSessions: Map<string, any> = new Map();
  private conversionEvents: any[] = [];

  constructor() {
    this.loadStoredData();
    this.initializeMockData();
  }

  /**
   * Load stored analytics data
   */
  private loadStoredData(): void {
    try {
      const salesData = localStorage.getItem(SALES_DATA_KEY);
      if (salesData) {
        const parsed = JSON.parse(salesData);
        this.salesHistory = new Map(parsed);
      }

      const behaviorData = localStorage.getItem(USER_BEHAVIOR_ANALYTICS_KEY);
      if (behaviorData) {
        const parsed = JSON.parse(behaviorData);
        this.userSessions = new Map(parsed);
      }

      const conversionData = localStorage.getItem(CONVERSION_FUNNEL_KEY);
      if (conversionData) {
        this.conversionEvents = JSON.parse(conversionData);
      }
    } catch (error) {
      logger.error('Failed to load stored analytics data', error);
    }
  }

  /**
   * Initialize mock data for demonstration
   */
  private initializeMockData(): void {
    if (this.salesHistory.size === 0) {
      // Generate mock sales data for the last 30 days
      const today = new Date();
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateKey = date.toISOString().split('T')[0];

        // Mock daily revenue between $500-$2000
        const dailyRevenue = Math.floor(Math.random() * 1500) + 500;
        this.salesHistory.set(dateKey, [dailyRevenue]);
      }
      this.saveSalesData();
    }

    if (this.userSessions.size === 0) {
      // Generate mock user session data
      const sessionId = getSessionId();
      this.userSessions.set(sessionId, {
        startTime: Date.now() - 1800000, // 30 minutes ago
        pageViews: 5,
        deviceType: 'desktop',
        source: 'organic'
      });
      this.saveUserBehaviorData();
    }
  }

  /**
   * Save sales data to localStorage
   */
  private saveSalesData(): void {
    try {
      const data = Array.from(this.salesHistory.entries());
      localStorage.setItem(SALES_DATA_KEY, JSON.stringify(data));
    } catch (error) {
      logger.error('Failed to save sales data', error);
    }
  }

  /**
   * Save user behavior data to localStorage
   */
  private saveUserBehaviorData(): void {
    try {
      const data = Array.from(this.userSessions.entries());
      localStorage.setItem(USER_BEHAVIOR_ANALYTICS_KEY, JSON.stringify(data));
    } catch (error) {
      logger.error('Failed to save user behavior data', error);
    }
  }

  /**
   * Save conversion funnel data to localStorage
   */
  private saveConversionData(): void {
    try {
      localStorage.setItem(CONVERSION_FUNNEL_KEY, JSON.stringify(this.conversionEvents));
    } catch (error) {
      logger.error('Failed to save conversion data', error);
    }
  }

  /**
   * Record a sale for analytics
   */
  recordSale(productId: string, revenue: number, quantity: number): void {
    try {
      const today = new Date().toISOString().split('T')[0];
      const dailyData = this.salesHistory.get(today) || [0];
      dailyData[0] += revenue;
      this.salesHistory.set(today, dailyData);
      this.saveSalesData();

      // Also record in inventory analytics
      inventoryAnalyticsService.recordSale(productId, quantity);

      logger.userAction('sale_recorded', {
        productId,
        revenue,
        quantity,
        date: today
      });
    } catch (error) {
      logger.error('Failed to record sale', error);
    }
  }

  /**
   * Track user session
   */
  trackUserSession(sessionId: string, data: any): void {
    try {
      this.userSessions.set(sessionId, {
        ...this.userSessions.get(sessionId),
        ...data,
        lastActivity: Date.now()
      });
      this.saveUserBehaviorData();
    } catch (error) {
      logger.error('Failed to track user session', error);
    }
  }

  /**
   * Record conversion event
   */
  recordConversionEvent(stage: string, sessionId: string, data?: any): void {
    try {
      const event = {
        stage,
        sessionId,
        timestamp: Date.now(),
        data
      };
      this.conversionEvents.push(event);

      // Keep only recent events (last 30 days)
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      this.conversionEvents = this.conversionEvents.filter(e => e.timestamp > thirtyDaysAgo);

      this.saveConversionData();
    } catch (error) {
      logger.error('Failed to record conversion event', error);
    }
  }

  /**
   * Calculate sales metrics
   */
  calculateSalesMetrics(products: Product[]): SalesMetrics {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

    let totalRevenue = 0;
    let totalOrders = 0;
    const salesByTimeframe: TimeframeSalesMetric[] = [];

    // Calculate revenue and orders from sales history
    for (const [dateStr, dailyData] of this.salesHistory.entries()) {
      const date = new Date(dateStr);
      if (date >= thirtyDaysAgo) {
        const dailyRevenue = dailyData[0] || 0;
        totalRevenue += dailyRevenue;
        totalOrders += Math.floor(dailyRevenue / 50); // Estimate orders based on average order value

        salesByTimeframe.push({
          period: dateStr,
          revenue: dailyRevenue,
          orders: Math.floor(dailyRevenue / 50),
          date: dateStr
        });
      }
    }

    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate growth (mock calculation)
    const revenueGrowth = Math.random() * 20 - 5; // -5% to +15%
    const orderGrowth = Math.random() * 15 - 3; // -3% to +12%

    // Mock top selling products
    const topSellingProducts: ProductSalesMetric[] = products.slice(0, 5).map(product => ({
      productId: product.id,
      productName: product.name,
      revenue: Math.floor(Math.random() * 5000) + 1000,
      unitsSold: Math.floor(Math.random() * 100) + 20,
      averageRating: product.rating || 4.5,
      conversionRate: Math.random() * 0.1 + 0.02 // 2-12%
    }));

    // Mock sales by category
    const categories = ['Skincare', 'Haircare', 'Wellness', 'Fragrance'];
    const salesByCategory: CategorySalesMetric[] = categories.map(category => ({
      category,
      revenue: Math.floor(Math.random() * 10000) + 2000,
      unitsSold: Math.floor(Math.random() * 200) + 50,
      growth: Math.random() * 30 - 10 // -10% to +20%
    }));

    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      revenueGrowth,
      orderGrowth,
      topSellingProducts,
      salesByCategory,
      salesByTimeframe: salesByTimeframe.sort((a, b) => a.date.localeCompare(b.date)),
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Calculate user behavior metrics
   */
  calculateUserBehaviorMetrics(): UserBehaviorMetrics {
    const sessions = Array.from(this.userSessions.values());
    const totalSessions = sessions.length;

    // Calculate average session duration
    const averageSessionDuration = sessions.reduce((total, session) => {
      const duration = (session.lastActivity || Date.now()) - (session.startTime || Date.now());
      return total + duration;
    }, 0) / totalSessions / 1000 / 60; // Convert to minutes

    // Calculate bounce rate (sessions with only 1 page view)
    const bouncedSessions = sessions.filter(session => (session.pageViews || 1) === 1).length;
    const bounceRate = (bouncedSessions / totalSessions) * 100;

    // Calculate average page views per session
    const pageViewsPerSession = sessions.reduce((total, session) =>
      total + (session.pageViews || 1), 0) / totalSessions;

    // Mock top pages
    const topPages: PageMetric[] = [
      { page: '/products', views: 1250, uniqueViews: 980, averageTimeOnPage: 145, exitRate: 25 },
      { page: '/', views: 890, uniqueViews: 750, averageTimeOnPage: 95, exitRate: 35 },
      { page: '/cart', views: 450, uniqueViews: 420, averageTimeOnPage: 180, exitRate: 15 },
      { page: '/checkout', views: 280, uniqueViews: 270, averageTimeOnPage: 240, exitRate: 8 },
      { page: '/account', views: 320, uniqueViews: 280, averageTimeOnPage: 200, exitRate: 20 }
    ];

    // Mock user flow
    const userFlow: UserFlowMetric[] = [
      { fromPage: '/', toPage: '/products', count: 450, conversionRate: 50.6 },
      { fromPage: '/products', toPage: '/product-detail', count: 380, conversionRate: 30.4 },
      { fromPage: '/product-detail', toPage: '/cart', count: 180, conversionRate: 47.4 },
      { fromPage: '/cart', toPage: '/checkout', count: 120, conversionRate: 26.7 },
      { fromPage: '/checkout', toPage: '/order-confirmation', count: 85, conversionRate: 30.4 }
    ];

    // Calculate device breakdown
    const deviceCounts = sessions.reduce((acc, session) => {
      const device = session.deviceType || 'desktop';
      acc[device] = (acc[device] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const deviceBreakdown: DeviceMetric[] = Object.entries(deviceCounts).map(([deviceType, count]) => ({
      deviceType,
      sessions: count,
      percentage: (count / totalSessions) * 100
    }));

    // Calculate traffic sources
    const sourceCounts = sessions.reduce((acc, session) => {
      const source = session.source || 'direct';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const trafficSources: TrafficSourceMetric[] = Object.entries(sourceCounts).map(([source, count]) => ({
      source,
      sessions: count,
      conversionRate: Math.random() * 5 + 2, // 2-7%
      percentage: (count / totalSessions) * 100
    }));

    return {
      totalSessions,
      averageSessionDuration,
      bounceRate,
      pageViewsPerSession,
      topPages,
      userFlow,
      deviceBreakdown,
      trafficSources,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Calculate conversion funnel metrics
   */
  calculateConversionFunnelMetrics(): ConversionFunnelMetrics {
    const _stages = [
      'Landing Page Visit',
      'Product Page View',
      'Add to Cart',
      'Checkout Started',
      'Order Completed'
    ];

    // Mock funnel data based on conversion events
    const funnelData: FunnelStage[] = [
      { stage: 'Landing Page Visit', users: 1000, conversionRate: 100, dropOffRate: 0 },
      { stage: 'Product Page View', users: 650, conversionRate: 65, dropOffRate: 35 },
      { stage: 'Add to Cart', users: 280, conversionRate: 43.1, dropOffRate: 56.9 },
      { stage: 'Checkout Started', users: 150, conversionRate: 53.6, dropOffRate: 46.4 },
      { stage: 'Order Completed', users: 85, conversionRate: 56.7, dropOffRate: 43.3 }
    ];

    const overallConversionRate = (funnelData[funnelData.length - 1].users / funnelData[0].users) * 100;

    const dropOffPoints: DropOffPoint[] = [
      {
        stage: 'Product Page View',
        dropOffRate: 35,
        commonReasons: ['Price too high', 'Product not as expected', 'Comparison shopping']
      },
      {
        stage: 'Add to Cart',
        dropOffRate: 56.9,
        commonReasons: ['Shipping costs', 'Account creation required', 'Payment concerns']
      },
      {
        stage: 'Checkout Started',
        dropOffRate: 46.4,
        commonReasons: ['Complex checkout', 'Unexpected fees', 'Security concerns']
      }
    ];

    const averageTimeToConvert = 2.5; // days

    return {
      stages: funnelData,
      overallConversionRate,
      dropOffPoints,
      averageTimeToConvert,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Calculate KPI metrics
   */
  calculateKPIMetrics(): KPIMetrics {
    return {
      customerLifetimeValue: 285.50,
      customerAcquisitionCost: 45.20,
      returnCustomerRate: 32.8,
      averageOrderFrequency: 2.3,
      inventoryTurnover: 6.2,
      grossMargin: 68.5,
      netPromoterScore: 72,
      customerSatisfactionScore: 4.3
    };
  }

  /**
   * Get comprehensive business analytics
   */
  getBusinessAnalytics(products: Product[], reviews: Review[]): BusinessAnalytics {
    try {
      const salesMetrics = this.calculateSalesMetrics(products);
      const userBehaviorMetrics = this.calculateUserBehaviorMetrics();
      const conversionFunnelMetrics = this.calculateConversionFunnelMetrics();
      const kpis = this.calculateKPIMetrics();

      // Get existing analytics from other services
      const inventoryMetrics = inventoryAnalyticsService.getInventoryAnalytics(products);
      const reviewMetrics = getReviewAnalytics(reviews);

      const analytics: BusinessAnalytics = {
        salesMetrics,
        userBehaviorMetrics,
        conversionFunnelMetrics,
        inventoryMetrics,
        reviewMetrics,
        kpis,
        lastUpdated: new Date().toISOString()
      };

      // Cache the results
      localStorage.setItem(BUSINESS_ANALYTICS_KEY, JSON.stringify(analytics));

      logger.performance('business_analytics_calculated', Date.now(), {
        totalRevenue: salesMetrics.totalRevenue,
        totalSessions: userBehaviorMetrics.totalSessions,
        conversionRate: conversionFunnelMetrics.overallConversionRate
      });

      return analytics;
    } catch (error) {
      logger.error('Failed to calculate business analytics', error);
      throw error;
    }
  }

  /**
   * Get cached analytics or calculate new ones
   */
  getCachedBusinessAnalytics(products: Product[], reviews: Review[]): BusinessAnalytics | null {
    try {
      const cached = localStorage.getItem(BUSINESS_ANALYTICS_KEY);
      if (cached) {
        const analytics = JSON.parse(cached);
        const cacheAge = Date.now() - new Date(analytics.lastUpdated).getTime();

        // Return cached data if less than 5 minutes old
        if (cacheAge < 5 * 60 * 1000) {
          return analytics;
        }
      }

      return this.getBusinessAnalytics(products, reviews);
    } catch (error) {
      logger.error('Failed to get cached business analytics', error);
      return null;
    }
  }

  /**
   * Clear analytics cache
   */
  clearAnalyticsCache(): void {
    try {
      localStorage.removeItem(BUSINESS_ANALYTICS_KEY);
      localStorage.removeItem(SALES_DATA_KEY);
      localStorage.removeItem(USER_BEHAVIOR_ANALYTICS_KEY);
      localStorage.removeItem(CONVERSION_FUNNEL_KEY);

      // Reinitialize with fresh mock data
      this.salesHistory.clear();
      this.userSessions.clear();
      this.conversionEvents = [];
      this.initializeMockData();

      logger.userAction('analytics_cache_cleared');
    } catch (error) {
      logger.error('Failed to clear analytics cache', error);
    }
  }

  /**
   * Export analytics data to CSV
   */
  exportAnalyticsToCSV(analytics: BusinessAnalytics): string {
    try {
      const csvData = [
        ['Metric', 'Value', 'Category'],
        ['Total Revenue', analytics.salesMetrics.totalRevenue.toString(), 'Sales'],
        ['Total Orders', analytics.salesMetrics.totalOrders.toString(), 'Sales'],
        ['Average Order Value', analytics.salesMetrics.averageOrderValue.toFixed(2), 'Sales'],
        ['Revenue Growth', analytics.salesMetrics.revenueGrowth.toFixed(1) + '%', 'Sales'],
        ['Total Sessions', analytics.userBehaviorMetrics.totalSessions.toString(), 'Behavior'],
        ['Bounce Rate', analytics.userBehaviorMetrics.bounceRate.toFixed(1) + '%', 'Behavior'],
        ['Conversion Rate', analytics.conversionFunnelMetrics.overallConversionRate.toFixed(1) + '%', 'Conversion'],
        ['Customer Lifetime Value', analytics.kpis.customerLifetimeValue.toFixed(2), 'KPI'],
        ['Net Promoter Score', analytics.kpis.netPromoterScore.toString(), 'KPI']
      ];

      return csvData.map(row => row.join(',')).join('\n');
    } catch (error) {
      logger.error('Failed to export analytics to CSV', error);
      return '';
    }
  }
}

// Export singleton instance
export const businessAnalyticsService = new BusinessAnalyticsService();
export default businessAnalyticsService;
