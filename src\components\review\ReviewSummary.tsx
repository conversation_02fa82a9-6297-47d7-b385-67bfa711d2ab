import { useEffect, useState } from "react";
import { useReviews } from "@/contexts/ReviewContext";
import { ReviewSummary as ReviewSummaryType } from "@/types/review";
import { StarRating } from "@/components/ui/star-rating";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";

interface ReviewSummaryProps {
  productId: string;
  onWriteReviewClick: () => void;
}

const ReviewSummary = ({ productId, onWriteReviewClick }: ReviewSummaryProps) => {
  const { getSummary, isLoadingSummary: _isLoadingSummary, reviewSummaries } = useReviews();
  const [summary, setSummary] = useState<ReviewSummaryType | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSummary = async () => {
      setIsLoading(true);
      try {
        // Check if we already have the summary in context
        if (reviewSummaries[productId]) {
          setSummary(reviewSummaries[productId]);
        } else {
          // Otherwise fetch it
          const data = await getSummary(productId);
          setSummary(data);
        }
      } catch (error) {
        console.error("Error fetching review summary:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [productId, getSummary, reviewSummaries]);

  if (isLoading) {
    return (
      <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border animate-pulse">
        <div className="h-6 w-1/3 bg-sabone-gold/20 rounded mb-4"></div>
        <div className="h-4 w-1/2 bg-sabone-gold/20 rounded mb-2"></div>
        <div className="h-4 w-1/4 bg-sabone-gold/20 rounded mb-4"></div>
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((star) => (
            <div key={star} className="flex items-center">
              <div className="h-4 w-16 bg-sabone-gold/20 rounded mr-2"></div>
              <div className="h-4 w-full bg-sabone-gold/20 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!summary) {
    return null;
  }

  const { averageRating, totalReviews, ratingDistribution } = summary;
  const hasReviews = totalReviews > 0;

  // Get the highest count for any rating to calculate percentages
  const maxCount = hasReviews
    ? Math.max(...Object.values(ratingDistribution))
    : 0;

  return (
    <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
      <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-4">
        Customer Reviews
      </h3>

      <div className="flex flex-col md:flex-row md:items-center gap-4 mb-6">
        <div className="flex flex-col items-center md:items-start">
          {hasReviews ? (
            <>
              <div className="flex items-center gap-2">
                <span className="text-3xl font-bold text-sabone-gold">
                  {averageRating.toFixed(1)}
                </span>
                <StarRating rating={averageRating} size="md" />
              </div>
              <p className="text-sabone-cream/70 text-sm">
                Based on {totalReviews} review{totalReviews !== 1 ? "s" : ""}
              </p>
            </>
          ) : (
            <p className="text-sabone-cream/70">No reviews yet</p>
          )}
        </div>

        <div className="md:ml-auto">
          <Button
            onClick={onWriteReviewClick}
            className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
          >
            Write a Review
          </Button>
        </div>
      </div>

      {hasReviews && (
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((star) => (
            <div key={star} className="flex items-center">
              <div className="w-16 flex items-center">
                <span className="text-sabone-cream mr-1">{star}</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-4 h-4 text-sabone-gold"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="flex-1 ml-2">
                <Progress
                  value={
                    maxCount > 0
                      ? (ratingDistribution[star as keyof typeof ratingDistribution] /
                          maxCount) *
                        100
                      : 0
                  }
                  className="h-2 bg-sabone-charcoal"
                  indicatorClassName="bg-sabone-gold"
                />
              </div>
              <span className="ml-2 text-sm text-sabone-cream/70 w-10 text-right">
                {ratingDistribution[star as keyof typeof ratingDistribution]}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewSummary;
