import { useState, useEffect } from "react";
import { useLocale } from "next-intl";
import SignInModal from "@/components/auth/SignInModal";
import SignUpModal from "@/components/auth/SignUpModal";
import MobileNavigation from "@/components/ui/mobile/MobileNavigation";
import DesktopNavigation from "@/components/ui/mobile/DesktopNavigation";
import useMobile from "@/hooks/useMobile";

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const [isSignUpModalOpen, setIsSignUpModalOpen] = useState(false);
  const locale = useLocale();
  const { enablePullToRefresh } = useMobile();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Enable pull-to-refresh functionality on mobile
  useEffect(() => {
    const cleanup = enablePullToRefresh(() => {
      window.location.reload();
    });
    return cleanup;
  }, [enablePullToRefresh]);

  const handleSignInClick = () => {
    console.log('Navbar: Opening sign-in modal');
    setIsSignInModalOpen(false);
    setTimeout(() => {
      setIsSignInModalOpen(true);
    }, 50);
  };

  const handleSignUpClick = () => {
    console.log('Navbar: Opening sign-up modal');
    setIsSignUpModalOpen(false);
    setTimeout(() => {
      setIsSignUpModalOpen(true);
    }, 50);
  };

  return (
    <header
      key={`navbar-${locale}`}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-sabone-charcoal-deep/85 backdrop-blur-[5px] shadow-[0_4px_20px_rgba(0,0,0,0.2)] border-b border-sabone-gold/10"
          : "bg-transparent"
      }`}
    >
      <div className="navbar-inner-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mobile Navigation */}
        <MobileNavigation 
          isScrolled={isScrolled}
          onSignInClick={handleSignInClick}
          onSignUpClick={handleSignUpClick}
        />
        
        {/* Desktop Navigation */}
        <DesktopNavigation 
          onSignInClick={handleSignInClick}
        />
      </div>

      {/* Authentication Modals */}
      <SignInModal
        open={isSignInModalOpen}
        onOpenChange={(open) => {
          console.log('SignInModal onOpenChange called with', open);
          setIsSignInModalOpen(open);
        }}
        onSignUpClick={() => {
          setIsSignInModalOpen(false);
          setTimeout(() => {
            setIsSignUpModalOpen(true);
          }, 50);
        }}
      />
      <SignUpModal
        open={isSignUpModalOpen}
        onOpenChange={(open) => {
          console.log('SignUpModal onOpenChange called with', open);
          setIsSignUpModalOpen(open);
        }}
        onSignInClick={() => {
          setIsSignUpModalOpen(false);
          setTimeout(() => {
            setIsSignInModalOpen(true);
          }, 50);
        }}
      />
    </header>
  );
};

export default Navbar;
