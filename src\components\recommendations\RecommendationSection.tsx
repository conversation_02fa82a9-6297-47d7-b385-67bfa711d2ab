import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Product } from '@/data/products';
import { Card } from '@/components/ui/card';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import { useRecommendations } from '@/contexts/RecommendationContext';
import { logger } from '@/utils/logger';
import { getSessionId } from '@/utils/recommendationUtils';

interface RecommendationSectionProps {
  title: string;
  subtitle?: string;
  products: Product[];
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  maxVisible?: number;
  showRefreshButton?: boolean;
  className?: string;
  trackingType: string;
}

const RecommendationSection: React.FC<RecommendationSectionProps> = ({
  title,
  subtitle,
  products,
  loading = false,
  error = null,
  onRefresh,
  maxVisible = 4,
  showRefreshButton = false,
  className = '',
  trackingType
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [_loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});
  const { trackProductView } = useRecommendations();

  // Reset index when products change
  useEffect(() => {
    setCurrentIndex(0);
  }, [products]);

  const handleImageLoad = (productId: string) => {
    setLoadedImages(prev => ({ ...prev, [productId]: true }));
  };

  const handleProductClick = (productId: string) => {
    trackProductView(productId);
    logger.userAction('recommendation_clicked', {
      productId,
      recommendationType: trackingType,
      sessionId: getSessionId()
    });
  };

  const handlePrevious = () => {
    setCurrentIndex(prev => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => Math.min(products.length - maxVisible, prev + 1));
  };

  const visibleProducts = products.slice(currentIndex, currentIndex + maxVisible);
  const canScrollLeft = currentIndex > 0;
  const canScrollRight = currentIndex < products.length - maxVisible;

  if (loading) {
    return (
      <section className={`py-8 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-2">
                {title}
              </h2>
              {subtitle && (
                <p className="text-sabone-cream/80 text-sm">{subtitle}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: maxVisible }).map((_, index) => (
              <Card key={index} className="bg-sabone-dark-olive/60 border-sabone-gold/20 rounded-md overflow-hidden">
                <div className="p-0">
                  <AspectRatio ratio={3/4}>
                    <Skeleton className="w-full h-full bg-sabone-dark-olive/40" />
                  </AspectRatio>
                  <div className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2 bg-sabone-dark-olive/40" />
                    <Skeleton className="h-4 w-1/2 bg-sabone-dark-olive/40" />
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className={`py-8 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-2">
                {title}
              </h2>
              {subtitle && (
                <p className="text-sabone-cream/80 text-sm">{subtitle}</p>
              )}
            </div>
            {onRefresh && (
              <Button
                onClick={onRefresh}
                variant="outline"
                size="sm"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            )}
          </div>
          
          <div className="text-center py-8">
            <p className="text-sabone-cream/60 mb-4">{error}</p>
            {onRefresh && (
              <Button
                onClick={onRefresh}
                variant="outline"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              >
                Try Again
              </Button>
            )}
          </div>
        </div>
      </section>
    );
  }

  if (products.length === 0) {
    return null;
  }

  return (
    <section className={`py-8 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-2">
              {title}
            </h2>
            {subtitle && (
              <p className="text-sabone-cream/80 text-sm">{subtitle}</p>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {showRefreshButton && onRefresh && (
              <Button
                onClick={onRefresh}
                variant="outline"
                size="sm"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            )}
            
            {products.length > maxVisible && (
              <div className="flex gap-2">
                <Button
                  onClick={handlePrevious}
                  disabled={!canScrollLeft}
                  variant="outline"
                  size="sm"
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10 disabled:opacity-50"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  onClick={handleNext}
                  disabled={!canScrollRight}
                  variant="outline"
                  size="sm"
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10 disabled:opacity-50"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {visibleProducts.map((product) => (
            <Link
              key={product.id}
              to={`/product/${product.id}`}
              onClick={() => handleProductClick(product.id)}
              className="block transition-transform hover:scale-[1.02] duration-300"
            >
              <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20 rounded-md overflow-hidden h-full transition-all duration-300 gold-border hover:border-sabone-gold/40">
                <div className="p-0 relative">
                  <AspectRatio ratio={3/4} className="overflow-hidden">
                    <OptimizedImage
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full"
                      objectFit="cover"
                      onLoad={() => handleImageLoad(product.id)}
                    />

                    {/* Product Info Overlay */}
                    <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm p-3 rounded-md">
                      <h3 className="text-base font-playfair font-medium text-sabone-gold truncate mb-1">
                        {product.name}
                      </h3>
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-sabone-gold text-sm md:text-base">
                          ${product.price.toFixed(2)}
                        </span>
                        <span className="text-xs text-sabone-cream/70 capitalize">
                          {product.type}
                        </span>
                      </div>
                    </div>
                  </AspectRatio>
                </div>
              </Card>
            </Link>
          ))}
        </div>

        {products.length > maxVisible && (
          <div className="flex justify-center mt-6">
            <div className="flex items-center gap-2 text-sm text-sabone-cream/60">
              <span>
                Showing {currentIndex + 1}-{Math.min(currentIndex + maxVisible, products.length)} of {products.length}
              </span>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default RecommendationSection;
