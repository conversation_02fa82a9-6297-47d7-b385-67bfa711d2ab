import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import SearchBar from '@/components/search/SearchBar';
import FilterPanel from '@/components/search/FilterPanel';
import SortOptions from '@/components/search/SortOptions';
import SearchResults from '@/components/search/SearchResults';
import MobileFilterDrawer from '@/components/search/MobileFilterDrawer';
import SEO from '@/components/seo/SEO';
import { SearchProvider, useSearch } from '@/contexts/SearchContext';
import { useMobile } from '@/hooks/useMobile';
import { cn } from '@/lib/utils';

const SearchPageContent: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { isMobile } = useMobile();
  const {
    query,
    setQuery,
    filters,
    setFilters,
    sortBy,
    setSortBy,
    totalResults,
    hasActiveFilters,
  } = useSearch();

  // Sync URL params with search state
  useEffect(() => {
    const urlQuery = searchParams.get('q') || '';
    const urlCategory = searchParams.get('category');
    const urlType = searchParams.get('type') as 'bar' | 'liquid' | null;
    const urlMinPrice = searchParams.get('minPrice');
    const urlMaxPrice = searchParams.get('maxPrice');
    const urlInStock = searchParams.get('inStock');
    const urlFeatured = searchParams.get('featured');
    const urlSort = searchParams.get('sort');
    const urlOrder = searchParams.get('order') as 'asc' | 'desc';

    // Set query if different
    if (urlQuery !== query) {
      setQuery(urlQuery);
    }

    // Set filters if different
    const urlFilters: any = {};
    if (urlCategory) urlFilters.category = urlCategory;
    if (urlType) urlFilters.type = urlType;
    if (urlMinPrice && urlMaxPrice) {
      urlFilters.priceRange = {
        min: parseInt(urlMinPrice),
        max: parseInt(urlMaxPrice),
      };
    }
    if (urlInStock === 'true') urlFilters.inStock = true;
    if (urlFeatured === 'true') urlFilters.featured = true;

    if (JSON.stringify(urlFilters) !== JSON.stringify(filters)) {
      setFilters(urlFilters);
    }

    // Set sort if different
    if (urlSort && urlOrder) {
      const urlSortBy = { field: urlSort as any, direction: urlOrder };
      if (JSON.stringify(urlSortBy) !== JSON.stringify(sortBy)) {
        setSortBy(urlSortBy);
      }
    }
  }, [searchParams]);

  // Update URL when search state changes
  useEffect(() => {
    const params = new URLSearchParams();

    if (query) params.set('q', query);
    if (filters.category) params.set('category', filters.category);
    if (filters.type) params.set('type', filters.type);
    if (filters.priceRange) {
      params.set('minPrice', filters.priceRange.min.toString());
      params.set('maxPrice', filters.priceRange.max.toString());
    }
    if (filters.inStock) params.set('inStock', 'true');
    if (filters.featured) params.set('featured', 'true');
    if (sortBy.field !== 'name' || sortBy.direction !== 'asc') {
      params.set('sort', sortBy.field);
      params.set('order', sortBy.direction);
    }

    setSearchParams(params, { replace: true });
  }, [query, filters, sortBy, setSearchParams]);

  const pageTitle = query 
    ? `Search results for "${query}" - Sabone`
    : hasActiveFilters 
    ? 'Filtered Products - Sabone'
    : 'Search Products - Sabone';

  const pageDescription = query
    ? `Find ${totalResults} products matching "${query}" in our natural soap and shampoo collection.`
    : 'Search and filter our premium collection of natural soaps and shampoos. Find the perfect products for your skin and hair care needs.';

  return (
    <HelmetProvider>
      <div className="min-h-screen bg-sabone-charcoal bg-[radial-gradient(ellipse_at_center,_rgba(50,50,40,0.1)_0%,_transparent_70%)]">
        <SEO
          title={pageTitle}
          description={pageDescription}
          keywords="search, natural soap, organic shampoo, skincare, haircare, filter, sort"
          canonical="/search"
        />

        <Navbar />

        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Search Header */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h1 className="text-3xl md:text-4xl font-bold text-sabone-cream mb-2">
                {query ? 'Search Results' : 'Find Your Perfect Products'}
              </h1>
              <p className="text-sabone-cream/80 max-w-2xl mx-auto">
                {query 
                  ? `Showing results for "${query}"`
                  : 'Search through our premium collection of natural soaps and shampoos'
                }
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-3xl mx-auto mb-6">
              <SearchBar 
                autoFocus={!query}
                className="w-full"
              />
            </div>

            {/* Mobile Controls */}
            {isMobile && (
              <div className="flex items-center justify-between gap-4 mb-6">
                <MobileFilterDrawer />
                <SortOptions showLabel={false} />
              </div>
            )}

            {/* Desktop Controls */}
            {!isMobile && (
              <div className="flex items-center justify-between mb-6">
                <div className="text-sm text-sabone-cream/60">
                  {totalResults > 0 && (
                    <span>
                      Showing {totalResults} {totalResults === 1 ? 'product' : 'products'}
                      {hasActiveFilters && ' with filters applied'}
                    </span>
                  )}
                </div>
                <SortOptions />
              </div>
            )}
          </div>

          {/* Main Content */}
          <div className={cn(
            "grid gap-8",
            isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-4"
          )}>
            {/* Desktop Sidebar */}
            {!isMobile && (
              <aside className="lg:col-span-1">
                <div className="sticky top-8">
                  <FilterPanel />
                </div>
              </aside>
            )}

            {/* Results */}
            <div className={cn(
              isMobile ? "col-span-1" : "lg:col-span-3"
            )}>
              <SearchResults 
                gridCols={isMobile ? 1 : 3}
                showPagination={true}
              />
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </HelmetProvider>
  );
};

const SearchPage: React.FC = () => {
  return (
    <SearchProvider>
      <SearchPageContent />
    </SearchProvider>
  );
};

export default SearchPage;
