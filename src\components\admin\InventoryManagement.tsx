import { useState, useEffect } from "react";
import { useInventory } from "@/contexts/InventoryContext";
import { products } from "@/data/products";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Package,
  RefreshCw,
  Search,
  Filter,
  Save,
  CheckCircle2,
  Bell,
  History,
  Settings
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

// Mock inventory history data
const mockInventoryHistory = [
  {
    id: 1,
    productId: 'p1',
    action: 'restock',
    quantity: 15,
    date: '2023-12-01T10:30:00Z',
    user: 'Admin User'
  },
  {
    id: 2,
    productId: 'p2',
    action: 'restock',
    quantity: 10,
    date: '2023-12-02T14:15:00Z',
    user: 'Admin User'
  },
  {
    id: 3,
    productId: 'p3',
    action: 'adjustment',
    quantity: -2,
    date: '2023-12-03T09:45:00Z',
    user: 'Admin User'
  },
  {
    id: 4,
    productId: 'p1',
    action: 'order',
    quantity: -3,
    date: '2023-12-04T16:20:00Z',
    user: 'System'
  },
  {
    id: 5,
    productId: 'p4',
    action: 'restock',
    quantity: 20,
    date: '2023-12-05T11:10:00Z',
    user: 'Admin User'
  }
];

const InventoryManagement = () => {
  const {
    inventory,
    loading,
    _getProductStock,
    isProductInStock,
    isLowStock,
    updateProductStock,
    refreshInventory,
    lowStockItems
  } = useInventory();

  const [selectedProduct, setSelectedProduct] = useState(null);
  const [restockQuantity, setRestockQuantity] = useState(10);
  const [isRestocking, setIsRestocking] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [batchRestockQuantity, setBatchRestockQuantity] = useState(10);
  const [showBatchDialog, setShowBatchDialog] = useState(false);
  const [_showProductDetailsDialog, _setShowProductDetailsDialog] = useState(false);
  const [_showHistoryDialog, _setShowHistoryDialog] = useState(false);
  const [_showNotificationsDialog, _setShowNotificationsDialog] = useState(false);
  const [_productHistory, _setProductHistory] = useState(mockInventoryHistory);
  const [_editMode, _setEditMode] = useState(false);
  const [productToEdit, setProductToEdit] = useState(null);
  const [sortOrder, setSortOrder] = useState({ field: 'name', direction: 'asc' });
  const [activeTab, setActiveTab] = useState("inventory");
  const [_notificationSettings, _setNotificationSettings] = useState({
    lowStockThreshold: 5,
    enableEmailNotifications: true,
    enablePushNotifications: false,
  });
  const _isMobile = useIsMobile();

  // Refresh inventory data on mount
  useEffect(() => {
    refreshInventory();
  }, [refreshInventory]);

  // Handle restock for a single product
  const handleRestock = async (product) => {
    if (!product) return;

    setIsRestocking(true);

    try {
      await updateProductStock(product.id, restockQuantity, 'restock');
      toast.success(`Restocked ${product.name} with ${restockQuantity} units`);
      setSelectedProduct(null);
      setRestockQuantity(10);
    } catch (error) {
      toast.error("Failed to restock product");
      console.error(error);
    } finally {
      setIsRestocking(false);
    }
  };

  // Handle batch restock for multiple products
  const handleBatchRestock = async () => {
    if (selectedProducts.length === 0) {
      toast.error("No products selected for batch restock");
      return;
    }

    setIsRestocking(true);

    try {
      for (const productId of selectedProducts) {
        await updateProductStock(productId, batchRestockQuantity, 'batch-restock');
      }

      toast.success(`Restocked ${selectedProducts.length} products with ${batchRestockQuantity} units each`);
      setSelectedProducts([]);
      setBatchRestockQuantity(10);
      setShowBatchDialog(false);
    } catch (error) {
      toast.error("Failed to perform batch restock");
      console.error(error);
    } finally {
      setIsRestocking(false);
    }
  };

  // Toggle product selection for batch operations
  const toggleProductSelection = (productId) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // Handle viewing product details
  const _handleViewProductDetails = (product) => {
    setProductToEdit({ ...product });
    _setShowProductDetailsDialog(true);
    _setEditMode(false);
  };

  // Handle editing product
  const _handleEditProduct = (product) => {
    setProductToEdit({ ...product });
    _setShowProductDetailsDialog(true);
    _setEditMode(true);
  };

  // Handle saving product changes
  const _handleSaveProductChanges = async () => {
    if (!productToEdit) return;

    setIsRestocking(true);

    try {
      // Simulate API call to update product
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the product in the UI
      // In a real app, this would be handled by your state management system

      toast.success(`Product "${productToEdit.name}" updated successfully`);
      _setShowProductDetailsDialog(false);
      setProductToEdit(null);
      _setEditMode(false);
    } catch (error) {
      toast.error("Failed to update product");
      console.error(error);
    } finally {
      setIsRestocking(false);
    }
  };

  // Handle viewing inventory history
  const _handleViewHistory = (product) => {
    setSelectedProduct(product);
    _setShowHistoryDialog(true);

    // In a real app, you would fetch the history for this specific product
    const filteredHistory = mockInventoryHistory.filter(
      item => item.productId === product.id
    );

    _setProductHistory(filteredHistory.length > 0 ? filteredHistory : mockInventoryHistory);
  };

  // Handle saving notification settings
  const _handleSaveNotificationSettings = () => {
    toast.success("Notification settings updated");
    _setShowNotificationsDialog(false);
  };

  // Handle sorting
  const _handleSort = (field) => {
    setSortOrder(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Filter and search products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.type.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterStatus === "all") return matchesSearch;
    if (filterStatus === "in-stock") return matchesSearch && isProductInStock(product.id);
    if (filterStatus === "low-stock") return matchesSearch && isLowStock(product.id);
    if (filterStatus === "out-of-stock") return matchesSearch && !isProductInStock(product.id);

    return matchesSearch;
  });

  // Sort products
  const _sortedProducts = [...filteredProducts].sort((a, b) => {
    const aValue = a[sortOrder.field];
    const bValue = b[sortOrder.field];

    if (sortOrder.direction === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Inventory Management</h2>
          <p className="text-sabone-cream/70 mt-1">Manage product stock levels and inventory</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => refreshInventory()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => _setShowNotificationsDialog(true)}
          >
            <Bell className="h-4 w-4 mr-2" />
            Alerts
            {lowStockItems.length > 0 && (
              <Badge className="ml-2 bg-red-500 hover:bg-red-600 text-white">{lowStockItems.length}</Badge>
            )}
          </Button>

          {selectedProducts.length > 0 && (
            <Button
              variant="default"
              className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
              onClick={() => setShowBatchDialog(true)}
            >
              <Package className="h-4 w-4 mr-2" />
              Batch Restock ({selectedProducts.length})
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="bg-sabone-dark-olive h-12 p-1 mb-6">
          <TabsTrigger
            value="inventory"
            className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px]"
          >
            <Package className="h-4 w-4 mr-2" />
            Inventory
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px]"
          >
            <History className="h-4 w-4 mr-2" />
            History
          </TabsTrigger>
          <TabsTrigger
            value="settings"
            className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px]"
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="mt-0">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sabone-cream/50 h-4 w-4" />
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter className="text-sabone-cream/50 h-4 w-4" />
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[180px] bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
                  <SelectItem value="all" className="text-sabone-cream">All Products</SelectItem>
                  <SelectItem value="in-stock" className="text-sabone-cream">In Stock</SelectItem>
                  <SelectItem value="low-stock" className="text-sabone-cream">Low Stock</SelectItem>
                  <SelectItem value="out-of-stock" className="text-sabone-cream">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator className="bg-sabone-gold/20" />

          <div className="rounded-md border border-sabone-gold/20 overflow-hidden">
            <Table>
              <TableHeader className="bg-sabone-dark-olive/60">
                <TableRow>
                  <TableHead className="w-12 text-sabone-gold">
                    <div className="flex items-center justify-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-sabone-gold/30 bg-transparent"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedProducts(filteredProducts.map(p => p.id));
                          } else {
                            setSelectedProducts([]);
                          }
                        }}
                        checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                      />
                    </div>
                  </TableHead>
                  <TableHead className="text-sabone-gold">Product</TableHead>
                  <TableHead className="text-sabone-gold">Type</TableHead>
                  <TableHead className="text-sabone-gold text-right">Stock</TableHead>
                  <TableHead className="text-sabone-gold text-center">Status</TableHead>
                  <TableHead className="text-sabone-gold">Last Updated</TableHead>
                  <TableHead className="text-sabone-gold text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-sabone-cream/70">
                      No products found matching your criteria
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => {
                    const inventoryItem = inventory.find(item => item.productId === product.id);
                    const stock = inventoryItem ? inventoryItem.stockQuantity : 0;
                    const inStock = isProductInStock(product.id);
                    const lowStock = isLowStock(product.id);

                    return (
                      <TableRow key={product.id} className="hover:bg-sabone-dark-olive/30">
                        <TableCell>
                          <div className="flex items-center justify-center">
                            <input
                              type="checkbox"
                              className="h-4 w-4 rounded border-sabone-gold/30 bg-transparent"
                              checked={selectedProducts.includes(product.id)}
                              onChange={() => toggleProductSelection(product.id)}
                            />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 rounded overflow-hidden bg-sabone-dark-olive/60">
                              <img
                                src={product.image}
                                alt={product.name}
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <div className="font-medium text-sabone-cream">{product.name}</div>
                          </div>
                        </TableCell>
                        <TableCell className="text-sabone-cream/70">{product.type}</TableCell>
                        <TableCell className="text-sabone-cream text-right">{stock}</TableCell>
                        <TableCell className="text-center">
                          {!inStock ? (
                            <Badge variant="destructive" className="bg-red-900/60 hover:bg-red-900/80 text-red-100">
                              Out of Stock
                            </Badge>
                          ) : lowStock ? (
                            <Badge variant="outline" className="border-yellow-600 text-yellow-400">
                              Low Stock
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="border-green-600 text-green-400">
                              In Stock
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-sabone-cream">
                          {inventoryItem ? new Date(inventoryItem.lastUpdated).toLocaleDateString() : 'N/A'}
                        </TableCell>
                        <TableCell className="text-right">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                                onClick={() => setSelectedProduct(product)}
                              >
                                <Package className="h-4 w-4 mr-1" />
                                Restock
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
                              <DialogHeader>
                                <DialogTitle className="text-sabone-gold">Restock {product.name}</DialogTitle>
                              </DialogHeader>
                              <div className="space-y-4 mt-4">
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Current Stock:</span>
                                  <span className="text-sabone-cream">{stock}</span>
                                </div>
                                <div className="space-y-2">
                                  <label className="text-sabone-cream/70">Quantity to Add:</label>
                                  <Input
                                    type="number"
                                    min="1"
                                    value={restockQuantity}
                                    onChange={(e) => setRestockQuantity(parseInt(e.target.value) || 0)}
                                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                                  />
                                </div>
                              </div>
                              <DialogFooter className="mt-6">
                                <DialogClose asChild>
                                  <Button variant="outline" className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10">
                                    Cancel
                                  </Button>
                                </DialogClose>
                                <Button
                                  onClick={() => handleRestock(selectedProduct)}
                                  className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                                  disabled={isRestocking || restockQuantity <= 0}
                                >
                                  {isRestocking ? (
                                    <>
                                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                      Processing...
                                    </>
                                  ) : (
                                    <>
                                      <Save className="h-4 w-4 mr-2" />
                                      Confirm Restock
                                    </>
                                  )}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Batch Restock Dialog */}
          <Dialog open={showBatchDialog} onOpenChange={setShowBatchDialog}>
            <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
              <DialogHeader>
                <DialogTitle className="text-sabone-gold">Batch Restock Products</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div className="flex items-center justify-between">
                  <span className="text-sabone-cream/70">Selected Products:</span>
                  <span className="text-sabone-cream">{selectedProducts.length}</span>
                </div>
                <div className="space-y-2">
                  <label className="text-sabone-cream/70">Quantity to Add to Each Product:</label>
                  <Input
                    type="number"
                    min="1"
                    value={batchRestockQuantity}
                    onChange={(e) => setBatchRestockQuantity(parseInt(e.target.value) || 0)}
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                  />
                </div>
                <div className="bg-sabone-charcoal/50 p-4 rounded-md max-h-40 overflow-y-auto">
                  <h4 className="text-sabone-gold mb-2">Products to Restock:</h4>
                  <ul className="space-y-1">
                    {selectedProducts.map(productId => {
                      const product = products.find(p => p.id === productId);
                      return (
                        <li key={productId} className="text-sabone-cream/70 text-sm flex items-center">
                          <CheckCircle2 className="h-3 w-3 mr-2 text-sabone-gold" />
                          {product?.name}
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                  onClick={() => setShowBatchDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleBatchRestock}
                  className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                  disabled={isRestocking || batchRestockQuantity <= 0}
                >
                  {isRestocking ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Confirm Batch Restock
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InventoryManagement;
