import { Helmet, HelmetProvider } from 'react-helmet-async';
import AdminLayout from '@/components/admin/AdminLayout';
import OrderManagement from '@/components/admin/OrderManagement';

const AdminOrders = () => {
  return (
    <HelmetProvider>
      <Helmet>
        <title>Order Management | Sabone Admin</title>
        <meta name="description" content="Order management for Sabone natural soaps and shampoos." />
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>
      
      <AdminLayout title="Order Management">
        <OrderManagement />
      </AdminLayout>
    </HelmetProvider>
  );
};

export default AdminOrders;
