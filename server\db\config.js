import dotenv from 'dotenv';
import { MongoClient } from 'mongodb';

// Initialize dotenv
dotenv.config();

/**
 * Database configuration settings
 */
const dbConfig = {
  // MongoDB connection string
  connectionString: process.env.MONGODB_URI || 'mongodb://localhost:27017/sabone',
  // Database name
  dbName: process.env.DB_NAME || 'sabone',
  // Connection options
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  }
};

/**
 * MongoDB client instance
 */
let client = null;
let db = null;

/**
 * Initializes the database connection
 * @returns {Promise<object>} MongoDB database instance
 */
export const initDatabase = async () => {
  try {
    // Skip actual connection in development if flag is set
    if (process.env.NODE_ENV === 'development' && process.env.SKIP_DB_CONNECTION === 'true') {
      console.warn('⚠️ Database connection skipped in development mode');
      return null;
    }

    console.log('Connecting to MongoDB...');
    
    // Create MongoDB client
    client = new MongoClient(dbConfig.connectionString, dbConfig.options);
    
    // Connect to MongoDB
    await client.connect();
    
    // Get database instance
    db = client.db(dbConfig.dbName);
    
    console.log('✅ Connected to MongoDB successfully');
    return db;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    
    // In development, use in-memory fallback
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Using in-memory database fallback in development mode');
      return null;
    }
    
    throw error;
  }
};

/**
 * Gets the database instance, initializing it if necessary
 * @returns {Promise<object>} MongoDB database instance
 */
export const getDatabase = async () => {
  if (!db) {
    return initDatabase();
  }
  return db;
};

/**
 * Closes the database connection
 * @returns {Promise<void>}
 */
export const closeDatabase = async () => {
  if (client) {
    await client.close();
    console.log('Database connection closed');
    client = null;
    db = null;
  }
};

/**
 * In-memory database fallback for development when no MongoDB is available
 * This provides basic storage functionality but without persistence
 */
class InMemoryDB {
  constructor() {
    this.collections = {};
  }

  /**
   * Gets a collection from the in-memory database
   * @param {string} name - Collection name
   * @returns {object} Collection instance
   */
  collection(name) {
    if (!this.collections[name]) {
      this.collections[name] = new InMemoryCollection(name);
    }
    return this.collections[name];
  }
}

/**
 * In-memory collection implementation
 */
class InMemoryCollection {
  constructor(name) {
    this.name = name;
    this.documents = [];
    this.idCounter = 1;
    console.log(`Created in-memory collection: ${name}`);
  }

  /**
   * Inserts a document into the collection
   * @param {object} doc - Document to insert
   * @returns {Promise<object>} Inserted document
   */
  async insertOne(doc) {
    const newDoc = { ...doc, _id: doc._id || String(this.idCounter++) };
    this.documents.push(newDoc);
    return { insertedId: newDoc._id, acknowledged: true };
  }

  /**
   * Inserts multiple documents into the collection
   * @param {Array<object>} docs - Documents to insert
   * @returns {Promise<object>} Result with insertedIds
   */
  async insertMany(docs) {
    const insertedIds = {};
    for (let i = 0; i < docs.length; i++) {
      const doc = docs[i];
      const newDoc = { ...doc, _id: doc._id || String(this.idCounter++) };
      this.documents.push(newDoc);
      insertedIds[i] = newDoc._id;
    }
    return { insertedIds, acknowledged: true, insertedCount: docs.length };
  }

  /**
   * Finds documents in the collection
   * @param {object} query - Query filter
   * @returns {Promise<Array<object>>} Matching documents
   */
  async find(query = {}) {
    // Basic filtering implementation
    return this.documents.filter(doc => {
      return Object.entries(query).every(([key, value]) => doc[key] === value);
    });
  }

  /**
   * Finds a single document in the collection
   * @param {object} query - Query filter
   * @returns {Promise<object>} Matching document or null
   */
  async findOne(query = {}) {
    return this.documents.find(doc => {
      return Object.entries(query).every(([key, value]) => doc[key] === value);
    }) || null;
  }

  /**
   * Updates a document in the collection
   * @param {object} query - Query filter
   * @param {object} update - Update operations
   * @returns {Promise<object>} Update result
   */
  async updateOne(query, update) {
    const index = this.documents.findIndex(doc => {
      return Object.entries(query).every(([key, value]) => doc[key] === value);
    });

    if (index === -1) {
      return { matchedCount: 0, modifiedCount: 0, acknowledged: true };
    }

    // Handle $set operator
    if (update.$set) {
      this.documents[index] = { ...this.documents[index], ...update.$set };
    }

    return { matchedCount: 1, modifiedCount: 1, acknowledged: true };
  }

  /**
   * Deletes a document from the collection
   * @param {object} query - Query filter
   * @returns {Promise<object>} Delete result
   */
  async deleteOne(query) {
    const initialLength = this.documents.length;
    this.documents = this.documents.filter(doc => {
      return !Object.entries(query).every(([key, value]) => doc[key] === value);
    });
    
    const deletedCount = initialLength - this.documents.length;
    return { deletedCount, acknowledged: true };
  }
}

/**
 * Creates an in-memory database instance
 * @returns {object} In-memory database instance
 */
export const createInMemoryDB = () => {
  console.warn('⚠️ Using in-memory database for development');
  return new InMemoryDB();
};

/**
 * Gets a database instance (real or in-memory based on environment)
 * @returns {Promise<object>} Database instance
 */
export const getDatabaseInstance = async () => {
  // Try to get real database first
  const realDb = await getDatabase().catch(err => {
    console.warn('⚠️ Failed to connect to real database:', err);
    return null;
  });
  
  if (realDb) {
    return realDb;
  }
  
  // Fall back to in-memory DB in development
  if (process.env.NODE_ENV === 'development') {
    return createInMemoryDB();
  }
  
  // In production, don't allow in-memory fallback
  throw new Error('Database connection failed and in-memory fallback is not allowed in production');
}; 