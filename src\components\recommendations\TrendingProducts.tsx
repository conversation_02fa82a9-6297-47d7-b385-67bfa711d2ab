import React, { useState, useEffect } from 'react';
import { Product } from '@/data/products';
import { useRecommendations } from '@/contexts/RecommendationContext';
import RecommendationSection from './RecommendationSection';

interface TrendingProductsProps {
  className?: string;
  maxVisible?: number;
  title?: string;
  subtitle?: string;
}

const TrendingProducts: React.FC<TrendingProductsProps> = ({
  className = '',
  maxVisible = 6,
  title = "Trending Now",
  subtitle = "Popular products our customers love"
}) => {
  const [trendingProducts, setTrendingProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { getTrendingProducts } = useRecommendations();

  const fetchTrendingProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const products = await getTrendingProducts();
      setTrendingProducts(products);
    } catch (err) {
      setError('Failed to load trending products');
      console.error('Error fetching trending products:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrendingProducts();
  }, [getTrendingProducts]);

  return (
    <RecommendationSection
      title={title}
      subtitle={subtitle}
      products={trendingProducts}
      loading={loading}
      error={error}
      onRefresh={fetchTrendingProducts}
      maxVisible={maxVisible}
      showRefreshButton={true}
      className={className}
      trackingType="trending"
    />
  );
};

export default TrendingProducts;
