import React, { createContext, useContext, useState, useEffect } from 'react';
import { useProducts } from './ProductContext';
import { Product } from '@/data/products';

export interface Bundle {
  id: string;
  name: string;
  description: string;
  productIds: string[];
  products: Product[];
  originalPrice: number;
  discountedPrice: number;
  image?: string; // Optional custom image for the bundle
  discountPercentage: number;
}

interface BundleContextType {
  bundles: Bundle[];
  loading: boolean;
  getBundleById: (id: string) => Bundle | undefined;
}

const BundleContext = createContext<BundleContextType | undefined>(undefined);

// Bundle definitions
const bundleDefinitions = [
  {
    id: 'morning-glow',
    name: 'Morning Glow Set',
    description: 'Start your day radiant with this refreshing ritual.',
    productIds: ['lemon-verbena', 'rose-clay', 'shine-silk'],
    discountPercentage: 15, // 15% discount
    image: '/lovable-uploads/Morning glow set.png'
  },
  {
    id: 'scalp-healing',
    name: '<PERSON><PERSON><PERSON> Healing Trio',
    description: 'Complete care for sensitive and troubled scalps.',
    productIds: ['scalp-rescue', 'sensitive-scalp', 'hair-growth'],
    discountPercentage: 12, // 12% discount
    image: '/lovable-uploads/Scalp Healing Trio.png'
  },
  {
    id: 'ritual-senses',
    name: 'Ritual of the Senses',
    description: 'Immerse yourself in aromatic luxury for mind and body.',
    productIds: ['royal-oud', 'white-misk', 'herbal-hammam'],
    discountPercentage: 18, // 18% discount
    image: '/lovable-uploads/Ritual of the Senses.png'
  }
];

export const BundleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { products } = useProducts();
  const [bundles, setBundles] = useState<Bundle[]>([]);
  const [loading, setLoading] = useState(true);

  // Create bundles when products are available
  useEffect(() => {
    if (products.length > 0) {
      const createdBundles = bundleDefinitions.map(def => {
        // Find the products in the bundle
        const bundleProducts = products.filter(product => def.productIds.includes(product.id));

        // Calculate original price (sum of all product prices)
        const originalPrice = bundleProducts.reduce((sum, product) => sum + product.price, 0);

        // Calculate discounted price
        const discountedPrice = Number((originalPrice * (1 - def.discountPercentage / 100)).toFixed(2));

        return {
          id: def.id,
          name: def.name,
          description: def.description,
          productIds: def.productIds,
          products: bundleProducts,
          originalPrice,
          discountedPrice,
          discountPercentage: def.discountPercentage,
          image: def.image
        };
      });

      setBundles(createdBundles);
      setLoading(false);
    }
  }, [products]);

  // Get a bundle by ID
  const getBundleById = (id: string): Bundle | undefined => {
    return bundles.find(bundle => bundle.id === id);
  };

  return (
    <BundleContext.Provider
      value={{
        bundles,
        loading,
        getBundleById
      }}
    >
      {children}
    </BundleContext.Provider>
  );
};

export const useBundles = () => {
  const context = useContext(BundleContext);
  if (context === undefined) {
    throw new Error('useBundles must be used within a BundleProvider');
  }
  return context;
};

export default BundleContext;
