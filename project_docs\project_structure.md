# 🏗️ Sabone.store Project Structure

## 📂 Overview

Sabone.store is a luxury e-commerce platform for natural soaps and shampoos with Arabic roots. The project is built using a modern frontend stack with React, Vite, and Tailwind CSS.

## 🧩 Technology Stack

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with custom theme
- **UI Components**: shadcn/ui (Radix UI-based components)
- **Routing**: React Router
- **State Management**: React Context API
- **Authentication**: Auth0
- **Form Handling**: React Hook Form with Zod validation
- **Data Fetching**: TanStack Query (React Query)
- **Notifications**: Sonner toast notifications

## 📁 Directory Structure

```
src/
├── components/         # UI components
│   ├── ui/             # Base UI components (shadcn/ui)
│   ├── ProductCard/    # Product card components
│   ├── checkout/       # Checkout-related components
│   ├── product/        # Product-related components
│   ├── review/         # Review-related components
│   ├── auth/           # Authentication components
│   └── ...
├── contexts/           # React Context providers
├── data/               # Static data (products, etc.)
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
├── pages/              # Page components
│   ├── admin/          # Admin dashboard pages
│   └── ...
├── services/           # Service functions (API calls, etc.)
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## 🌐 Page Routes

| Route | Component | Description |
|-------|-----------|-------------|
| `/` | `Index` | Homepage with product listings |
| `/product/:id` | `ProductDetailPage` | Product detail page |
| `/checkout` | `Checkout` | Checkout process |
| `/account/*` | `Account` | User account pages |
| `/admin/*` | Various admin components | Admin dashboard |

## 🧠 Context Providers

The application uses several context providers to manage state:

| Provider | Purpose |
|----------|---------|
| `AuthProvider` | User authentication state |
| `ProductProvider` | Product data and operations |
| `BundleProvider` | Product bundles and offers |
| `CartProvider` | Shopping cart state |
| `EmailProvider` | Email notifications |
| `InventoryProvider` | Product inventory management |
| `OrderProvider` | Order processing and management |

## 🔄 Data Flow

```
User Interaction → Context Actions → Service Functions → Local Storage/API → UI Update
```

### Example: Adding a Product to Cart

1. User clicks "Add to Cart" button
2. `addItem` function from `CartContext` is called
3. Function checks inventory availability via `inventoryService`
4. If available, item is added to cart state and persisted to localStorage
5. UI is updated to reflect the change (cart count, toast notification)

## 🎨 UI Components

### Core Components

- `Navbar`: Site navigation and user actions
- `Hero`: Homepage hero section with animations
- `ProductGrid`: Display of product listings
- `ProductCard`: Individual product display
- `Footer`: Site footer with links and information
- `BundlesSection`: Product bundles and offers

### UI Components (shadcn/ui)

- `Button`: Various button styles
- `Dialog`: Modal dialogs
- `Sheet`: Slide-out panels
- `Form` components: Form inputs and validation
- `Dropdown`: Dropdown menus
- `Toast`: Notifications

## 🔐 Authentication

Authentication is implemented using Auth0 with a development mode fallback:

- Production: Auth0 authentication
- Development: Mock authentication with `VITE_SKIP_AUTH=true`

## 🛒 Shopping Cart

The cart implementation includes:

- Add/remove/update items
- Quantity validation against inventory
- Persistent storage in localStorage
- Price calculations (subtotal, shipping, total)

## 📦 Product Data Structure

```typescript
interface Product {
  id: string;
  name: string;
  description: string;
  fullDescription?: string;
  price: number;
  image: string;
  type: 'bar' | 'liquid';
  ingredients: string[];
  benefits: string[];
  skinType?: string;
  application?: string;
  storage?: string;
}
```

## 🔍 SEO Implementation

- React Helmet for meta tags
- Schema.org structured data for products
- Semantic HTML structure
- Optimized page titles and descriptions

## 📱 Responsive Design

The site is fully responsive with:

- Mobile-first approach
- Tailored layouts for different screen sizes
- Touch-friendly interactions
- Optimized images and assets

## 🚀 Performance Optimizations

- Code splitting with lazy loading
- Image optimization
- Efficient state management
- Memoization of expensive calculations
