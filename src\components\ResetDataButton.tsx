import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useProducts } from "@/contexts/ProductContext";
import { toast } from "sonner";
import { RefreshCw } from "lucide-react";

interface ResetDataButtonProps {
  className?: string;
}

const ResetDataButton = ({ className = "" }: ResetDataButtonProps) => {
  const { resetProducts } = useProducts();
  const [isResetting, setIsResetting] = useState(false);

  const handleReset = async () => {
    setIsResetting(true);
    try {
      await resetProducts();
      toast.success("Products data has been reset. Refresh the page to see changes.");
    } catch (error) {
      console.error("Error resetting data:", error);
      toast.error("Failed to reset data. Please try again.");
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className={`bg-sabone-charcoal text-sabone-gold border-sabone-gold/30 hover:bg-sabone-dark-olive/80 hover:text-sabone-gold-accent ${className}`}
      onClick={handleReset}
      disabled={isResetting}
    >
      <RefreshCw className={`mr-2 h-4 w-4 ${isResetting ? "animate-spin" : ""}`} />
      Reset Products Data
    </Button>
  );
};

export default ResetDataButton;
