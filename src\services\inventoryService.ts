import { products } from "@/data/products";
import {
  InventoryItem,
  InventoryUpdate,
  InventoryCheck
} from "@/types/inventory";
import { CartItem } from "@/contexts/CartContext";

// In a real application, this would be an API call to a backend service
// For now, we'll use localStorage to simulate inventory persistence

const INVENTORY_STORAGE_KEY = 'sabone-inventory';
const INVENTORY_HISTORY_KEY = 'sabone-inventory-history';

// Initialize inventory with default values if not already in localStorage
const initializeInventory = (): InventoryItem[] => {
  try {
    const inventoryJson = localStorage.getItem(INVENTORY_STORAGE_KEY);
    if (inventoryJson) {
      return JSON.parse(inventoryJson);
    }
    
    // Create default inventory for all products
    const defaultInventory: InventoryItem[] = products.map(product => ({
      productId: product.id,
      stockQuantity: 50, // Default stock quantity
      lowStockThreshold: 10, // Default low stock threshold
      isInStock: true,
      lastUpdated: new Date().toISOString()
    }));
    
    localStorage.setItem(INVENTORY_STORAGE_KEY, JSON.stringify(defaultInventory));
    return defaultInventory;
  } catch (error) {
    console.error('Error initializing inventory:', error);
    return [];
  }
};

// Get all inventory items
export const getInventory = (): InventoryItem[] => {
  return initializeInventory();
};

// Get inventory for a specific product
export const getProductInventory = (productId: string): InventoryItem | null => {
  const inventory = getInventory();
  return inventory.find(item => item.productId === productId) || null;
};

// Update inventory quantity
export const updateInventory = (update: InventoryUpdate): InventoryItem | null => {
  try {
    const inventory = getInventory();
    const itemIndex = inventory.findIndex(item => item.productId === update.productId);
    
    if (itemIndex === -1) return null;
    
    // Update the inventory item
    const updatedItem = { 
      ...inventory[itemIndex],
      stockQuantity: Math.max(0, inventory[itemIndex].stockQuantity + update.quantityChange),
      lastUpdated: new Date().toISOString()
    };
    
    // Update isInStock status
    updatedItem.isInStock = updatedItem.stockQuantity > 0;
    
    // Update the inventory
    inventory[itemIndex] = updatedItem;
    localStorage.setItem(INVENTORY_STORAGE_KEY, JSON.stringify(inventory));
    
    // Record the update in history
    recordInventoryUpdate(update);
    
    return updatedItem;
  } catch (error) {
    console.error('Error updating inventory:', error);
    return null;
  }
};

// Record inventory update in history
const recordInventoryUpdate = (update: InventoryUpdate): void => {
  try {
    const historyJson = localStorage.getItem(INVENTORY_HISTORY_KEY);
    const history = historyJson ? JSON.parse(historyJson) : [];
    
    history.push({
      ...update,
      timestamp: new Date().toISOString()
    });
    
    localStorage.setItem(INVENTORY_HISTORY_KEY, JSON.stringify(history));
  } catch (error) {
    console.error('Error recording inventory update:', error);
  }
};

// Check if items are in stock
export const checkInventory = (items: CartItem[]): InventoryCheck[] => {
  const inventory = getInventory();
  
  return items.map(item => {
    const inventoryItem = inventory.find(invItem => invItem.productId === item.product.id);
    
    if (!inventoryItem) {
      return {
        productId: item.product.id,
        requestedQuantity: item.quantity,
        available: false,
        availableQuantity: 0
      };
    }
    
    return {
      productId: item.product.id,
      requestedQuantity: item.quantity,
      available: inventoryItem.isInStock && inventoryItem.stockQuantity >= item.quantity,
      availableQuantity: inventoryItem.stockQuantity
    };
  });
};

// Update inventory after order placement
export const updateInventoryAfterOrder = (items: CartItem[]): boolean => {
  try {
    items.forEach(item => {
      updateInventory({
        productId: item.product.id,
        quantityChange: -item.quantity,
        reason: 'purchase'
      });
    });
    
    return true;
  } catch (error) {
    console.error('Error updating inventory after order:', error);
    return false;
  }
};

// Restock inventory
export const restockInventory = (productId: string, quantity: number): InventoryItem | null => {
  return updateInventory({
    productId,
    quantityChange: quantity,
    reason: 'restock'
  });
};

// Get low stock items
export const getLowStockItems = (): InventoryItem[] => {
  const inventory = getInventory();
  return inventory.filter(item => 
    item.isInStock && item.stockQuantity <= item.lowStockThreshold
  );
};
