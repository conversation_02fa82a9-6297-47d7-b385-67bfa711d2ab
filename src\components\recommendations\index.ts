// Recommendation Components
export { default as RecommendationSection } from './RecommendationSection';
export { default as CustomersAlsoBought } from './CustomersAlsoBought';
export { default as RecommendedForYou } from './RecommendedForYou';
export { default as RecentlyViewed } from './RecentlyViewed';
export { default as FrequentlyBoughtTogether } from './FrequentlyBoughtTogether';
export { default as TrendingProducts } from './TrendingProducts';

// Re-export types from utils for convenience
export type {
  UserBehavior,
  ProductView,
  CartAddition,
  Purchase,
  RecommendationScore,
  RecommendationResult
} from '@/utils/recommendationUtils';
