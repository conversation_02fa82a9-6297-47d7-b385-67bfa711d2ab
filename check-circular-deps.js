// Simple script to check for potential circular dependencies
// Run with: node check-circular-deps.js

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file and directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const srcDir = path.join(__dirname, 'src');
const fileExtensions = ['.js', '.jsx', '.ts', '.tsx'];
const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+[^,]*|[^,{]*)\s+from\s+['"]([^'"]+)['"]/g;

// Store dependencies
const dependencies = new Map();
const circularDeps = [];

// Helper function to normalize import path
function normalizeImportPath(importPath, currentFile) {
  if (importPath.startsWith('.')) {
    // Relative import
    const currentDir = path.dirname(currentFile);
    const absolutePath = path.resolve(currentDir, importPath);

    // Try to resolve the actual file
    for (const ext of fileExtensions) {
      const withExt = absolutePath + ext;
      if (fs.existsSync(withExt)) {
        return withExt;
      }

      // Check for index files
      const indexFile = path.join(absolutePath, `index${ext}`);
      if (fs.existsSync(indexFile)) {
        return indexFile;
      }
    }

    // If we can't resolve to a specific file, return the directory
    return absolutePath;
  } else if (importPath.startsWith('@/')) {
    // Alias import (assuming @/ maps to src/)
    const aliasPath = importPath.replace('@/', '');
    return path.join(srcDir, aliasPath);
  }

  // External dependency
  return importPath;
}

// Function to extract imports from a file
function extractImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      const normalizedPath = normalizeImportPath(importPath, filePath);
      imports.push(normalizedPath);
    }

    return imports;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

// Function to check for circular dependencies
function detectCircularDependencies(startFile, currentFile, visited = new Set(), path = []) {
  if (visited.has(currentFile)) {
    if (currentFile === startFile) {
      // Found a circular dependency
      circularDeps.push([...path, currentFile]);
    }
    return;
  }

  visited.add(currentFile);
  path.push(currentFile);

  const deps = dependencies.get(currentFile) || [];
  for (const dep of deps) {
    detectCircularDependencies(startFile, dep, new Set(visited), [...path]);
  }
}

// Function to scan directory recursively
function scanDirectory(dir) {
  const files = fs.readdirSync(dir, { withFileTypes: true });

  for (const file of files) {
    const fullPath = path.join(dir, file.name);

    if (file.isDirectory()) {
      // Skip node_modules and other non-source directories
      if (file.name !== 'node_modules' && !file.name.startsWith('.')) {
        scanDirectory(fullPath);
      }
    } else if (fileExtensions.some(ext => file.name.endsWith(ext))) {
      // Process source files
      const imports = extractImports(fullPath);
      dependencies.set(fullPath, imports);
    }
  }
}

// Main function
function main() {
  console.log('Scanning for circular dependencies...');

  // Scan the source directory
  scanDirectory(srcDir);

  console.log(`Found ${dependencies.size} files with imports.`);

  // Check each file for circular dependencies
  for (const [file] of dependencies) {
    detectCircularDependencies(file, file);
  }

  // Print results
  if (circularDeps.length > 0) {
    console.log(`\nFound ${circularDeps.length} circular dependencies:`);

    circularDeps.forEach((cycle, index) => {
      console.log(`\nCircular Dependency #${index + 1}:`);
      cycle.forEach((file, i) => {
        const relativePath = path.relative(__dirname, file);
        console.log(`${i + 1}. ${relativePath}`);
      });
    });

    console.log('\nCircular dependencies can cause issues with React rendering and state management.');
    console.log('Consider refactoring these dependencies to break the cycles.');
  } else {
    console.log('\nNo circular dependencies found!');
  }
}

// Run the script
main();
