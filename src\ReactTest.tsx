import React, { useState } from 'react';

// Simple React component to test if <PERSON><PERSON> is working
const ReactTest: React.FC = () => {
  const [count, setCount] = useState(0);

  const incrementCount = () => {
    setCount(count + 1);
  };

  return (
    <div style={{
      backgroundColor: '#2a2a1f',
      color: '#e5dcc5',
      padding: '20px',
      borderRadius: '8px',
      border: '1px solid #c6a870',
      maxWidth: '600px',
      margin: '50px auto',
      textAlign: 'center'
    }}>
      <h1 style={{ color: '#c6a870' }}>React Test Component</h1>
      <p>If you can see this, <PERSON><PERSON> is working correctly!</p>
      <div style={{ marginTop: '20px' }}>
        <p>Count: {count}</p>
        <button
          onClick={incrementCount}
          style={{
            backgroundColor: '#c6a870',
            color: '#1c1c1c',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '10px'
          }}
        >
          Increment
        </button>
      </div>
    </div>
  );
};

export default ReactTest;
