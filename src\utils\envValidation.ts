import { z } from 'zod';

/**
 * Environment variables validation schema
 */
const envSchema = z.object({
  // App environment
  VITE_NODE_ENV: z.enum(['development', 'production', 'test']).optional().default('development'),
  
  // Auth0 configuration
  VITE_AUTH0_DOMAIN: z.string().min(1, 'Auth0 domain is required'),
  VITE_AUTH0_CLIENT_ID: z.string().min(1, 'Auth0 client ID is required'),
  VITE_SKIP_AUTH: z.enum(['true', 'false']).optional().default('false'),
  
  // Stripe configuration
  VITE_STRIPE_PUBLISHABLE_KEY: z.string().min(1, 'Stripe publishable key is required'),
  VITE_STRIPE_SECRET_KEY: z.string().min(1, 'Stripe secret key is required'),
  VITE_STRIPE_WEBHOOK_SECRET: z.string().min(1, 'Stripe webhook secret is required'),
  
  // PayPal configuration
  VITE_PAYPAL_CLIENT_ID: z.string().min(1, 'PayPal client ID is required'),
  VITE_PAYPAL_CLIENT_SECRET: z.string().min(1, 'PayPal client secret is required'),
  VITE_PAYPAL_SANDBOX: z.enum(['true', 'false']).optional().default('true'),
  
  // SendGrid configuration
  VITE_SENDGRID_API_KEY: z.string().min(1, 'SendGrid API key is required'),
  VITE_SENDER_EMAIL: z.string().email('Invalid sender email').optional(),
  
  // Server configuration
  PORT: z.string().transform(val => parseInt(val, 10)).pipe(
    z.number().positive('Port must be a positive number')
  ).optional().default('3000'),
  
  // API endpoints
  VITE_API_URL: z.string().url('Invalid API URL').optional(),
});

/**
 * Type definition for validated environment variables
 */
export type EnvVars = z.infer<typeof envSchema>;

/**
 * Validates environment variables and returns a validated object
 * @returns Validated environment variables
 */
export const validateEnv = (): EnvVars => {
  // Collect all environment variables
  const envVars = {
    VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV,
    VITE_AUTH0_DOMAIN: import.meta.env.VITE_AUTH0_DOMAIN,
    VITE_AUTH0_CLIENT_ID: import.meta.env.VITE_AUTH0_CLIENT_ID,
    VITE_SKIP_AUTH: import.meta.env.VITE_SKIP_AUTH,
    VITE_STRIPE_PUBLISHABLE_KEY: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
    VITE_STRIPE_SECRET_KEY: import.meta.env.VITE_STRIPE_SECRET_KEY,
    VITE_STRIPE_WEBHOOK_SECRET: import.meta.env.VITE_STRIPE_WEBHOOK_SECRET,
    VITE_PAYPAL_CLIENT_ID: import.meta.env.VITE_PAYPAL_CLIENT_ID,
    VITE_PAYPAL_CLIENT_SECRET: import.meta.env.VITE_PAYPAL_CLIENT_SECRET,
    VITE_PAYPAL_SANDBOX: import.meta.env.VITE_PAYPAL_SANDBOX,
    VITE_SENDGRID_API_KEY: import.meta.env.VITE_SENDGRID_API_KEY,
    VITE_SENDER_EMAIL: import.meta.env.VITE_SENDER_EMAIL,
    PORT: process.env.PORT,
    VITE_API_URL: import.meta.env.VITE_API_URL,
  };

  // In development mode, use fallbacks for some variables
  if (import.meta.env.DEV) {
    return developmentFallbacks(envVars);
  }

  try {
    // Parse and validate environment variables
    return envSchema.parse(envVars);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => {
        return `- ${err.path.join('.')}: ${err.message}`;
      }).join('\n');
      
      console.error(`❌ Environment validation failed:\n${missingVars}`);
      
      // In development, provide more helpful error message
      if (import.meta.env.DEV) {
        console.info('ℹ️ Create a .env file with the required variables or check existing .env file.');
      }
    } else {
      console.error('❌ Unknown error validating environment variables:', error);
    }
    
    // Rethrow for proper error handling
    throw error;
  }
};

/**
 * Provides development fallbacks for certain environment variables
 * @param envVars - The current environment variables
 * @returns Environment variables with development fallbacks
 */
const developmentFallbacks = (envVars: Partial<EnvVars>): EnvVars => {
  // Create a copy to avoid mutating the original
  const vars = { ...envVars };
  
  // Development-only fallbacks
  if (!vars.VITE_AUTH0_DOMAIN) {
    console.warn('⚠️ Using placeholder Auth0 domain for development');
    vars.VITE_AUTH0_DOMAIN = 'dev-placeholder.auth0.com';
  }
  
  if (!vars.VITE_AUTH0_CLIENT_ID) {
    console.warn('⚠️ Using placeholder Auth0 client ID for development');
    vars.VITE_AUTH0_CLIENT_ID = 'placeholder123456';
  }
  
  if (!vars.VITE_STRIPE_PUBLISHABLE_KEY) {
    console.warn('⚠️ Using placeholder Stripe publishable key for development');
    vars.VITE_STRIPE_PUBLISHABLE_KEY = 'pk_test_placeholder';
  }
  
  if (!vars.VITE_STRIPE_SECRET_KEY) {
    console.warn('⚠️ Using placeholder Stripe secret key for development');
    vars.VITE_STRIPE_SECRET_KEY = 'sk_test_placeholder';
  }
  
  if (!vars.VITE_STRIPE_WEBHOOK_SECRET) {
    console.warn('⚠️ Using placeholder Stripe webhook secret for development');
    vars.VITE_STRIPE_WEBHOOK_SECRET = 'whsec_placeholder';
  }
  
  if (!vars.VITE_PAYPAL_CLIENT_ID) {
    console.warn('⚠️ Using placeholder PayPal client ID for development');
    vars.VITE_PAYPAL_CLIENT_ID = 'paypal_client_placeholder';
  }
  
  if (!vars.VITE_PAYPAL_CLIENT_SECRET) {
    console.warn('⚠️ Using placeholder PayPal client secret for development');
    vars.VITE_PAYPAL_CLIENT_SECRET = 'paypal_secret_placeholder';
  }
  
  if (!vars.VITE_SENDGRID_API_KEY) {
    console.warn('⚠️ Using placeholder SendGrid API key for development');
    vars.VITE_SENDGRID_API_KEY = 'SG.placeholder';
  }

  // Parse with fallbacks
  try {
    return envSchema.parse(vars);
  } catch (error) {
    // Still log the error but use partial data
    console.error('❌ Environment validation failed even with fallbacks:', error);
    return vars as EnvVars;
  }
};

/**
 * Singleton instance of validated environment variables
 */
let validatedEnv: EnvVars | null = null;

/**
 * Gets validated environment variables
 * @returns Validated environment variables
 */
export const getEnv = (): EnvVars => {
  if (!validatedEnv) {
    validatedEnv = validateEnv();
  }
  return validatedEnv;
}; 