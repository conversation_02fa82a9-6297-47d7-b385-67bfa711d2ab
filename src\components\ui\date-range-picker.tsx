import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { addDays, format } from "date-fns";
import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DateRangePickerProps {
  className?: string;
  value?: DateRange;
  onChange?: (date: DateRange | undefined) => void;
}

export function DateRangePicker({
  className,
  value,
  onChange,
}: DateRangePickerProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(value);

  React.useEffect(() => {
    if (value) {
      setDate(value);
    }
  }, [value]);

  const handleDateChange = (newDate: DateRange | undefined) => {
    setDate(newDate);
    if (onChange) {
      onChange(newDate);
    }
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 bg-sabone-dark-olive border-sabone-gold/30" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleDateChange}
            numberOfMonths={2}
            className="text-sabone-cream"
          />
          <div className="p-3 border-t border-sabone-gold/20 flex justify-between">
            <Button
              variant="outline"
              className="text-xs border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
              onClick={() => handleDateChange(undefined)}
            >
              Clear
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="text-xs border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
                onClick={() => {
                  const today = new Date();
                  handleDateChange({
                    from: today,
                    to: today,
                  });
                }}
              >
                Today
              </Button>
              <Button
                variant="outline"
                className="text-xs border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
                onClick={() => {
                  const today = new Date();
                  const lastWeek = addDays(today, -7);
                  handleDateChange({
                    from: lastWeek,
                    to: today,
                  });
                }}
              >
                Last 7 Days
              </Button>
              <Button
                variant="outline"
                className="text-xs border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
                onClick={() => {
                  const today = new Date();
                  const lastMonth = addDays(today, -30);
                  handleDateChange({
                    from: lastMonth,
                    to: today,
                  });
                }}
              >
                Last 30 Days
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
