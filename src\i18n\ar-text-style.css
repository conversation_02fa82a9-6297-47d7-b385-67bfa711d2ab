/**
 * Arabic text styling - without changing layout direction
 * This ensures Arabic text displays correctly while keeping the LTR layout
 */

/* Apply Arabic font for Arabic locale without RTL layout changes */
html[lang="ar"] {
  font-family: '<PERSON><PERSON><PERSON>', 'Playfair Display', serif;
}

html[lang="ar"] h1, 
html[lang="ar"] h2, 
html[lang="ar"] h3, 
html[lang="ar"] h4, 
html[lang="ar"] h5, 
html[lang="ar"] h6 {
  font-family: '<PERSON><PERSON>wal', 'Playfair Display', serif;
}

/* Text alignment for headers and important elements */
html[lang="ar"] .text-center {
  text-align: center !important;
}

/* Make sure product grid elements are centered */
html[lang="ar"] #products-section h2,
html[lang="ar"] #products-section p {
  text-align: center !important;
}

/* Ensure buttons and UI elements display correctly */
html[lang="ar"] button,
html[lang="ar"] .btn {
  font-family: '<PERSON><PERSON><PERSON>', 'Playfair Display', serif;
}

/* Fix navbar elements */
html[lang="ar"] .navbar-inner-container {
  margin-left: auto !important;
  margin-right: auto !important;
  justify-content: space-between !important;
} 