import { useState } from 'react';
import { useCart } from '@/contexts/CartContext';
import { Order } from '@/types/order';
import { formatDate } from '@/utils/formatDate';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  ShoppingCart,
  ExternalLink,
  Loader2
} from 'lucide-react';

interface OrderDetailProps {
  order: Order;
  onClose?: () => void;
}

const OrderDetail = ({ order, onClose }: OrderDetailProps) => {
  const { addItem } = useCart();
  const [isReordering, setIsReordering] = useState(false);
  const [isTrackingOrder, setIsTrackingOrder] = useState(false);

  // Generate a tracking number based on the order ID
  const trackingNumber = `TRK${order.id.substring(order.id.length - 8).toUpperCase()}`;

  // Calculate estimated delivery date (7 days from order date for shipped orders)
  const orderDate = new Date(order.createdAt);
  const estimatedDelivery = new Date(orderDate);
  estimatedDelivery.setDate(orderDate.getDate() + 7);

  // Get status icon based on order status
  const getStatusIcon = () => {
    switch (order.status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-amber-400" />;
      case 'processing':
        return <Package className="h-5 w-5 text-blue-400" />;
      case 'shipped':
        return <Truck className="h-5 w-5 text-green-400" />;
      case 'delivered':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-amber-400" />;
    }
  };

  // Get status color based on order status
  const getStatusColor = () => {
    switch (order.status) {
      case 'pending':
        return 'bg-amber-500/10 text-amber-500 border-amber-500/20';
      case 'processing':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'shipped':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'delivered':
        return 'bg-green-600/10 text-green-600 border-green-600/20';
      case 'cancelled':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      default:
        return 'bg-amber-500/10 text-amber-500 border-amber-500/20';
    }
  };

  // Handle reordering
  const handleReorder = async () => {
    setIsReordering(true);

    try {
      // Add each item from the order to the cart
      for (const item of order.items) {
        // In a real app, you would fetch the current product data
        // For now, we'll use the data from the order
        const product = {
          id: item.productId,
          name: item.name,
          price: item.price,
          image: item.image,
          type: 'soap', // Default type
          description: '', // Default description
          ingredients: [], // Default ingredients
          benefits: [], // Default benefits
        };

        // Add to cart with a small delay to prevent race conditions
        await new Promise(resolve => setTimeout(resolve, 100));
        addItem(product, item.quantity);
      }

      toast.success('Items added to cart');

      // Close the dialog if a callback is provided
      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error reordering:', error);
      toast.error('Failed to add items to cart');
    } finally {
      setIsReordering(false);
    }
  };

  // Handle tracking
  const handleTrackOrder = () => {
    setIsTrackingOrder(true);

    // Simulate tracking lookup
    setTimeout(() => {
      setIsTrackingOrder(false);

      // In a real app, this would open a tracking page or modal
      // For now, we'll just show a toast
      toast.success(`Tracking information for ${trackingNumber} opened`);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-medium text-sabone-gold">Order #{order.id}</h3>
          <p className="text-sabone-cream/70 text-sm">Placed on {formatDate(order.createdAt)}</p>
        </div>
        <Badge className={getStatusColor()}>
          <span className="flex items-center">
            {getStatusIcon()}
            <span className="ml-1 capitalize">{order.status}</span>
          </span>
        </Badge>
      </div>

      <Separator className="bg-sabone-gold/20" />

      {/* Order tracking section - only show for shipped orders */}
      {order.status === 'shipped' && (
        <div className="bg-sabone-charcoal/30 p-4 rounded-md border border-sabone-gold/10">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-sabone-gold font-medium">Tracking Information</h4>
              <p className="text-sabone-cream/70 text-sm">Tracking Number: {trackingNumber}</p>
              <p className="text-sabone-cream/70 text-sm">
                Estimated Delivery: {formatDate(estimatedDelivery)}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              onClick={handleTrackOrder}
              disabled={isTrackingOrder}
            >
              {isTrackingOrder ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <ExternalLink className="h-4 w-4 mr-2" />
              )}
              Track Order
            </Button>
          </div>
        </div>
      )}

      {/* Order items */}
      <div>
        <h4 className="text-sabone-gold font-medium mb-3">Items</h4>
        <div className="space-y-4">
          {order.items.map((item) => (
            <div key={item.productId} className="flex items-center space-x-4">
              <div className="h-16 w-16 rounded-md overflow-hidden bg-sabone-charcoal/50 flex-shrink-0">
                <img
                  src={item.image}
                  alt={item.name}
                  className="h-full w-full object-cover"
                />
              </div>
              <div className="flex-1">
                <p className="text-sabone-cream font-medium">{item.name}</p>
                <p className="text-sabone-cream/70 text-sm">
                  Quantity: {item.quantity} × ${item.price.toFixed(2)}
                </p>
              </div>
              <div className="text-sabone-gold font-medium">
                ${(item.price * item.quantity).toFixed(2)}
              </div>
            </div>
          ))}
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      {/* Order summary */}
      <div className="space-y-2">
        <div className="flex justify-between">
          <p className="text-sabone-cream/70">Subtotal</p>
          <p className="text-sabone-cream">${order.subtotal.toFixed(2)}</p>
        </div>
        <div className="flex justify-between">
          <p className="text-sabone-cream/70">Shipping</p>
          <p className="text-sabone-cream">${order.shipping.toFixed(2)}</p>
        </div>
        <div className="flex justify-between">
          <p className="text-sabone-cream/70">Tax</p>
          <p className="text-sabone-cream">${order.tax.toFixed(2)}</p>
        </div>
        <div className="flex justify-between font-medium">
          <p className="text-sabone-gold">Total</p>
          <p className="text-sabone-gold">${order.total.toFixed(2)}</p>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      {/* Shipping address */}
      <div>
        <h4 className="text-sabone-gold font-medium mb-2">Shipping Address</h4>
        <div className="text-sabone-cream/90">
          <p>{order.shippingAddress.fullName}</p>
          <p>{order.shippingAddress.addressLine1}</p>
          {order.shippingAddress.addressLine2 && <p>{order.shippingAddress.addressLine2}</p>}
          <p>
            {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
          </p>
          <p>{order.shippingAddress.country}</p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          onClick={handleReorder}
          disabled={isReordering}
        >
          {isReordering ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <ShoppingCart className="h-4 w-4 mr-2" />
          )}
          Reorder
        </Button>
      </div>
    </div>
  );
};

export default OrderDetail;
