# 🚀 Sabone.store Deployment Guide

## 📋 Overview

This document outlines the deployment strategy and configuration for the Sabone.store e-commerce platform. It covers hosting options, build processes, environment configuration, and CI/CD setup.

## 🏗️ Hosting Options

### Recommended: Vercel

Vercel is the recommended hosting platform for the Sabone.store frontend due to its excellent support for Vite applications and seamless integration with GitHub.

**Benefits:**
- Optimized for React applications
- Automatic HTTPS
- Global CDN
- Preview deployments for PRs
- Easy environment variable management
- Serverless functions support

**Alternative: Railway**

Railway is a viable alternative that offers similar benefits with potentially different pricing structures.

**Benefits:**
- Simple deployment process
- Built-in databases
- Automatic scaling
- Preview environments
- GitHub integration

## 🔧 Build Configuration

### Vite Build Setup

The project uses Vite as the build tool. The build configuration is defined in `vite.config.ts`:

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
      },
    },
  },
});
```

### Build Scripts

The following npm scripts are available for building the application:

```json
"scripts": {
  "dev": "vite",
  "build": "vite build",
  "build:dev": "vite build --mode development",
  "preview": "vite preview"
}
```

- `npm run build`: Production build with optimizations
- `npm run build:dev`: Development build with source maps and without console removal

## 🌐 Environment Configuration

### Environment Variables

The application requires the following environment variables:

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `VITE_AUTH0_DOMAIN` | Auth0 domain | Yes | - |
| `VITE_AUTH0_CLIENT_ID` | Auth0 client ID | Yes | - |
| `VITE_SKIP_AUTH` | Skip authentication in development | No | `false` |
| `VITE_API_URL` | Backend API URL | Yes | - |
| `VITE_STRIPE_PUBLIC_KEY` | Stripe publishable key | For production | - |

### Environment Setup in Vercel

1. Navigate to your project in the Vercel dashboard
2. Go to "Settings" > "Environment Variables"
3. Add each required environment variable
4. Specify which environments (Production, Preview, Development) each variable applies to

Example:
```
VITE_AUTH0_DOMAIN=your-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-client-id
VITE_API_URL=https://api.sabone.store
VITE_STRIPE_PUBLIC_KEY=pk_live_...
```

## 🔄 CI/CD Pipeline

### GitHub Actions Setup

Create a GitHub Actions workflow file at `.github/workflows/deploy.yml`:

```yaml
name: Deploy

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
      - run: npm ci
      - run: npm run lint
      - run: npm test

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
      - run: npm ci
      - run: npm run build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build
          path: dist/

  deploy:
    needs: build
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/download-artifact@v3
        with:
          name: build
          path: dist/
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

### Vercel GitHub Integration

Alternatively, you can use Vercel's GitHub integration for a simpler setup:

1. Connect your GitHub repository to Vercel
2. Configure build settings:
   - Framework Preset: Vite
   - Build Command: `npm run build`
   - Output Directory: `dist`
3. Set up environment variables
4. Enable automatic deployments

## 📦 Deployment Process

### Production Deployment

1. Merge code into the `main` branch
2. GitHub Actions or Vercel will automatically:
   - Run tests
   - Build the application
   - Deploy to production

### Preview Deployments

For pull requests:
1. Create a pull request to the `main` branch
2. Vercel will automatically create a preview deployment
3. Review the changes in the preview environment
4. Merge the PR when ready

## 🔍 Post-Deployment Verification

### Automated Checks

- **Lighthouse CI**: Set up Lighthouse CI to verify performance, accessibility, and best practices
- **E2E Tests**: Run Cypress tests against the deployed environment

### Manual Verification

After each deployment, verify:

1. **Core Functionality**:
   - User authentication
   - Product browsing
   - Cart operations
   - Checkout process

2. **Performance**:
   - Page load times
   - Interaction responsiveness
   - Image loading

3. **Cross-Browser Testing**:
   - Chrome, Firefox, Safari, Edge
   - Mobile browsers

## 🔒 SSL Configuration

Vercel and Railway both provide automatic SSL certificate management. No additional configuration is required for HTTPS.

## 🌐 Domain Configuration

### Custom Domain Setup in Vercel

1. Go to your project in the Vercel dashboard
2. Navigate to "Settings" > "Domains"
3. Add your custom domain (e.g., `sabone.store`)
4. Follow the instructions to configure DNS settings

### DNS Configuration

Configure the following DNS records with your domain registrar:

| Type | Name | Value | TTL |
|------|------|-------|-----|
| A | @ | *********** | 3600 |
| CNAME | www | cname.vercel-dns.com. | 3600 |

## 🔄 Rollback Strategy

### Vercel Rollback

If issues are detected after deployment:

1. Go to the "Deployments" tab in Vercel
2. Find the last stable deployment
3. Click the three dots menu and select "Promote to Production"

### Manual Rollback

If Vercel rollback is not available:

1. Revert the problematic commit in GitHub
2. Push the revert commit
3. Let the CI/CD pipeline deploy the reverted code

## 📊 Monitoring and Analytics

### Performance Monitoring

- **Vercel Analytics**: Enable Vercel Analytics for performance monitoring
- **Google Analytics**: Set up GA4 for user behavior tracking
- **Core Web Vitals**: Monitor CWV through Google Search Console

### Error Tracking

- **Sentry**: Implement Sentry for error tracking and monitoring
- **Vercel Error Tracking**: Enable error tracking in Vercel

## 🚀 Scaling Considerations

### CDN Configuration

Vercel automatically provides CDN capabilities. For additional optimization:

1. Configure caching headers for static assets
2. Use the `Cache-Control` header to specify caching behavior

### Performance Optimization

1. **Image Optimization**:
   - Use responsive images
   - Implement lazy loading
   - Consider using Vercel Image Optimization

2. **Code Splitting**:
   - Ensure proper code splitting is configured
   - Lazy load non-critical components

3. **Asset Optimization**:
   - Minify CSS and JavaScript
   - Optimize images
   - Use modern image formats (WebP, AVIF)

## 🔄 Continuous Optimization

### Regular Audits

Schedule regular performance and security audits:

1. Monthly Lighthouse audits
2. Quarterly security reviews
3. Regular dependency updates

### Feedback Loop

Implement a system to collect and act on performance feedback:

1. Monitor real user metrics (RUM)
2. Collect user feedback on performance
3. Prioritize optimizations based on data
