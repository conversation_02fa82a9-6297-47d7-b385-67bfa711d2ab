import { Helmet, HelmetProvider } from 'react-helmet-async';
import AdminLayout from '@/components/admin/AdminLayout';
import ReviewManagement from '@/components/admin/ReviewManagement';

const AdminReviews = () => {
  return (
    <HelmetProvider>
      <Helmet>
        <title>Review Management | Sabone Admin</title>
        <meta name="description" content="Manage customer reviews for Sabone products." />
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>
      
      <AdminLayout title="Review Management">
        <ReviewManagement />
      </AdminLayout>
    </HelmetProvider>
  );
};

export default AdminReviews;
