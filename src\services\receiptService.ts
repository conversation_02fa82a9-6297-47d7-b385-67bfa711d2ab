import { Order } from '@/types/order';
import { formatDate } from '@/utils/formatDate';
import { toast } from 'sonner';

// In a real application, this would use a PDF generation library like jsPDF
// or make an API call to a backend service that generates PDFs

// Function to generate a PDF receipt for an order
export const generateReceipt = async (order: Order): Promise<{ url: string } | null> => {
  try {
    // In a real application, this would generate a PDF and return a URL to download it
    // For demo purposes, we'll simulate a successful response with a fake URL
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Generate a fake receipt URL
    const receiptUrl = `/receipts/order-${order.id}.pdf`;
    
    return { url: receiptUrl };
  } catch (error) {
    console.error('Error generating receipt:', error);
    toast.error('Failed to generate receipt');
    return null;
  }
};

// Function to download a receipt
export const downloadReceipt = async (order: Order): Promise<boolean> => {
  try {
    const receipt = await generateReceipt(order);
    
    if (!receipt) {
      throw new Error('Failed to generate receipt');
    }
    
    // In a real application, this would trigger a download of the PDF
    // For demo purposes, we'll just show a success message
    toast.success('Receipt downloaded successfully');
    
    return true;
  } catch (error) {
    console.error('Error downloading receipt:', error);
    toast.error('Failed to download receipt');
    return false;
  }
};

// Function to email a receipt to a customer
export const emailReceipt = async (order: Order, email: string): Promise<boolean> => {
  try {
    const receipt = await generateReceipt(order);
    
    if (!receipt) {
      throw new Error('Failed to generate receipt');
    }
    
    // In a real application, this would send an email with the receipt attached
    // For demo purposes, we'll just show a success message
    toast.success(`Receipt sent to ${email}`);
    
    return true;
  } catch (error) {
    console.error('Error emailing receipt:', error);
    toast.error('Failed to email receipt');
    return false;
  }
};

// Function to generate receipt HTML content
// This could be used for displaying a receipt in the UI or for generating a PDF
export const generateReceiptHTML = (order: Order): string => {
  const { id, items, shippingAddress, billingAddress, subtotal, shipping, tax, total, createdAt, paymentMethod } = order;
  
  // Format the order date
  const orderDate = formatDate(createdAt);
  
  // Generate HTML for the order items
  const itemsHTML = items.map(item => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #ddd;">${item.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${item.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">$${item.price.toFixed(2)}</td>
      <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">$${(item.price * item.quantity).toFixed(2)}</td>
    </tr>
  `).join('');
  
  // Generate the full receipt HTML
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Receipt - Order #${id}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo {
          max-width: 150px;
          margin-bottom: 10px;
        }
        .receipt-title {
          font-size: 24px;
          margin-bottom: 5px;
        }
        .receipt-subtitle {
          color: #777;
          margin-bottom: 20px;
        }
        .info-section {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .info-box {
          width: 30%;
        }
        .info-box h3 {
          margin-top: 0;
          border-bottom: 1px solid #ddd;
          padding-bottom: 5px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        th {
          background-color: #f2f2f2;
          padding: 10px 8px;
          text-align: left;
        }
        .totals {
          width: 300px;
          margin-left: auto;
        }
        .totals td {
          padding: 5px 0;
        }
        .totals .total-row {
          font-weight: bold;
          font-size: 18px;
          border-top: 2px solid #333;
        }
        .footer {
          margin-top: 50px;
          text-align: center;
          color: #777;
          font-size: 14px;
          border-top: 1px solid #ddd;
          padding-top: 20px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <img src="https://sabone.store/logo.png" alt="Sabone" class="logo">
        <h1 class="receipt-title">Receipt</h1>
        <p class="receipt-subtitle">Order #${id} - ${orderDate}</p>
      </div>
      
      <div class="info-section">
        <div class="info-box">
          <h3>Billing Information</h3>
          <p>
            ${billingAddress ? billingAddress.fullName : shippingAddress.fullName}<br>
            ${billingAddress ? billingAddress.addressLine1 : shippingAddress.addressLine1}<br>
            ${billingAddress && billingAddress.addressLine2 ? billingAddress.addressLine2 + '<br>' : ''}
            ${billingAddress ? billingAddress.city : shippingAddress.city}, 
            ${billingAddress ? billingAddress.state : shippingAddress.state} 
            ${billingAddress ? billingAddress.zipCode : shippingAddress.zipCode}<br>
            ${billingAddress ? billingAddress.country : shippingAddress.country}
          </p>
        </div>
        
        <div class="info-box">
          <h3>Shipping Information</h3>
          <p>
            ${shippingAddress.fullName}<br>
            ${shippingAddress.addressLine1}<br>
            ${shippingAddress.addressLine2 ? shippingAddress.addressLine2 + '<br>' : ''}
            ${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.zipCode}<br>
            ${shippingAddress.country}
          </p>
        </div>
        
        <div class="info-box">
          <h3>Order Details</h3>
          <p>
            <strong>Order Date:</strong> ${orderDate}<br>
            <strong>Order ID:</strong> ${id}<br>
            <strong>Payment Method:</strong> ${paymentMethod === 'credit_card' ? 'Credit Card' : 
                                              paymentMethod === 'paypal' ? 'PayPal' : 
                                              'Cash on Delivery'}
          </p>
        </div>
      </div>
      
      <table>
        <thead>
          <tr>
            <th>Product</th>
            <th style="text-align: center;">Quantity</th>
            <th style="text-align: right;">Price</th>
            <th style="text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHTML}
        </tbody>
      </table>
      
      <table class="totals">
        <tr>
          <td>Subtotal:</td>
          <td style="text-align: right;">$${subtotal.toFixed(2)}</td>
        </tr>
        <tr>
          <td>Shipping:</td>
          <td style="text-align: right;">$${shipping.toFixed(2)}</td>
        </tr>
        <tr>
          <td>Tax:</td>
          <td style="text-align: right;">$${tax.toFixed(2)}</td>
        </tr>
        <tr class="total-row">
          <td>Total:</td>
          <td style="text-align: right;">$${total.toFixed(2)}</td>
        </tr>
      </table>
      
      <div class="footer">
        <p>Thank you for your purchase!</p>
        <p>Sabone - Crafted in Berlin. Rooted in Aleppo.</p>
        <p>Questions? Contact <NAME_EMAIL></p>
      </div>
    </body>
    </html>
  `;
};
