import React, { Suspense, lazy, ComponentType } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface LazyLoadOptions {
  /**
   * Fallback component to show while loading
   */
  fallback?: React.ReactNode;
  
  /**
   * Whether to preload the component
   */
  preload?: boolean;
  
  /**
   * Minimum delay before showing the component (ms)
   * Prevents flickering for fast loads
   */
  minDelay?: number;
}

/**
 * Default loading fallback
 */
const DefaultFallback = () => (
  <div className="w-full h-full min-h-[200px] flex items-center justify-center">
    <div className="space-y-4 w-full max-w-md">
      <Skeleton className="h-8 w-3/4 mx-auto bg-sabone-gold/10" />
      <Skeleton className="h-32 w-full bg-sabone-gold/10" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full bg-sabone-gold/10" />
        <Skeleton className="h-4 w-5/6 bg-sabone-gold/10" />
        <Skeleton className="h-4 w-4/6 bg-sabone-gold/10" />
      </div>
    </div>
  </div>
);

/**
 * Creates a lazy-loaded component with a fallback
 * 
 * @param factory Function that imports the component
 * @param options Options for lazy loading
 * @returns Lazy-loaded component
 */
export function lazyLoad<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
) {
  const {
    fallback = <DefaultFallback />,
    preload = false,
    minDelay = 0
  } = options;

  // Create the lazy component
  const LazyComponent = lazy(() => {
    // Add minimum delay if specified
    if (minDelay > 0) {
      return Promise.all([
        factory(),
        new Promise(resolve => setTimeout(resolve, minDelay))
      ]).then(([moduleExports]) => moduleExports);
    }
    return factory();
  });

  // Preload the component if requested
  if (preload) {
    factory();
  }

  // Return a component that renders the lazy component with a suspense boundary
  return (props: React.ComponentProps<T>) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
}

/**
 * HOC that adds lazy loading to a component
 * 
 * @param Component Component to lazy load
 * @param options Options for lazy loading
 * @returns Lazy-loaded component
 */
export function withLazyLoading<T extends ComponentType<any>>(
  Component: T,
  options: LazyLoadOptions = {}
) {
  const {
    fallback = <DefaultFallback />,
    minDelay = 0
  } = options;

  return (props: React.ComponentProps<T>) => (
    <Suspense fallback={fallback}>
      {minDelay > 0 ? (
        <MinDelayComponent Component={Component} delay={minDelay} {...props} />
      ) : (
        <Component {...props} />
      )}
    </Suspense>
  );
}

/**
 * Component that adds a minimum delay before rendering
 */
function MinDelayComponent<T extends ComponentType<any>>({
  Component,
  delay,
  ...props
}: {
  Component: T;
  delay: number;
} & React.ComponentProps<T>) {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setReady(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  if (!ready) {
    return <DefaultFallback />;
  }

  return <Component {...props as any} />;
}
