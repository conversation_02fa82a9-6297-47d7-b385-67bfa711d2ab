import { Helmet } from 'react-helmet-async';
import { Product } from '@/data/products';

interface ProductSchemaProps {
  product: Product;
  reviews?: {
    ratingValue: number;
    reviewCount: number;
  };
}

/**
 * Component to add enhanced product structured data
 * 
 * @param product Product object
 * @param reviews Optional reviews data with rating value and count
 * @returns Helmet component with product schema
 */
const ProductSchema = ({ product, reviews }: ProductSchemaProps) => {
  // Create the product schema
  const productSchema = {
    '@context': 'https://schema.org/',
    '@type': 'Product',
    'name': product.name,
    'description': product.description,
    'image': product.image.startsWith('http') ? product.image : `https://sabone.store${product.image}`,
    'sku': product.id,
    'mpn': product.id,
    'brand': {
      '@type': 'Brand',
      'name': 'Sabone'
    },
    'offers': {
      '@type': 'Offer',
      'url': `https://sabone.store/product/${product.id}`,
      'priceCurrency': 'USD',
      'price': product.price.toFixed(2),
      'availability': 'https://schema.org/InStock',
      'priceValidUntil': new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0]
    }
  };

  // Add reviews if available
  if (reviews) {
    Object.assign(productSchema, {
      'aggregateRating': {
        '@type': 'AggregateRating',
        'ratingValue': reviews.ratingValue.toFixed(1),
        'reviewCount': reviews.reviewCount
      }
    });
  }

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(productSchema)}
      </script>
    </Helmet>
  );
};

export default ProductSchema;
