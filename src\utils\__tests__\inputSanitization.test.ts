import {
  sanitizeString,
  sanitizeObject,
  containsXssPatterns,
  containsSqlInjection,
  validateRateLimit,
  clearRateLimit,
} from '../inputSanitization';

describe('inputSanitization', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  describe('sanitizeString', () => {
    it('should return empty string for null/undefined input', () => {
      expect(sanitizeString(null)).toBe('');
      expect(sanitizeString(undefined)).toBe('');
      expect(sanitizeString('')).toBe('');
    });

    it('should trim whitespace by default', () => {
      expect(sanitizeString('  hello world  ')).toBe('hello world');
    });

    it('should enforce maximum length', () => {
      const longString = 'a'.repeat(20000);
      const result = sanitizeString(longString, { maxLength: 100 });
      expect(result.length).toBe(100);
    });

    it('should remove control characters', () => {
      const input = 'hello\x00\x01\x02world\x7F';
      const result = sanitizeString(input);
      expect(result).toBe('helloworld');
    });

    it('should normalize unicode', () => {
      const input = 'café'; // Using combining characters
      const result = sanitizeString(input);
      expect(result).toBe('café');
    });

    describe('STRICT level', () => {
      it('should remove all HTML tags', () => {
        const input = '<p>Hello <strong>world</strong></p>';
        const result = sanitizeString(input, { level: 'STRICT' });
        expect(result).not.toContain('<');
        expect(result).not.toContain('>');
      });

      it('should remove script tags', () => {
        const input = '<script>alert("xss")</script>Hello';
        const result = sanitizeString(input, { level: 'STRICT' });
        expect(result).not.toContain('script');
        expect(result).not.toContain('alert');
      });

      it('should encode special characters', () => {
        const input = '<>&"\'';
        const result = sanitizeString(input, { level: 'STRICT' });
        expect(result).toContain('&lt;');
        expect(result).toContain('&gt;');
        expect(result).toContain('&amp;');
      });
    });

    describe('MODERATE level', () => {
      it('should allow some HTML when allowHtml is true', () => {
        const input = '<p>Hello <strong>world</strong></p>';
        const result = sanitizeString(input, {
          level: 'MODERATE',
          allowHtml: true
        });
        expect(result).toContain('<p>');
        expect(result).toContain('<strong>');
      });

      it('should still remove script tags even with allowHtml', () => {
        const input = '<p>Hello</p><script>alert("xss")</script>';
        const result = sanitizeString(input, {
          level: 'MODERATE',
          allowHtml: true
        });
        expect(result).toContain('<p>');
        expect(result).not.toContain('script');
      });
    });

    describe('BASIC level', () => {
      it('should only remove script tags', () => {
        const input = '<p>Hello</p><script>alert("xss")</script>';
        const result = sanitizeString(input, { level: 'BASIC' });
        expect(result).not.toContain('script');
        expect(result).toContain('Hello');
      });
    });
  });

  describe('containsXssPatterns', () => {
    it('should detect javascript: protocol', () => {
      const jsProtocol = 'javascript' + ':' + 'alert(1)';
      const jsProtocolUpper = 'JAVASCRIPT' + ':' + 'alert(1)';
      expect(containsXssPatterns(jsProtocol)).toBe(true);
      expect(containsXssPatterns(jsProtocolUpper)).toBe(true);
    });

    it('should detect vbscript: protocol', () => {
      expect(containsXssPatterns('vbscript:msgbox(1)')).toBe(true);
    });

    it('should detect script tags', () => {
      expect(containsXssPatterns('<script>alert(1)</script>')).toBe(true);
      expect(containsXssPatterns('<SCRIPT>alert(1)</SCRIPT>')).toBe(true);
    });

    it('should detect event handlers', () => {
      expect(containsXssPatterns('onload=alert(1)')).toBe(true);
      expect(containsXssPatterns('onclick=alert(1)')).toBe(true);
      expect(containsXssPatterns('onerror=alert(1)')).toBe(true);
    });

    it('should not flag safe content', () => {
      expect(containsXssPatterns('Hello world')).toBe(false);
      expect(containsXssPatterns('<EMAIL>')).toBe(false);
      expect(containsXssPatterns('https://example.com')).toBe(false);
    });
  });

  describe('containsSqlInjection', () => {
    it('should detect SQL injection patterns', () => {
      expect(containsSqlInjection("'; DROP TABLE users; --")).toBe(true);
      expect(containsSqlInjection("' OR '1'='1")).toBe(true);
      expect(containsSqlInjection("1; SELECT * FROM users")).toBe(true);
      expect(containsSqlInjection("/* comment */ SELECT")).toBe(true);
    });

    it('should not flag safe content', () => {
      expect(containsSqlInjection('Hello world')).toBe(false);
      expect(containsSqlInjection('<EMAIL>')).toBe(false);
      expect(containsSqlInjection('Price: $19.99')).toBe(false);
    });
  });

  describe('sanitizeObject', () => {
    it('should sanitize string properties', () => {
      const input = {
        name: '<script>alert("xss")</script>John',
        email: '<EMAIL>',
        comment: '<p>Hello</p>',
      };

      const result = sanitizeObject(input);

      expect(result.name).not.toContain('script');
      expect(result.name).toContain('John');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should sanitize array properties', () => {
      const input = {
        tags: ['<script>alert(1)</script>tag1', 'tag2', 'tag3'],
        numbers: [1, 2, 3],
      };

      const result = sanitizeObject(input);

      expect(result.tags[0]).not.toContain('script');
      expect(result.tags[0]).toContain('tag1');
      expect(result.numbers).toEqual([1, 2, 3]);
    });

    it('should apply field-specific configuration', () => {
      const input = {
        title: '<b>Important</b>',
        description: '<p>Description with <strong>formatting</strong></p>',
      };

      const fieldConfig = {
        title: { level: 'STRICT' as const },
        description: { level: 'MODERATE' as const, allowHtml: true },
      };

      const result = sanitizeObject(input, fieldConfig);

      expect(result.title).not.toContain('<b>');
      expect(result.description).toContain('<p>');
      expect(result.description).toContain('<strong>');
    });

    it('should preserve non-string properties', () => {
      const input = {
        name: 'John',
        age: 30,
        active: true,
        data: { nested: 'value' },
      };

      const result = sanitizeObject(input);

      expect(result.age).toBe(30);
      expect(result.active).toBe(true);
      expect(result.data).toEqual({ nested: 'value' });
    });
  });

  describe('validateRateLimit', () => {
    it('should allow requests within limit', () => {
      const identifier = 'test-operation';

      expect(validateRateLimit(identifier, 3, 60000)).toBe(true);
      expect(validateRateLimit(identifier, 3, 60000)).toBe(true);
      expect(validateRateLimit(identifier, 3, 60000)).toBe(true);
    });

    it('should block requests exceeding limit', () => {
      const identifier = 'test-operation-2';

      expect(validateRateLimit(identifier, 2, 60000)).toBe(true);
      expect(validateRateLimit(identifier, 2, 60000)).toBe(true);
      expect(validateRateLimit(identifier, 2, 60000)).toBe(false);
    });

    it('should reset after time window expires', () => {
      const identifier = 'test-operation-3';

      // Mock Date.now to control time
      const originalNow = Date.now;
      let currentTime = 1000000;
      Date.now = jest.fn(() => currentTime);

      // Use up the limit
      expect(validateRateLimit(identifier, 2, 1000)).toBe(true);
      expect(validateRateLimit(identifier, 2, 1000)).toBe(true);
      expect(validateRateLimit(identifier, 2, 1000)).toBe(false);

      // Advance time past window
      currentTime += 2000;

      // Should allow again
      expect(validateRateLimit(identifier, 2, 1000)).toBe(true);

      // Restore Date.now
      Date.now = originalNow;
    });

    it('should handle localStorage errors gracefully', () => {
      const identifier = 'test-operation-4';

      // Mock localStorage to throw error
      const originalGetItem = localStorage.getItem;
      localStorage.getItem = jest.fn(() => {
        throw new Error('Storage error');
      });

      // Should still work with default behavior
      expect(validateRateLimit(identifier, 3, 60000)).toBe(true);

      // Restore localStorage
      localStorage.getItem = originalGetItem;
    });
  });

  describe('clearRateLimit', () => {
    it('should clear rate limit for identifier', () => {
      const identifier = 'test-clear';

      // Use up the limit
      validateRateLimit(identifier, 1, 60000);
      expect(validateRateLimit(identifier, 1, 60000)).toBe(false);

      // Clear and try again
      clearRateLimit(identifier);
      expect(validateRateLimit(identifier, 1, 60000)).toBe(true);
    });
  });

  describe('integration tests', () => {
    it('should handle complex malicious input', () => {
      const maliciousInput = {
        username: `<script>fetch('/api/steal', {method: 'POST', body: document.cookie})</script>admin`,
        comment: `'; DROP TABLE users; --<iframe src="${'javascript' + ':'}alert('XSS')"></iframe>`,
        search: `onload="alert('XSS')" style="background:url('${'javascript' + ':'}alert(1)')"`,
      };

      const result = sanitizeObject(maliciousInput);

      // Should not contain any dangerous patterns
      expect(result.username).not.toContain('script');
      expect(result.username).not.toContain('fetch');
      expect(result.comment).not.toContain('DROP TABLE');
      expect(result.comment).not.toContain('iframe');
      expect(result.search).not.toContain('onload');
      expect(result.search).not.toContain('javascript' + ':');

      // Should preserve safe content
      expect(result.username).toContain('admin');
    });

    it('should maintain performance with large inputs', () => {
      const largeInput = 'a'.repeat(50000);

      const startTime = performance.now();
      const result = sanitizeString(largeInput);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
      expect(result).toBeDefined();
    });
  });
});