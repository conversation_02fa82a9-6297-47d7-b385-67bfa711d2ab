import React, { useState } from 'react';
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useSearch } from '@/contexts/SearchContext';
import { cn } from '@/lib/utils';

interface FilterPanelProps {
  className?: string;
  showTitle?: boolean;
  collapsible?: boolean;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  className,
  showTitle = true,
  collapsible = false,
}) => {
  const {
    filters,
    setFilters,
    clearFilters,
    hasActiveFilters,
    getFilterOptions,
  } = useSearch();

  const [expandedSections, setExpandedSections] = useState({
    category: true,
    price: true,
    type: true,
    availability: true,
    ingredients: false,
    benefits: false,
    scent: false,
  });

  const filterOptions = getFilterOptions();

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleCategoryChange = (category: string, checked: boolean) => {
    setFilters({
      category: checked ? category : undefined,
    });
  };

  const handlePriceRangeChange = (values: number[]) => {
    setFilters({
      priceRange: {
        min: values[0],
        max: values[1],
      },
    });
  };

  const handleTypeChange = (type: 'bar' | 'liquid', checked: boolean) => {
    setFilters({
      type: checked ? type : undefined,
    });
  };

  const handleAvailabilityChange = (field: 'inStock' | 'featured', checked: boolean) => {
    setFilters({
      [field]: checked ? true : undefined,
    });
  };

  const handleIngredientChange = (ingredient: string, checked: boolean) => {
    const currentIngredients = filters.ingredients || [];
    const newIngredients = checked
      ? [...currentIngredients, ingredient]
      : currentIngredients.filter(i => i !== ingredient);
    
    setFilters({
      ingredients: newIngredients.length > 0 ? newIngredients : undefined,
    });
  };

  const handleBenefitChange = (benefit: string, checked: boolean) => {
    const currentBenefits = filters.benefits || [];
    const newBenefits = checked
      ? [...currentBenefits, benefit]
      : currentBenefits.filter(b => b !== benefit);
    
    setFilters({
      benefits: newBenefits.length > 0 ? newBenefits : undefined,
    });
  };

  const handleScentChange = (scent: string, checked: boolean) => {
    setFilters({
      scent: checked ? scent : undefined,
    });
  };

  const FilterSection: React.FC<{
    title: string;
    sectionKey: keyof typeof expandedSections;
    children: React.ReactNode;
  }> = ({ title, sectionKey, children }) => {
    if (collapsible) {
      return (
        <Collapsible
          open={expandedSections[sectionKey]}
          onOpenChange={() => toggleSection(sectionKey)}
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-0 h-auto font-medium text-sabone-cream hover:text-sabone-gold"
            >
              {title}
              {expandedSections[sectionKey] ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3">
            {children}
          </CollapsibleContent>
        </Collapsible>
      );
    }

    return (
      <div>
        <h4 className="font-medium text-sabone-cream mb-3">{title}</h4>
        {children}
      </div>
    );
  };

  return (
    <Card className={cn("bg-sabone-charcoal border-sabone-gold/30", className)}>
      {showTitle && (
        <CardHeader className="pb-4">
          <CardTitle className="text-sabone-cream flex items-center justify-between">
            <span className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </span>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-sabone-gold hover:text-sabone-cream h-auto p-1"
              >
                Clear All
              </Button>
            )}
          </CardTitle>
          
          {/* Active filters display */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2 mt-2">
              {filters.category && (
                <Badge variant="secondary" className="bg-sabone-gold/20 text-sabone-cream">
                  {filters.category}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => setFilters({ category: undefined })}
                  />
                </Badge>
              )}
              {filters.type && (
                <Badge variant="secondary" className="bg-sabone-gold/20 text-sabone-cream">
                  {filters.type}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => setFilters({ type: undefined })}
                  />
                </Badge>
              )}
              {filters.priceRange && (
                <Badge variant="secondary" className="bg-sabone-gold/20 text-sabone-cream">
                  ${filters.priceRange.min} - ${filters.priceRange.max}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => setFilters({ priceRange: undefined })}
                  />
                </Badge>
              )}
              {filters.inStock && (
                <Badge variant="secondary" className="bg-sabone-gold/20 text-sabone-cream">
                  In Stock
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => setFilters({ inStock: undefined })}
                  />
                </Badge>
              )}
              {filters.featured && (
                <Badge variant="secondary" className="bg-sabone-gold/20 text-sabone-cream">
                  Featured
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => setFilters({ featured: undefined })}
                  />
                </Badge>
              )}
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className="space-y-6">
        {/* Category Filter */}
        <FilterSection title="Category" sectionKey="category">
          <div className="space-y-2">
            {filterOptions.categories.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category}`}
                  checked={filters.category === category}
                  onCheckedChange={(checked) => handleCategoryChange(category, checked as boolean)}
                />
                <Label
                  htmlFor={`category-${category}`}
                  className="text-sm text-sabone-cream/80 cursor-pointer capitalize"
                >
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Price Range Filter */}
        <FilterSection title="Price Range" sectionKey="price">
          <div className="space-y-4">
            <Slider
              value={[
                filters.priceRange?.min ?? filterOptions.priceRange.min,
                filters.priceRange?.max ?? filterOptions.priceRange.max,
              ]}
              onValueChange={handlePriceRangeChange}
              min={filterOptions.priceRange.min}
              max={filterOptions.priceRange.max}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-sabone-cream/80">
              <span>${filters.priceRange?.min ?? filterOptions.priceRange.min}</span>
              <span>${filters.priceRange?.max ?? filterOptions.priceRange.max}</span>
            </div>
          </div>
        </FilterSection>

        {/* Product Type Filter */}
        <FilterSection title="Product Type" sectionKey="type">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-bar"
                checked={filters.type === 'bar'}
                onCheckedChange={(checked) => handleTypeChange('bar', checked as boolean)}
              />
              <Label htmlFor="type-bar" className="text-sm text-sabone-cream/80 cursor-pointer">
                Bar Soap
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-liquid"
                checked={filters.type === 'liquid'}
                onCheckedChange={(checked) => handleTypeChange('liquid', checked as boolean)}
              />
              <Label htmlFor="type-liquid" className="text-sm text-sabone-cream/80 cursor-pointer">
                Liquid Soap
              </Label>
            </div>
          </div>
        </FilterSection>

        {/* Availability Filter */}
        <FilterSection title="Availability" sectionKey="availability">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="in-stock"
                checked={filters.inStock === true}
                onCheckedChange={(checked) => handleAvailabilityChange('inStock', checked as boolean)}
              />
              <Label htmlFor="in-stock" className="text-sm text-sabone-cream/80 cursor-pointer">
                In Stock
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={filters.featured === true}
                onCheckedChange={(checked) => handleAvailabilityChange('featured', checked as boolean)}
              />
              <Label htmlFor="featured" className="text-sm text-sabone-cream/80 cursor-pointer">
                Featured Products
              </Label>
            </div>
          </div>
        </FilterSection>

        {/* Ingredients Filter */}
        <FilterSection title="Key Ingredients" sectionKey="ingredients">
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {filterOptions.ingredients.slice(0, 10).map((ingredient) => (
              <div key={ingredient} className="flex items-center space-x-2">
                <Checkbox
                  id={`ingredient-${ingredient}`}
                  checked={filters.ingredients?.includes(ingredient) || false}
                  onCheckedChange={(checked) => handleIngredientChange(ingredient, checked as boolean)}
                />
                <Label
                  htmlFor={`ingredient-${ingredient}`}
                  className="text-sm text-sabone-cream/80 cursor-pointer"
                >
                  {ingredient}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Benefits Filter */}
        <FilterSection title="Benefits" sectionKey="benefits">
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {filterOptions.benefits.slice(0, 10).map((benefit) => (
              <div key={benefit} className="flex items-center space-x-2">
                <Checkbox
                  id={`benefit-${benefit}`}
                  checked={filters.benefits?.includes(benefit) || false}
                  onCheckedChange={(checked) => handleBenefitChange(benefit, checked as boolean)}
                />
                <Label
                  htmlFor={`benefit-${benefit}`}
                  className="text-sm text-sabone-cream/80 cursor-pointer"
                >
                  {benefit}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Scent Filter */}
        <FilterSection title="Scent" sectionKey="scent">
          <div className="space-y-2">
            {filterOptions.scents.map((scent) => (
              <div key={scent} className="flex items-center space-x-2">
                <Checkbox
                  id={`scent-${scent}`}
                  checked={filters.scent === scent}
                  onCheckedChange={(checked) => handleScentChange(scent, checked as boolean)}
                />
                <Label
                  htmlFor={`scent-${scent}`}
                  className="text-sm text-sabone-cream/80 cursor-pointer capitalize"
                >
                  {scent}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>
      </CardContent>
    </Card>
  );
};

export default FilterPanel;
