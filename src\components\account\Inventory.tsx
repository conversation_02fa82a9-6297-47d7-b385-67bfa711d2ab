import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useInventory } from "@/contexts/InventoryContext";
import { products } from "@/data/products";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Package, RefreshCw, AlertTriangle } from "lucide-react";

const Inventory = () => {
  const { user } = useAuth();
  const { 
    inventory, 
    loading, 
    getProductStock, 
    isProductInStock, 
    isLowStock,
    updateProductStock,
    refreshInventory,
    lowStockItems
  } = useInventory();
  
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [restockQuantity, setRestockQuantity] = useState(10);
  const [isRestocking, setIsRestocking] = useState(false);
  
  // Refresh inventory data on mount
  useEffect(() => {
    refreshInventory();
  }, [refreshInventory]);
  
  const handleRestock = async () => {
    if (!selectedProduct) return;
    
    setIsRestocking(true);
    try {
      await updateProductStock({
        productId: selectedProduct.id,
        quantityChange: parseInt(restockQuantity),
        reason: 'restock'
      });
      
      toast.success(`Restocked ${selectedProduct.name} with ${restockQuantity} units`);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Error restocking product:', error);
      toast.error('Failed to restock product');
    } finally {
      setIsRestocking(false);
    }
  };
  
  // Only admins should be able to access this page
  if (user?.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-xl font-playfair font-semibold text-sabone-gold mb-2">
          Access Restricted
        </h2>
        <p className="text-sabone-cream/70">
          You don't have permission to access inventory management.
        </p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Inventory Management</h2>
          <p className="text-sabone-cream/70 mt-1">Manage product stock levels</p>
        </div>
        
        <Button 
          variant="outline" 
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          onClick={() => refreshInventory()}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
      
      <Separator className="bg-sabone-gold/20" />
      
      {lowStockItems.length > 0 && (
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-md p-4 mb-6">
          <div className="flex items-center text-yellow-500 mb-2">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <h3 className="font-medium">Low Stock Alert</h3>
          </div>
          <p className="text-sabone-cream/80 text-sm mb-2">
            The following products are running low on stock:
          </p>
          <ul className="list-disc list-inside text-sabone-cream/70 text-sm">
            {lowStockItems.map(item => {
              const product = products.find(p => p.id === item.productId);
              return (
                <li key={item.productId}>
                  {product?.name} - Only {item.stockQuantity} units left
                </li>
              );
            })}
          </ul>
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-12">
          <p className="text-sabone-cream/70">Loading inventory data...</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-sabone-gold/20">
                <TableHead className="text-sabone-gold">Product</TableHead>
                <TableHead className="text-sabone-gold">Stock</TableHead>
                <TableHead className="text-sabone-gold">Status</TableHead>
                <TableHead className="text-sabone-gold">Last Updated</TableHead>
                <TableHead className="text-sabone-gold text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => {
                const stockQuantity = getProductStock(product.id);
                const inStock = isProductInStock(product.id);
                const lowStock = isLowStock(product.id);
                const inventoryItem = inventory.find(item => item.productId === product.id);
                
                return (
                  <TableRow key={product.id} className="border-sabone-gold/20">
                    <TableCell className="text-sabone-cream font-medium">{product.name}</TableCell>
                    <TableCell className="text-sabone-cream">{stockQuantity}</TableCell>
                    <TableCell>
                      {!inStock ? (
                        <Badge variant="destructive" className="bg-red-500/20 text-red-500 hover:bg-red-500/30">
                          Out of Stock
                        </Badge>
                      ) : lowStock ? (
                        <Badge variant="outline" className="bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30 border-yellow-500/30">
                          Low Stock
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-green-500/20 text-green-500 hover:bg-green-500/30 border-green-500/30">
                          In Stock
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-sabone-cream">
                      {inventoryItem ? new Date(inventoryItem.lastUpdated).toLocaleDateString() : 'N/A'}
                    </TableCell>
                    <TableCell className="text-right">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                            onClick={() => setSelectedProduct(product)}
                          >
                            <Package className="h-4 w-4 mr-1" />
                            Restock
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
                          <DialogHeader>
                            <DialogTitle className="text-sabone-gold">Restock {product.name}</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4 mt-4">
                            <div className="flex justify-between">
                              <span className="text-sabone-cream/70">Current Stock:</span>
                              <span className="text-sabone-cream">{stockQuantity} units</span>
                            </div>
                            
                            <div className="space-y-2">
                              <label className="text-sabone-cream/70">Quantity to Add:</label>
                              <Input
                                type="number"
                                min="1"
                                value={restockQuantity}
                                onChange={(e) => setRestockQuantity(parseInt(e.target.value) || 0)}
                                className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                              />
                            </div>
                          </div>
                          
                          <DialogFooter className="mt-6">
                            <DialogClose asChild>
                              <Button 
                                variant="outline"
                                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                              >
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button 
                              className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                              onClick={handleRestock}
                              disabled={isRestocking || restockQuantity <= 0}
                            >
                              {isRestocking ? 'Processing...' : 'Restock'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default Inventory;
