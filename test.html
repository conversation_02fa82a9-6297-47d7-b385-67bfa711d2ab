<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Page</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #1c1c1c;
      color: #e5dcc5;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: #2a2a1f;
      border-radius: 8px;
      border: 1px solid #c6a870;
    }
    h1 {
      color: #c6a870;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Sabone Test Page</h1>
    <p>If you can see this content, your browser is rendering HTML correctly.</p>
    <p>The issue is likely with the React application itself.</p>
  </div>
  <script>
    console.log('Test page loaded successfully');
  </script>
</body>
</html>
