/**
 * Sabone.store Checkout Test Server
 * 
 * This script provides test endpoints for the checkout flow testing.
 * It simulates the payment processing APIs for both Stripe and PayPal.
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// Initialize environment variables
dotenv.config();

// Create Express app
const app = express();
const PORT = process.env.TEST_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Test data
const testData = {
  stripePaymentIntents: {},
  paypalOrders: {}
};

/**
 * Test endpoint for Stripe payment intent creation
 */
app.post('/api/create-payment-intent', (req, res) => {
  try {
    const { amount, currency = 'usd' } = req.body;

    // Validate the amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount. Amount must be greater than 0.'
      });
    }

    // Generate a fake payment intent ID and client secret
    const paymentIntentId = `pi_test_${uuidv4().replace(/-/g, '')}`;
    const clientSecret = `${paymentIntentId}_secret_${uuidv4().replace(/-/g, '')}`;

    // Store the payment intent in test data
    testData.stripePaymentIntents[paymentIntentId] = {
      id: paymentIntentId,
      amount,
      currency,
      status: 'requires_payment_method',
      client_secret: clientSecret,
      created: Date.now()
    };

    console.log(`🧪 Created test payment intent: ${paymentIntentId}`);

    // Return the client secret to the client
    res.json({
      clientSecret,
      paymentIntentId
    });
  } catch (error) {
    console.error('Error creating test payment intent:', error);
    res.status(500).json({
      error: error.message || 'An error occurred while creating the test payment intent.'
    });
  }
});

/**
 * Test endpoint for Stripe payment confirmation
 */
app.post('/api/confirm-payment', (req, res) => {
  try {
    const { paymentIntentId, paymentMethodId } = req.body;

    // Validate the payment intent ID
    if (!paymentIntentId || !testData.stripePaymentIntents[paymentIntentId]) {
      return res.status(400).json({
        error: 'Invalid payment intent ID.'
      });
    }

    // Update the payment intent status
    testData.stripePaymentIntents[paymentIntentId].status = 'succeeded';
    testData.stripePaymentIntents[paymentIntentId].payment_method = paymentMethodId;

    console.log(`🧪 Confirmed test payment intent: ${paymentIntentId}`);

    // Return the updated payment intent
    res.json({
      paymentIntent: testData.stripePaymentIntents[paymentIntentId]
    });
  } catch (error) {
    console.error('Error confirming test payment:', error);
    res.status(500).json({
      error: error.message || 'An error occurred while confirming the test payment.'
    });
  }
});

/**
 * Test endpoint for PayPal order creation
 */
app.post('/api/create-paypal-order', (req, res) => {
  try {
    const { amount, currency = 'USD' } = req.body;

    // Validate the amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount. Amount must be greater than 0.'
      });
    }

    // Generate a fake PayPal order ID
    const orderId = `PAYPAL-TEST-${uuidv4().replace(/-/g, '').toUpperCase()}`;

    // Store the order in test data
    testData.paypalOrders[orderId] = {
      id: orderId,
      status: 'CREATED',
      amount: {
        currency_code: currency,
        value: amount.toFixed(2)
      },
      created: Date.now()
    };

    console.log(`🧪 Created test PayPal order: ${orderId}`);

    // Return the order ID and details to the client
    res.json({
      orderId,
      status: 'CREATED',
      links: [
        {
          href: `http://localhost:${PORT}/paypal/checkout/${orderId}`,
          rel: 'approve',
          method: 'GET'
        }
      ]
    });
  } catch (error) {
    console.error('Error creating test PayPal order:', error);
    res.status(500).json({
      error: 'Failed to create test PayPal order'
    });
  }
});

/**
 * Test endpoint for PayPal payment capture
 */
app.post('/api/capture-paypal-payment', (req, res) => {
  try {
    const { orderId } = req.body;

    // Validate the order ID
    if (!orderId || !testData.paypalOrders[orderId]) {
      return res.status(400).json({
        error: 'Invalid order ID'
      });
    }

    // Update the order status
    testData.paypalOrders[orderId].status = 'COMPLETED';

    // Generate a capture ID
    const captureId = `CAPTURE-TEST-${uuidv4().replace(/-/g, '').toUpperCase()}`;
    const payerId = `PAYER-TEST-${uuidv4().replace(/-/g, '').toUpperCase()}`;

    console.log(`🧪 Captured test PayPal payment for order: ${orderId}`);

    // Return the capture details to the client
    res.json({
      captureId,
      status: 'COMPLETED',
      payerId,
      paymentId: captureId
    });
  } catch (error) {
    console.error('Error capturing test PayPal payment:', error);
    res.status(500).json({
      error: 'Failed to capture test PayPal payment'
    });
  }
});

/**
 * Test endpoint for email sending
 */
app.post('/api/send-email', (req, res) => {
  try {
    const { to, subject, html } = req.body;

    // Validate the email data
    if (!to || !subject || !html) {
      return res.status(400).json({
        error: 'Invalid email data. Required fields: to, subject, html.'
      });
    }

    console.log(`🧪 Simulated sending email to: ${to}`);
    console.log(`🧪 Subject: ${subject}`);
    console.log(`🧪 Email content length: ${html.length} characters`);

    // Return success response
    res.json({
      success: true,
      messageId: `EMAIL-TEST-${uuidv4().replace(/-/g, '').toUpperCase()}`
    });
  } catch (error) {
    console.error('Error simulating email send:', error);
    res.status(500).json({
      error: 'Failed to simulate email send'
    });
  }
});

/**
 * Test endpoint for order creation
 */
app.post('/api/create-order', (req, res) => {
  try {
    const { order } = req.body;

    // Validate the order data
    if (!order) {
      return res.status(400).json({
        error: 'Invalid order data.'
      });
    }

    console.log(`🧪 Simulated creating order: ${order.id}`);
    console.log(`🧪 Order total: ${order.total}`);
    console.log(`🧪 Payment method: ${order.paymentMethod}`);

    // Return success response
    res.json({
      success: true,
      order: {
        ...order,
        status: 'pending',
        createdAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error simulating order creation:', error);
    res.status(500).json({
      error: 'Failed to simulate order creation'
    });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`🧪 Checkout test server running on port ${PORT}`);
  console.log(`🧪 Test endpoints available at http://localhost:${PORT}/api`);
  console.log('🧪 Available test endpoints:');
  console.log('  - POST /api/create-payment-intent');
  console.log('  - POST /api/confirm-payment');
  console.log('  - POST /api/create-paypal-order');
  console.log('  - POST /api/capture-paypal-payment');
  console.log('  - POST /api/send-email');
  console.log('  - POST /api/create-order');
});

export default app;
