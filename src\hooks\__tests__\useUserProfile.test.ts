import { renderHook, act } from '@testing-library/react';
import { toast } from 'sonner';
import { useUserProfile } from '../useUserProfile';
import { uploadProfilePicture, getUserProfile, saveUserProfile } from '@/services/userService';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock userService
jest.mock('@/services/userService', () => ({
  uploadProfilePicture: jest.fn(),
  getUserProfile: jest.fn(),
  saveUserProfile: jest.fn(),
}));

describe('useUserProfile', () => {
  const mockUploadProfilePicture = uploadProfilePicture as jest.MockedFunction<typeof uploadProfilePicture>;
  const mockGetUserProfile = getUserProfile as jest.MockedFunction<typeof getUserProfile>;
  const mockSaveUserProfile = saveUserProfile as jest.MockedFunction<typeof saveUserProfile>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('updateProfile', () => {
    it('should update profile successfully in development mode', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockUpdateDevUser = jest.fn();
      const userData = { name: 'Test User', phone_number: '+1234567890' };

      mockGetUserProfile.mockReturnValue(null);
      mockSaveUserProfile.mockReturnValue(true);

      const success = await act(async () => {
        return await result.current.updateProfile(
          'user-123',
          userData,
          true, // isDevelopmentMode
          mockUpdateDevUser
        );
      });

      expect(success).toBe(true);
      expect(mockUpdateDevUser).toHaveBeenCalledWith(userData);
      expect(mockSaveUserProfile).toHaveBeenCalledWith({
        userId: 'user-123',
        lastUpdated: expect.any(String),
        name: 'Test User',
        phone: '+1234567890'
      });
      expect(toast.success).toHaveBeenCalledWith('Profile updated successfully');
    });

    it('should update profile successfully in production mode', async () => {
      const { result } = renderHook(() => useUserProfile());
      const userData = { name: 'Test User', phone_number: '+1234567890' };

      mockGetUserProfile.mockReturnValue(null);
      mockSaveUserProfile.mockReturnValue(true);

      const success = await act(async () => {
        return await result.current.updateProfile(
          'user-123',
          userData,
          false // isDevelopmentMode
        );
      });

      expect(success).toBe(true);
      expect(mockSaveUserProfile).toHaveBeenCalledWith({
        userId: 'user-123',
        lastUpdated: expect.any(String),
        name: 'Test User',
        phone: '+1234567890'
      });
      expect(toast.success).toHaveBeenCalledWith('Profile updated successfully');
    });

    it('should merge with existing profile data', async () => {
      const { result } = renderHook(() => useUserProfile());
      const userData = { name: 'Updated User' };
      const existingProfile = {
        userId: 'user-123',
        name: 'Old User',
        phone: '+0987654321',
        lastUpdated: '2023-01-01T00:00:00.000Z'
      };

      mockGetUserProfile.mockReturnValue(existingProfile);
      mockSaveUserProfile.mockReturnValue(true);

      const success = await act(async () => {
        return await result.current.updateProfile(
          'user-123',
          userData,
          false
        );
      });

      expect(success).toBe(true);
      expect(mockSaveUserProfile).toHaveBeenCalledWith({
        userId: 'user-123',
        name: 'Updated User',
        phone: '+0987654321',
        lastUpdated: '2023-01-01T00:00:00.000Z'
      });
    });

    it('should handle profile save failure', async () => {
      const { result } = renderHook(() => useUserProfile());
      const userData = { name: 'Test User' };

      mockGetUserProfile.mockReturnValue(null);
      mockSaveUserProfile.mockReturnValue(false);

      const success = await act(async () => {
        return await result.current.updateProfile(
          'user-123',
          userData,
          false
        );
      });

      expect(success).toBe(false);
      expect(toast.error).toHaveBeenCalledWith('Failed to update profile');
    });

    it('should handle errors gracefully', async () => {
      const { result } = renderHook(() => useUserProfile());
      const userData = { name: 'Test User' };

      mockGetUserProfile.mockImplementation(() => {
        throw new Error('Profile service error');
      });

      const success = await act(async () => {
        return await result.current.updateProfile(
          'user-123',
          userData,
          false
        );
      });

      expect(success).toBe(false);
      expect(toast.error).toHaveBeenCalledWith('Failed to update profile');
    });

    it('should handle partial profile updates', async () => {
      const { result } = renderHook(() => useUserProfile());
      const userData = { phone_number: '+1111111111' };
      const existingProfile = {
        userId: 'user-123',
        name: 'Existing User',
        lastUpdated: '2023-01-01T00:00:00.000Z'
      };

      mockGetUserProfile.mockReturnValue(existingProfile);
      mockSaveUserProfile.mockReturnValue(true);

      await act(async () => {
        await result.current.updateProfile(
          'user-123',
          userData,
          false
        );
      });

      expect(mockSaveUserProfile).toHaveBeenCalledWith({
        userId: 'user-123',
        name: 'Existing User',
        phone: '+1111111111',
        lastUpdated: '2023-01-01T00:00:00.000Z'
      });
    });
  });

  describe('updateProfilePicture', () => {
    it('should update profile picture successfully in development mode', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockUpdateDevUser = jest.fn();
      const expectedPath = '/uploads/user-123/profile.jpg';

      mockUploadProfilePicture.mockResolvedValue(expectedPath);

      const picturePath = await act(async () => {
        return await result.current.updateProfilePicture(
          'user-123',
          mockFile,
          true, // isDevelopmentMode
          mockUpdateDevUser
        );
      });

      expect(picturePath).toBe(expectedPath);
      expect(mockUploadProfilePicture).toHaveBeenCalledWith('user-123', mockFile);
      expect(mockUpdateDevUser).toHaveBeenCalledWith({ picture: expectedPath });
      expect(toast.success).toHaveBeenCalledWith('Profile picture updated successfully');
    });

    it('should update profile picture successfully in production mode', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const expectedPath = '/uploads/user-123/profile.jpg';

      mockUploadProfilePicture.mockResolvedValue(expectedPath);

      const picturePath = await act(async () => {
        return await result.current.updateProfilePicture(
          'user-123',
          mockFile,
          false // isDevelopmentMode
        );
      });

      expect(picturePath).toBe(expectedPath);
      expect(mockUploadProfilePicture).toHaveBeenCalledWith('user-123', mockFile);
      expect(toast.success).toHaveBeenCalledWith('Profile picture updated successfully');
    });

    it('should handle profile picture upload failure', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      mockUploadProfilePicture.mockRejectedValue(new Error('Upload failed'));

      const picturePath = await act(async () => {
        return await result.current.updateProfilePicture(
          'user-123',
          mockFile,
          false
        );
      });

      expect(picturePath).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Failed to update profile picture');
    });

    it('should not update dev user when not in development mode', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockUpdateDevUser = jest.fn();
      const expectedPath = '/uploads/user-123/profile.jpg';

      mockUploadProfilePicture.mockResolvedValue(expectedPath);

      await act(async () => {
        await result.current.updateProfilePicture(
          'user-123',
          mockFile,
          false, // isDevelopmentMode
          mockUpdateDevUser
        );
      });

      expect(mockUpdateDevUser).not.toHaveBeenCalled();
    });

    it('should handle different file types', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' });
      const expectedPath = '/uploads/user-123/profile.png';

      mockUploadProfilePicture.mockResolvedValue(expectedPath);

      const picturePath = await act(async () => {
        return await result.current.updateProfilePicture(
          'user-123',
          mockFile,
          false
        );
      });

      expect(picturePath).toBe(expectedPath);
      expect(mockUploadProfilePicture).toHaveBeenCalledWith('user-123', mockFile);
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      const { result } = renderHook(() => useUserProfile());
      const userData = { name: 'Test User' };

      mockGetUserProfile.mockImplementation(() => {
        throw new Error('Network error');
      });

      const success = await act(async () => {
        return await result.current.updateProfile(
          'user-123',
          userData,
          false
        );
      });

      expect(success).toBe(false);
      expect(toast.error).toHaveBeenCalledWith('Failed to update profile');
    });

    it('should handle file upload errors', async () => {
      const { result } = renderHook(() => useUserProfile());
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      mockUploadProfilePicture.mockRejectedValue(new Error('File too large'));

      const picturePath = await act(async () => {
        return await result.current.updateProfilePicture(
          'user-123',
          mockFile,
          false
        );
      });

      expect(picturePath).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Failed to update profile picture');
    });
  });
}); 