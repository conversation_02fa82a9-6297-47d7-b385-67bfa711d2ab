{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "./src", "./server", "./docs"], "description": "File system operations for Sabone project", "capabilities": ["read", "write", "search", "analyze"]}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "."], "description": "Git operations for version control", "capabilities": ["status", "diff", "log", "branch"]}, "typescript-analyzer": {"command": "node", "args": ["./tools/typescript-analyzer.js"], "description": "TypeScript error analysis and fixing", "capabilities": ["analyze", "fix", "report"]}, "eslint-analyzer": {"command": "node", "args": ["./tools/eslint-analyzer.js"], "description": "ESLint error analysis and fixing", "capabilities": ["lint", "fix", "report"]}, "security-scanner": {"command": "node", "args": ["./tools/security-scanner.js"], "description": "Security vulnerability scanner", "capabilities": ["scan", "fix", "report"]}, "test-coverage": {"command": "node", "args": ["./tools/test-coverage-analyzer.js"], "description": "Test coverage analysis and improvement", "capabilities": ["analyze", "generate", "report"]}, "bundle-analyzer": {"command": "node", "args": ["./tools/bundle-analyzer.js"], "description": "Bundle size analysis and optimization", "capabilities": ["analyze", "optimize", "report"]}}, "phase1_priorities": ["typescript-analyzer", "eslint-analyzer", "security-scanner"], "phase2_priorities": ["bundle-analyzer", "filesystem"], "phase3_priorities": ["test-coverage", "git"]}