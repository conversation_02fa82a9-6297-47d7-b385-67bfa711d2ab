/**
 * Mobile optimization hook for enhanced mobile user experience
 * Provides utilities for touch interactions, performance optimization,
 * and mobile-specific features
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useIsMobile } from './use-mobile';

export interface TouchGesture {
  type: 'tap' | 'double-tap' | 'long-press' | 'swipe' | 'pinch';
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration: number;
  distance: number;
  direction?: 'left' | 'right' | 'up' | 'down';
}

export interface MobileOptimizationOptions {
  enableTouchGestures?: boolean;
  enableHapticFeedback?: boolean;
  enableReducedMotion?: boolean;
  enableDataSaver?: boolean;
  touchThreshold?: number;
  longPressDelay?: number;
  doubleTapDelay?: number;
}

/**
 * Hook for mobile-specific optimizations and touch interactions
 */
export const useMobileOptimization = (options: MobileOptimizationOptions = {}) => {
  const {
    enableTouchGestures = true,
    enableHapticFeedback = true,
    enableReducedMotion = true,
    enableDataSaver = true,
    touchThreshold = 10,
    longPressDelay = 500,
    doubleTapDelay = 300,
  } = options;

  const isMobile = useIsMobile();
  const [isReducedMotion, setIsReducedMotion] = useState(false);
  const [isDataSaver, setIsDataSaver] = useState(false);
  const [networkInfo, setNetworkInfo] = useState<{
    effectiveType: string;
    downlink: number;
    saveData: boolean;
  } | null>(null);

  // Touch gesture state
  const [currentGesture, setCurrentGesture] = useState<TouchGesture | null>(null);
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastTapRef = useRef<number>(0);

  // Check for reduced motion preference
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setIsReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Check for data saver and network information
  useEffect(() => {
    if (typeof window === 'undefined' || !('connection' in navigator)) return;

    const connection = (navigator as any).connection;
    if (connection) {
      const updateNetworkInfo = () => {
        setNetworkInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          saveData: connection.saveData,
        });
        setIsDataSaver(connection.saveData);
      };

      updateNetworkInfo();
      connection.addEventListener('change', updateNetworkInfo);
      return () => connection.removeEventListener('change', updateNetworkInfo);
    }
  }, []);

  // Haptic feedback utility
  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!enableHapticFeedback || !isMobile) return;

    try {
      if ('vibrate' in navigator) {
        const patterns = {
          light: [10],
          medium: [20],
          heavy: [30],
        };
        navigator.vibrate(patterns[type]);
      }
    } catch (error) {
      console.warn('Haptic feedback not supported:', error);
    }
  }, [enableHapticFeedback, isMobile]);

  // Touch gesture handlers
  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!enableTouchGestures || !isMobile) return;

    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
    };

    // Set up long press timer
    longPressTimerRef.current = setTimeout(() => {
      if (touchStartRef.current) {
        const gesture: TouchGesture = {
          type: 'long-press',
          startX: touchStartRef.current.x,
          startY: touchStartRef.current.y,
          endX: touchStartRef.current.x,
          endY: touchStartRef.current.y,
          duration: Date.now() - touchStartRef.current.time,
          distance: 0,
        };
        setCurrentGesture(gesture);
        triggerHapticFeedback('medium');
      }
    }, longPressDelay);
  }, [enableTouchGestures, isMobile, longPressDelay, triggerHapticFeedback]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!enableTouchGestures || !isMobile || !touchStartRef.current) return;

    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    const touch = e.changedTouches[0];
    const endTime = Date.now();
    const duration = endTime - touchStartRef.current.time;
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Determine gesture type
    let gestureType: TouchGesture['type'] = 'tap';
    let direction: TouchGesture['direction'] | undefined;

    if (distance > touchThreshold) {
      gestureType = 'swipe';
      // Determine swipe direction
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        direction = deltaX > 0 ? 'right' : 'left';
      } else {
        direction = deltaY > 0 ? 'down' : 'up';
      }
    } else if (duration < doubleTapDelay && endTime - lastTapRef.current < doubleTapDelay) {
      gestureType = 'double-tap';
      triggerHapticFeedback('light');
    }

    lastTapRef.current = endTime;

    const gesture: TouchGesture = {
      type: gestureType,
      startX: touchStartRef.current.x,
      startY: touchStartRef.current.y,
      endX: touch.clientX,
      endY: touch.clientY,
      duration,
      distance,
      direction,
    };

    setCurrentGesture(gesture);
    touchStartRef.current = null;
  }, [enableTouchGestures, isMobile, touchThreshold, doubleTapDelay, triggerHapticFeedback]);

  // Performance optimization utilities
  const shouldReduceAnimations = enableReducedMotion && isReducedMotion;
  const shouldReduceData = enableDataSaver && (isDataSaver || networkInfo?.effectiveType === 'slow-2g');
  const isSlowConnection = networkInfo?.effectiveType === 'slow-2g' || networkInfo?.effectiveType === '2g';

  // Image quality based on network conditions
  const getOptimalImageQuality = useCallback(() => {
    if (!networkInfo) return 85;

    switch (networkInfo.effectiveType) {
      case 'slow-2g':
        return 60;
      case '2g':
        return 70;
      case '3g':
        return 80;
      case '4g':
      default:
        return 85;
    }
  }, [networkInfo]);

  // Optimal image sizes for mobile
  const getMobileImageSizes = useCallback(() => {
    const baseWidth = window.innerWidth;
    const pixelRatio = window.devicePixelRatio || 1;
    
    return [
      { width: Math.round(baseWidth * 0.5 * pixelRatio), quality: getOptimalImageQuality() },
      { width: Math.round(baseWidth * pixelRatio), quality: getOptimalImageQuality() },
      { width: Math.round(baseWidth * 1.5 * pixelRatio), quality: getOptimalImageQuality() },
    ];
  }, [getOptimalImageQuality]);

  // Touch event setup
  const setupTouchEvents = useCallback((element: HTMLElement) => {
    if (!enableTouchGestures || !isMobile) return () => {};

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enableTouchGestures, isMobile, handleTouchStart, handleTouchEnd]);

  // Scroll optimization
  const optimizeScrolling = useCallback((element: HTMLElement) => {
    if (!isMobile) return () => {};

    // Add momentum scrolling for iOS
    element.style.webkitOverflowScrolling = 'touch';
    element.style.overscrollBehavior = 'contain';

    return () => {
      element.style.webkitOverflowScrolling = '';
      element.style.overscrollBehavior = '';
    };
  }, [isMobile]);

  // Prevent zoom on double tap for specific elements
  const preventZoom = useCallback((element: HTMLElement) => {
    if (!isMobile) return () => {};

    element.style.touchAction = 'manipulation';

    return () => {
      element.style.touchAction = '';
    };
  }, [isMobile]);

  return {
    // State
    isMobile,
    isReducedMotion,
    isDataSaver,
    isSlowConnection,
    networkInfo,
    currentGesture,

    // Optimization flags
    shouldReduceAnimations,
    shouldReduceData,

    // Utilities
    triggerHapticFeedback,
    getOptimalImageQuality,
    getMobileImageSizes,

    // Event handlers
    setupTouchEvents,
    optimizeScrolling,
    preventZoom,

    // Performance helpers
    performanceMode: {
      images: shouldReduceData ? 'low' : 'high',
      animations: shouldReduceAnimations ? 'reduced' : 'full',
      prefetch: !shouldReduceData,
    },
  };
};

/**
 * Hook for mobile-specific image optimization
 */
export const useMobileImageOptimization = () => {
  const { 
    shouldReduceData, 
    getOptimalImageQuality, 
    getMobileImageSizes,
    networkInfo 
  } = useMobileOptimization();

  const getImageOptions = useCallback(() => {
    return {
      quality: getOptimalImageQuality(),
      sizes: getMobileImageSizes(),
      enableWebP: !shouldReduceData,
      enableAVIF: !shouldReduceData && networkInfo?.effectiveType !== 'slow-2g',
      loading: shouldReduceData ? 'lazy' as const : 'eager' as const,
    };
  }, [shouldReduceData, getOptimalImageQuality, getMobileImageSizes, networkInfo]);

  return {
    shouldReduceData,
    getImageOptions,
    networkInfo,
  };
};

export default useMobileOptimization;
