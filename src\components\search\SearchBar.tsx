import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSearch } from '@/contexts/SearchContext';
import { debounce, getPopularSearchTerms } from '@/utils/searchUtils';
import { cn } from '@/lib/utils';

interface SearchBarProps {
  className?: string;
  placeholder?: string;
  showSuggestions?: boolean;
  autoFocus?: boolean;
  onSearchSubmit?: (query: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  className,
  placeholder = "Search for soaps, shampoos, ingredients...",
  showSuggestions = true,
  autoFocus = false,
  onSearchSubmit,
}) => {
  const {
    query,
    setQuery,
    suggestions,
    searchHistory,
    addToHistory,
    clearHistory,
    isSearching,
  } = useSearch();

  const [isFocused, setIsFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [localQuery, setLocalQuery] = useState(query);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Debounced search function
  const debouncedSearch = debounce((searchQuery: string) => {
    setQuery(searchQuery);
  }, 300);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalQuery(value);
    debouncedSearch(value);
  };

  // Handle search submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (localQuery.trim()) {
      addToHistory(localQuery.trim());
      setQuery(localQuery.trim());
      setShowDropdown(false);
      inputRef.current?.blur();
      onSearchSubmit?.(localQuery.trim());
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setLocalQuery(suggestion);
    setQuery(suggestion);
    addToHistory(suggestion);
    setShowDropdown(false);
    inputRef.current?.blur();
    onSearchSubmit?.(suggestion);
  };

  // Handle clear search
  const handleClear = () => {
    setLocalQuery('');
    setQuery('');
    setShowDropdown(false);
    inputRef.current?.focus();
  };

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    setShowDropdown(true);
  };

  // Handle blur
  const handleBlur = (e: React.FocusEvent) => {
    // Delay hiding dropdown to allow clicking on suggestions
    setTimeout(() => {
      if (!dropdownRef.current?.contains(e.relatedTarget as Node)) {
        setIsFocused(false);
        setShowDropdown(false);
      }
    }, 150);
  };

  // Close dropdown on escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowDropdown(false);
        inputRef.current?.blur();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Auto focus if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Sync local query with context query
  useEffect(() => {
    setLocalQuery(query);
  }, [query]);

  const popularTerms = getPopularSearchTerms();
  const hasContent = localQuery.length > 0;
  const hasHistory = searchHistory.length > 0;
  const hasSuggestions = suggestions.length > 0;

  return (
    <div className={cn("relative w-full max-w-2xl", className)}>
      {/* Search Input */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-sabone-cream/60" />
          <Input
            ref={inputRef}
            type="text"
            value={localQuery}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            className={cn(
              "pl-10 pr-12 h-12 text-base",
              "bg-sabone-charcoal/50 border-sabone-gold/30",
              "text-sabone-cream placeholder:text-sabone-cream/60",
              "focus:border-sabone-gold focus:ring-sabone-gold/20",
              "transition-all duration-200",
              isFocused && "ring-2 ring-sabone-gold/20"
            )}
          />
          
          {/* Clear button */}
          {hasContent && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-sabone-gold/20"
            >
              <X className="h-4 w-4 text-sabone-cream/60" />
            </Button>
          )}
          
          {/* Loading indicator */}
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin h-4 w-4 border-2 border-sabone-gold border-t-transparent rounded-full" />
            </div>
          )}
        </div>
      </form>

      {/* Search Suggestions Dropdown */}
      {showSuggestions && showDropdown && (
        <Card
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-2 z-50 bg-sabone-charcoal border-sabone-gold/30 shadow-xl"
        >
          <CardContent className="p-0 max-h-96 overflow-y-auto">
            {/* Current search suggestions */}
            {hasSuggestions && (
              <div className="p-4 border-b border-sabone-gold/20">
                <h4 className="text-sm font-medium text-sabone-cream mb-2 flex items-center">
                  <Search className="h-4 w-4 mr-2" />
                  Suggestions
                </h4>
                <div className="space-y-1">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full text-left px-3 py-2 text-sm text-sabone-cream/80 hover:bg-sabone-gold/10 rounded transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Search history */}
            {hasHistory && (
              <div className="p-4 border-b border-sabone-gold/20">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-sabone-cream flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Recent Searches
                  </h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearHistory}
                    className="text-xs text-sabone-cream/60 hover:text-sabone-cream h-auto p-1"
                  >
                    Clear
                  </Button>
                </div>
                <div className="space-y-1">
                  {searchHistory.slice(0, 5).map((historyItem, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(historyItem)}
                      className="w-full text-left px-3 py-2 text-sm text-sabone-cream/80 hover:bg-sabone-gold/10 rounded transition-colors"
                    >
                      {historyItem}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Popular searches */}
            {!hasContent && (
              <div className="p-4">
                <h4 className="text-sm font-medium text-sabone-cream mb-2 flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Popular Searches
                </h4>
                <div className="flex flex-wrap gap-2">
                  {popularTerms.map((term, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="cursor-pointer border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10 transition-colors"
                      onClick={() => handleSuggestionClick(term)}
                    >
                      {term}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* No results message */}
            {hasContent && !hasSuggestions && !hasHistory && (
              <div className="p-4 text-center text-sabone-cream/60">
                <p className="text-sm">No suggestions found</p>
                <p className="text-xs mt-1">Try searching for products, ingredients, or benefits</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SearchBar;
