<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sabone | Luxury Natural Soaps & Shampoos</title>
  <link rel="icon" type="image/png" href="/favicon.ico.png">
  <style>
    body {
      font-family: 'Montserrat', Arial, sans-serif;
      background-color: #1c1c1c;
      color: #e5dcc5;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid rgba(198, 168, 112, 0.3);
    }
    .logo {
      max-width: 150px;
    }
    .hero {
      text-align: center;
      padding: 100px 0;
      position: relative;
    }
    .hero h1 {
      font-family: 'Playfair Display', serif;
      font-size: 3.5rem;
      color: #c6a870;
      margin-bottom: 20px;
    }
    .hero p {
      font-size: 1.2rem;
      max-width: 600px;
      margin: 0 auto 30px;
    }
    .cta-button {
      background-color: transparent;
      border: 1px solid #c6a870;
      color: #c6a870;
      padding: 12px 30px;
      font-size: 1rem;
      border-radius: 30px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .cta-button:hover {
      background-color: #c6a870;
      color: #1c1c1c;
    }
    .products {
      padding: 50px 0;
    }
    .products h2 {
      font-family: 'Playfair Display', serif;
      font-size: 2.5rem;
      color: #c6a870;
      text-align: center;
      margin-bottom: 40px;
    }
    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 30px;
    }
    .product-card {
      background-color: #2a2a1f;
      border-radius: 8px;
      overflow: hidden;
      transition: transform 0.3s ease;
    }
    .product-card:hover {
      transform: translateY(-5px);
    }
    .product-image {
      height: 300px;
      background-color: #333328;
      position: relative;
    }
    .product-info {
      padding: 20px;
    }
    .product-name {
      font-family: 'Playfair Display', serif;
      font-size: 1.5rem;
      color: #c6a870;
      margin-bottom: 10px;
    }
    .product-price {
      font-size: 1.2rem;
      margin-bottom: 15px;
    }
    .product-description {
      font-size: 0.9rem;
      margin-bottom: 20px;
    }
    .add-to-cart {
      background-color: #c6a870;
      color: #1c1c1c;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
    }
    .add-to-cart:hover {
      background-color: #d8ba82;
    }
    footer {
      background-color: #2a2a1f;
      padding: 40px 0;
      margin-top: 50px;
      text-align: center;
    }
    .error-message {
      background-color: rgba(255, 107, 107, 0.2);
      border: 1px solid #ff6b6b;
      color: #ff6b6b;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }
    .success-message {
      background-color: rgba(107, 255, 107, 0.2);
      border: 1px solid #6bff6b;
      color: #6bff6b;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }
    .debug-panel {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: rgba(42, 42, 31, 0.9);
      border: 1px solid #c6a870;
      border-radius: 8px;
      padding: 15px;
      max-width: 300px;
      z-index: 1000;
    }
    .debug-panel h3 {
      color: #c6a870;
      margin-top: 0;
    }
    .debug-panel button {
      background-color: #c6a870;
      color: #1c1c1c;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 5px;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <div class="logo">
        <img src="/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png" alt="Sabone Logo" width="150">
      </div>
      <nav>
        <button class="cta-button">Shop Now</button>
      </nav>
    </header>

    <main>
      <section class="hero">
        <h1>Nature in Ritual</h1>
        <p>Artisanal soaps and shampoos rooted in Arabic purity traditions. Reconnect with nature's sacred cleansing ritual.</p>
        <button class="cta-button">Explore the Collection</button>
      </section>

      <section class="products">
        <h2>Our Products</h2>
        <div class="product-grid">
          <!-- Product cards will be dynamically generated here -->
        </div>
      </section>
    </main>

    <footer>
      <p>&copy; 2025 Sabone. All rights reserved.</p>
    </footer>
  </div>

  <div class="debug-panel">
    <h3>Debug Panel</h3>
    <div id="debug-status"></div>
    <button id="check-react">Check React</button>
    <button id="check-vite">Check Vite</button>
    <button id="clear-storage">Clear Storage</button>
    <button id="reload-page">Reload Page</button>
  </div>

  <script>
    // Simple product data
    const products = [
      {
        id: 1,
        name: 'Lemon Verbena Fresh Bar',
        price: '$12.99',
        description: 'Refreshing citrus soap bar with natural verbena extract.',
        image: '/placeholder.svg'
      },
      {
        id: 2,
        name: 'Rose Clay Glow Bar',
        price: '$14.99',
        description: 'Luxurious rose-infused clay soap for radiant skin.',
        image: '/placeholder.svg'
      },
      {
        id: 3,
        name: 'Scalp Rescue Shampoo',
        price: '$18.99',
        description: 'Soothing shampoo for sensitive and irritated scalps.',
        image: '/placeholder.svg'
      }
    ];

    // Render products
    function renderProducts() {
      const productGrid = document.querySelector('.product-grid');
      
      products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        
        productCard.innerHTML = `
          <div class="product-image">
            <img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;">
          </div>
          <div class="product-info">
            <h3 class="product-name">${product.name}</h3>
            <div class="product-price">${product.price}</div>
            <p class="product-description">${product.description}</p>
            <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
          </div>
        `;
        
        productGrid.appendChild(productCard);
      });
    }

    // Debug functions
    function checkReact() {
      const debugStatus = document.getElementById('debug-status');
      
      if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
        debugStatus.innerHTML = '<div class="success-message">React is loaded!</div>';
      } else {
        debugStatus.innerHTML = '<div class="error-message">React is not loaded.</div>';
      }
    }

    function checkVite() {
      const debugStatus = document.getElementById('debug-status');
      
      // Check if any Vite-specific globals exist
      if (typeof import.meta !== 'undefined') {
        debugStatus.innerHTML = '<div class="success-message">Vite environment detected!</div>';
      } else {
        debugStatus.innerHTML = '<div class="error-message">Vite environment not detected.</div>';
      }
    }

    function clearStorage() {
      const debugStatus = document.getElementById('debug-status');
      
      try {
        localStorage.clear();
        sessionStorage.clear();
        debugStatus.innerHTML = '<div class="success-message">Storage cleared successfully!</div>';
      } catch (e) {
        debugStatus.innerHTML = `<div class="error-message">Error clearing storage: ${e.message}</div>`;
      }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      renderProducts();
      
      // Debug panel event listeners
      document.getElementById('check-react').addEventListener('click', checkReact);
      document.getElementById('check-vite').addEventListener('click', checkVite);
      document.getElementById('clear-storage').addEventListener('click', clearStorage);
      document.getElementById('reload-page').addEventListener('click', () => window.location.reload());
      
      console.log('Sabone reset app initialized');
    });
  </script>
</body>
</html>
