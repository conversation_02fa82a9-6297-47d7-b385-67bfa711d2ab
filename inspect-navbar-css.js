// Script to inspect CSS properties of navbar-inner-container in Arabic mode
import puppeteer from 'puppeteer';

(async () => {
  console.log('Starting browser to inspect navbar CSS...');
  
  const browser = await puppeteer.launch({
    headless: false, // See the browser in action
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: null
  });
  
  try {
    const page = await browser.newPage();
    
    // Navigate to the local development server
    console.log('Navigating to localhost:8080...');
    await page.goto('http://localhost:8080', { waitUntil: 'networkidle2' });
    
    // Wait for the page to load completely
    await page.waitForSelector('.navbar-inner-container', { timeout: 5000 });
    
    console.log('Switching to Arabic mode...');
    
    // Find and click the language switcher to change to Arabic
    // This depends on how your language switcher is implemented
    // We'll try a few common selectors
    try {
      // Try to find language switcher by common selectors
      const languageSwitchers = [
        '.language-switcher', 
        '[data-testid="language-switcher"]', 
        '.lang-switch',
        'button[aria-label="Switch to Arabic"]',
        'select[id="language-selector"]'
      ];
      
      for (const selector of languageSwitchers) {
        const exists = await page.$(selector).then(res => !!res);
        if (exists) {
          console.log(`Found language switcher with selector: ${selector}`);
          
          // If it's a select element
          if (selector.includes('select')) {
            await page.select(selector, 'ar');
          } else {
            await page.click(selector);
            // If there's a dropdown, find and click the Arabic option
            try {
              await page.waitForSelector('li[data-value="ar"], option[value="ar"]', { timeout: 2000 });
              await page.click('li[data-value="ar"], option[value="ar"]');
            } catch (e) {
              // Maybe it's a direct toggle, not a dropdown
              console.log('No dropdown found, assuming direct toggle');
            }
          }
          
          // Wait for page to reload or update
          await page.waitForTimeout(2000);
          break;
        }
      }

      // If none of the above worked, look for any element containing "English" or "العربية"
      // or any button that might be a language switch
      if (!(await page.$('.navbar-inner-container[dir="rtl"]'))) {
        console.log('Trying to find language switch by text content...');
        
        // Find elements containing language names
        const arabicElementHandle = await page.evaluateHandle(() => {
          // Look for common language switch patterns
          const elements = [
            ...document.querySelectorAll('button, a, span, div'),
          ].filter(el => {
            const text = el.textContent.toLowerCase();
            return text.includes('english') || 
                   text.includes('arabic') || 
                   text.includes('العربية') ||
                   text.includes('ar') || 
                   text.includes('en') ||
                   text.includes('language');
          });
          return elements[0]; // Return first match
        });
        
        if (arabicElementHandle) {
          console.log('Found potential language switch element by text content');
          await arabicElementHandle.click();
          await page.waitForTimeout(2000);
        }
      }
    } catch (e) {
      console.log('Error finding/clicking language switcher:', e.message);
    }
    
    // Check if the navbar is now RTL
    const isRtl = await page.evaluate(() => {
      const navbar = document.querySelector('.navbar-inner-container');
      if (navbar) {
        return navbar.dir === 'rtl' || 
               window.getComputedStyle(navbar).direction === 'rtl' || 
               document.dir === 'rtl';
      }
      return false;
    });
    
    console.log(`Page is in RTL mode: ${isRtl ? 'Yes' : 'No'}`);
    
    // Get the CSS properties for the navbar-inner-container
    const cssProperties = await page.evaluate(() => {
      const navbar = document.querySelector('.navbar-inner-container');
      if (!navbar) return { error: 'Navbar element not found' };
      
      const computedStyle = window.getComputedStyle(navbar);
      
      // Get all CSS properties
      return {
        marginLeft: computedStyle.marginLeft,
        marginRight: computedStyle.marginRight,
        direction: computedStyle.direction,
        textAlign: computedStyle.textAlign,
        // Get additional useful properties
        padding: computedStyle.padding,
        display: computedStyle.display,
        position: computedStyle.position,
        // Get all margin and padding properties
        margin: computedStyle.margin,
        marginTop: computedStyle.marginTop,
        marginBottom: computedStyle.marginBottom,
        paddingLeft: computedStyle.paddingLeft,
        paddingRight: computedStyle.paddingRight,
        // Get applied CSS rules in order
        cssRules: Array.from(document.styleSheets)
          .filter(sheet => {
            try {
              return sheet.cssRules; // This will throw if it's a cross-origin stylesheet
            } catch (e) {
              return false;
            }
          })
          .flatMap(sheet => Array.from(sheet.cssRules || []))
          .filter(rule => {
            if (rule.selectorText && 
                rule.selectorText.includes('navbar-inner-container') && 
                (rule.style.marginLeft || rule.style.marginRight || rule.style.margin)) {
              return true;
            }
            return false;
          })
          .map(rule => ({
            selector: rule.selectorText,
            marginLeft: rule.style.marginLeft,
            marginRight: rule.style.marginRight,
            margin: rule.style.margin
          }))
      };
    });
    
    console.log('\n--- Navbar Inner Container CSS Properties ---');
    console.log(JSON.stringify(cssProperties, null, 2));
    
    // Take a screenshot for reference
    await page.screenshot({ path: 'navbar-arabic-mode.png' });
    console.log('Screenshot saved as navbar-arabic-mode.png');
    
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    // Keep the browser open for manual inspection
    console.log('\nKeeping browser open for manual inspection.');
    console.log('Press Ctrl+C to close the script when done.');
    // Uncomment this if you want the browser to close automatically:
    // await browser.close();
  }
})(); 