import { useState, useEffect } from "react";
import { useProducts } from "@/contexts/ProductContext";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Upload,
  Star,
  Image as ImageIcon,
  Trash2
} from "lucide-react";
import ConfirmationDialog from "./ConfirmationDialog";

interface ProductImageManagerProps {
  productId: string;
  currentImage: string;
  onImageChange: (imagePath: string) => void;
}

const ProductImageManager = ({
  productId: _productId,
  currentImage,
  onImageChange
}: ProductImageManagerProps) => {
  const { uploadProductImage, deleteProductImage } = useProducts();
  const [images, setImages] = useState<string[]>([currentImage]);
  const [selectedImage, setSelectedImage] = useState<string>(currentImage);
  const [isUploading, setIsUploading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<string | null>(null);
  
  // Set the primary image when selected image changes
  useEffect(() => {
    if (selectedImage && selectedImage !== currentImage) {
      onImageChange(selectedImage);
    }
  }, [selectedImage, currentImage, onImageChange]);
  
  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    setIsUploading(true);
    
    try {
      // Upload each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const imagePath = await uploadProductImage(file);
        
        // Add to images array if not already present
        setImages(prev => {
          if (!prev.includes(imagePath)) {
            return [...prev, imagePath];
          }
          return prev;
        });
        
        // If this is the first image, set it as selected
        if (i === 0 && images.length === 0) {
          setSelectedImage(imagePath);
          onImageChange(imagePath);
        }
      }
      
      toast.success(`${files.length > 1 ? 'Images' : 'Image'} uploaded successfully`);
    } catch (error) {
      console.error("Error uploading images:", error);
      toast.error("Failed to upload images");
    } finally {
      setIsUploading(false);
    }
  };
  
  // Handle image deletion
  const handleDeleteImage = async () => {
    if (!imageToDelete) return;
    
    try {
      // Don't allow deleting the last image
      if (images.length <= 1) {
        toast.error("Cannot delete the only product image");
        return;
      }
      
      const success = await deleteProductImage(imageToDelete);
      
      if (success) {
        // Remove from images array
        setImages(prev => prev.filter(img => img !== imageToDelete));
        
        // If the deleted image was selected, select another one
        if (selectedImage === imageToDelete) {
          const newSelected = images.find(img => img !== imageToDelete) || "";
          setSelectedImage(newSelected);
          onImageChange(newSelected);
        }
        
        toast.success("Image deleted successfully");
      }
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image");
    } finally {
      setShowDeleteDialog(false);
      setImageToDelete(null);
    }
  };
  
  // Set an image as primary
  const setPrimaryImage = (imagePath: string) => {
    setSelectedImage(imagePath);
    onImageChange(imagePath);
    toast.success("Primary image updated");
  };
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sabone-gold font-medium">Product Images</h3>
        <div>
          <label className="cursor-pointer">
            <Button
              type="button"
              variant="outline"
              className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              disabled={isUploading}
            >
              {isUploading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Uploading...
                </span>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Images
                </>
              )}
            </Button>
            <input
              type="file"
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              disabled={isUploading}
            />
          </label>
        </div>
      </div>
      
      {images.length === 0 ? (
        <div className="border-2 border-dashed border-sabone-gold/30 rounded-md p-8 flex flex-col items-center justify-center bg-sabone-charcoal/50">
          <ImageIcon className="h-12 w-12 text-sabone-gold/50 mb-2" />
          <p className="text-sabone-cream font-medium">No images available</p>
          <p className="text-sabone-cream/50 text-sm mt-1">Upload images to display here</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {images.map((imagePath, index) => (
            <div 
              key={index} 
              className={`
                relative border rounded-md overflow-hidden h-32
                ${selectedImage === imagePath 
                  ? 'border-sabone-gold ring-2 ring-sabone-gold/30' 
                  : 'border-sabone-gold/20 hover:border-sabone-gold/50'
                }
                transition-all duration-200
              `}
            >
              <img
                src={imagePath}
                alt={`Product ${index + 1}`}
                className="w-full h-full object-cover"
              />
              
              {/* Primary indicator */}
              {selectedImage === imagePath && (
                <div className="absolute top-2 left-2 bg-sabone-gold text-sabone-charcoal rounded-full p-1">
                  <Star className="h-4 w-4" />
                </div>
              )}
              
              {/* Actions */}
              <div className="absolute bottom-0 left-0 right-0 bg-sabone-charcoal/80 backdrop-blur-sm p-1 flex justify-between">
                {selectedImage !== imagePath && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={() => setPrimaryImage(imagePath)}
                    title="Set as primary"
                  >
                    <Star className="h-3.5 w-3.5" />
                  </Button>
                )}
                
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-red-500 hover:bg-red-500/10 ml-auto"
                  onClick={() => {
                    setImageToDelete(imagePath);
                    setShowDeleteDialog(true);
                  }}
                  title="Delete image"
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Image"
        description="Are you sure you want to delete this image? This action cannot be undone."
        confirmText="Delete"
        variant="destructive"
        onConfirm={handleDeleteImage}
      >
        {imageToDelete && (
          <div className="flex justify-center">
            <img
              src={imageToDelete}
              alt="Image to delete"
              className="max-h-40 rounded-md"
            />
          </div>
        )}
      </ConfirmationDialog>
    </div>
  );
};

export default ProductImageManager;
