// Script to check browser console for errors
import puppeteer from 'puppeteer';

(async () => {
  console.log('Starting browser to check for console errors...');
  
  const browser = await puppeteer.launch({
    headless: false, // Set to true for headless mode
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: null
  });
  
  try {
    const page = await browser.newPage();
    
    // Collect console messages
    const consoleMessages = [];
    page.on('console', (msg) => {
      const text = msg.text();
      const type = msg.type();
      consoleMessages.push({ type, text });
      console.log(`Browser console [${type}]: ${text}`);
    });
    
    // Collect errors
    page.on('pageerror', (error) => {
      console.error('Page error:', error.message);
    });
    
    // Navigate to the local development server
    console.log('Navigating to localhost:8080...');
    await page.goto('http://localhost:8080', { waitUntil: 'networkidle2' });
    
    console.log('Waiting for 5 seconds to capture any delayed console messages...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check specifically for MCP-related messages or errors
    const mcpMessages = consoleMessages.filter(msg => 
      msg.text.toLowerCase().includes('mcp') || 
      msg.text.toLowerCase().includes('browsermcp')
    );
    
    console.log('\n--- MCP-Related Console Messages ---');
    if (mcpMessages.length > 0) {
      mcpMessages.forEach(msg => console.log(`[${msg.type}]: ${msg.text}`));
    } else {
      console.log('No MCP-related messages found in the console.');
    }
    
    console.log('\n--- Console Errors ---');
    const errors = consoleMessages.filter(msg => msg.type === 'error');
    if (errors.length > 0) {
      errors.forEach(msg => console.log(`Error: ${msg.text}`));
    } else {
      console.log('No errors found in the console.');
    }
    
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    console.log('Closing browser...');
    await browser.close();
  }
})(); 