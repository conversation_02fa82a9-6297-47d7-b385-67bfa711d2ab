import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import {
  EmailPreferences,
  EmailType,
  OrderEmailData,
  WelcomeEmailData,
  PasswordResetEmailData,
  AccountUpdateEmailData,
  LowStockAlertData,
  NewOrderAdminData,
  SalesSummaryData,
  NewsletterData,
  PromotionalEmailData,
  ReceiptEmailData // Added missing import
} from '@/types/email';
import {
  getUserEmailPreferences,
  saveUserEmailPreferences,
  createDefaultEmailPreferences,
  sendOrderConfirmationEmail,
  sendOrderShippedEmail,
  sendOrderDeliveredEmail,
  sendOrderCancelledEmail,
  sendOrderReceiptEmail,
  sendWelcomeEmail,
  sendPasswordResetEmail,
  sendAccountUpdateEmail,
  sendLowStockAlertEmail,
  sendNewOrderAdminEmail,
  sendSalesSummaryEmail,
  sendNewsletterEmail,
  sendPromotionalEmail,
  previewEmail
} from '@/services/emailService';
import { generateOrderConfirmationEmail } from '@/templates/emailTemplates';
import { Order } from '@/types/order';
import { InventoryItem } from '@/types/inventory';

interface EmailContextType {
  emailPreferences: EmailPreferences | null;
  updateEmailPreferences: (preferences: Partial<EmailPreferences>) => void;
  sendOrderConfirmation: (order: Order, customerName: string, estimatedDelivery?: string) => Promise<boolean>;
  sendOrderShipped: (order: Order, customerName: string, trackingNumber?: string) => Promise<boolean>;
  sendOrderDelivered: (order: Order, customerName: string) => Promise<boolean>;
  sendOrderCancelled: (order: Order, customerName: string, cancellationReason?: string) => Promise<boolean>;
  sendOrderReceipt: (order: Order, customerName: string, receiptUrl?: string) => Promise<boolean>;
  sendWelcome: (userName: string, email: string, verificationLink?: string) => Promise<boolean>;
  sendPasswordReset: (userName: string, email: string, resetLink: string) => Promise<boolean>;
  sendAccountUpdate: (userName: string, email: string, userId: string, updateType: 'profile' | 'password' | 'address' | 'payment_method', updateDetails?: string) => Promise<boolean>;
  sendLowStockAlert: (items: InventoryItem[], productNames: string[]) => Promise<boolean>;
  sendNewOrderAdmin: (order: Order, customerName: string, customerEmail: string) => Promise<boolean>;
  sendSalesSummary: (period: 'daily' | 'weekly' | 'monthly', totalSales: number, orderCount: number, topProducts: { name: string; quantity: number; revenue: number; }[], date: string) => Promise<boolean>;
  sendNewsletter: (subject: string, content: string, subscribers: string[], featuredProducts?: { name: string; image: string; price: number; url: string; }[]) => Promise<boolean>;
  sendPromotional: (subject: string, promotionTitle: string, promotionDescription: string, subscribers: string[], discountCode?: string, expiryDate?: string, featuredProducts?: { name: string; image: string; price: number; discountedPrice?: number; url: string; }[]) => Promise<boolean>;
  previewOrderConfirmation: (order: Order, customerName: string, estimatedDelivery?: string) => void;
  isEmailEnabled: (emailType: EmailType) => boolean;
}

const EmailContext = createContext<EmailContextType | undefined>(undefined);

export const EmailProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [emailPreferences, setEmailPreferences] = useState<EmailPreferences | null>(null);

  // Load email preferences when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user?.sub) {
      const preferences = getUserEmailPreferences(user.sub) || createDefaultEmailPreferences(user.sub);
      setEmailPreferences(preferences);
    } else {
      setEmailPreferences(null);
    }
  }, [isAuthenticated, user?.sub]);

  // Update email preferences
  const updateEmailPreferences = (preferences: Partial<EmailPreferences>) => {
    if (!isAuthenticated || !user?.sub || !emailPreferences) {
      toast.error('You must be logged in to update email preferences');
      return;
    }

    const updatedPreferences: EmailPreferences = {
      ...emailPreferences,
      ...preferences,
      lastUpdated: new Date().toISOString()
    };

    const success = saveUserEmailPreferences(updatedPreferences);
    if (success) {
      setEmailPreferences(updatedPreferences);
      toast.success('Email preferences updated');
    } else {
      toast.error('Failed to update email preferences');
    }
  };

  // Check if email type is enabled for the current user
  const isEmailEnabled = (emailType: EmailType): boolean => {
    if (!isAuthenticated || !user?.sub || !emailPreferences) return false;

    switch (emailType) {
      case 'order_confirmation':
      case 'order_shipped':
      case 'order_delivered':
      case 'order_cancelled':
        return emailPreferences.orderNotifications;

      case 'welcome':
      case 'password_reset':
      case 'account_update':
        return emailPreferences.accountNotifications;

      case 'newsletter':
      case 'promotional':
        return emailPreferences.marketingEmails;

      // Admin notifications are always enabled
      case 'low_stock_alert':
      case 'new_order_admin':
      case 'sales_summary':
        return true;

      default:
        return true;
    }
  };

  // Send order confirmation email
  const sendOrderConfirmation = async (order: Order, customerName: string, estimatedDelivery?: string): Promise<boolean> => {
    const data: OrderEmailData = {
      order,
      customerName,
      customerEmail: user?.email || '', // Added customerEmail
      estimatedDelivery
    };

    return await sendOrderConfirmationEmail(data);
  };

  // Send order shipped email
  const sendOrderShipped = async (order: Order, customerName: string, trackingNumber?: string): Promise<boolean> => {
    const data: OrderEmailData = {
      order,
      customerName,
      customerEmail: user?.email || '', // Added customerEmail
      trackingNumber
    };

    return await sendOrderShippedEmail(data);
  };

  // Send order delivered email
  const sendOrderDelivered = async (order: Order, customerName: string): Promise<boolean> => {
    const data: OrderEmailData = {
      order,
      customerName,
      customerEmail: user?.email || '', // Added customerEmail
    };

    return await sendOrderDeliveredEmail(data);
  };

  // Send order cancelled email
  const sendOrderCancelled = async (order: Order, customerName: string, cancellationReason?: string): Promise<boolean> => {
    const data: OrderEmailData = {
      order,
      customerName,
      customerEmail: user?.email || '', // Added customerEmail
      cancellationReason
    };

    return await sendOrderCancelledEmail(data);
  };

  // Send order receipt email
  const sendOrderReceipt = async (order: Order, customerName: string, receiptUrl?: string): Promise<boolean> => {
    const data: ReceiptEmailData = {
      order,
      customerName,
      customerEmail: user?.email || '', // Added customerEmail
      receiptUrl
    };

    return await sendOrderReceiptEmail(data);
  };

  // Send welcome email
  const sendWelcome = async (userName: string, email: string, verificationLink?: string): Promise<boolean> => {
    const data: WelcomeEmailData = {
      userName,
      verificationLink
    };

    return await sendWelcomeEmail(data, email);
  };

  // Send password reset email
  const sendPasswordReset = async (userName: string, email: string, resetLink: string): Promise<boolean> => {
    const data: PasswordResetEmailData = {
      userName,
      resetLink
    };

    return await sendPasswordResetEmail(data, email);
  };

  // Send account update email
  const sendAccountUpdate = async (
    userName: string,
    email: string,
    userId: string,
    updateType: 'profile' | 'password' | 'address' | 'payment_method',
    updateDetails?: string
  ): Promise<boolean> => {
    const data: AccountUpdateEmailData = {
      userName,
      userId,
      updateType,
      updateDetails
    };

    return await sendAccountUpdateEmail(data, email);
  };

  // Send low stock alert email
  const sendLowStockAlert = async (items: InventoryItem[], productNames: string[]): Promise<boolean> => {
    const data: LowStockAlertData = {
      items,
      productNames
    };

    return await sendLowStockAlertEmail(data);
  };

  // Send new order admin email
  const sendNewOrderAdmin = async (order: Order, customerName: string, customerEmail: string): Promise<boolean> => {
    const data: NewOrderAdminData = {
      order,
      customerName,
      customerEmail
    };

    return await sendNewOrderAdminEmail(data);
  };

  // Send sales summary email
  const sendSalesSummary = async (
    period: 'daily' | 'weekly' | 'monthly',
    totalSales: number,
    orderCount: number,
    topProducts: { name: string; quantity: number; revenue: number; }[],
    date: string
  ): Promise<boolean> => {
    const data: SalesSummaryData = {
      period,
      totalSales,
      orderCount,
      topProducts,
      date
    };

    return await sendSalesSummaryEmail(data);
  };

  // Send newsletter email
  const sendNewsletter = async (
    subject: string,
    content: string,
    subscribers: string[],
    featuredProducts?: { name: string; image: string; price: number; url: string; }[]
  ): Promise<boolean> => {
    const data: NewsletterData = {
      subject,
      content,
      featuredProducts
    };

    return await sendNewsletterEmail(data, subscribers);
  };

  // Send promotional email
  const sendPromotional = async (
    subject: string,
    promotionTitle: string,
    promotionDescription: string,
    subscribers: string[],
    discountCode?: string,
    expiryDate?: string,
    featuredProducts?: { name: string; image: string; price: number; discountedPrice?: number; url: string; }[]
  ): Promise<boolean> => {
    const data: PromotionalEmailData = {
      subject,
      recipientName: '',
      promotionTitle,
      promotionDescription,
      discountCode,
      expiryDate,
      featuredProducts
    };

    return await sendPromotionalEmail(data, subscribers);
  };

  // Preview order confirmation email (for development)
  const previewOrderConfirmation = (order: Order, customerName: string, estimatedDelivery?: string): void => {
    const data: OrderEmailData = {
      order,
      customerName,
      customerEmail: user?.email || '<EMAIL>', // Ensure customerEmail is present
      estimatedDelivery
    };

    const { subject, html } = generateOrderConfirmationEmail(data);

    // Use authenticated user's email for preview, or a placeholder if not available
    const recipientEmail = user?.email || '<EMAIL>';

    previewEmail({
      to: { email: recipientEmail, name: customerName },
      from: { email: '<EMAIL>', name: 'Sabone Store' },
      subject,
      html
    });
  };

  return (
    <EmailContext.Provider
      value={{
        emailPreferences,
        updateEmailPreferences,
        sendOrderConfirmation,
        sendOrderShipped,
        sendOrderDelivered,
        sendOrderCancelled,
        sendOrderReceipt,
        sendWelcome,
        sendPasswordReset,
        sendAccountUpdate,
        sendLowStockAlert,
        sendNewOrderAdmin,
        sendSalesSummary,
        sendNewsletter,
        sendPromotional,
        previewOrderConfirmation,
        isEmailEnabled
      }}
    >
      {children}
    </EmailContext.Provider>
  );
};

export const useEmail = () => {
  const context = useContext(EmailContext);
  if (context === undefined) {
    throw new Error('useEmail must be used within an EmailProvider');
  }
  return context;
};
