import { Order } from './order';
import { InventoryItem } from './inventory';

export type EmailType =
  | 'order_confirmation'
  | 'order_shipped'
  | 'order_delivered'
  | 'order_cancelled'
  | 'order_receipt'
  | 'welcome'
  | 'password_reset'
  | 'account_update'
  | 'low_stock_alert'
  | 'new_order_admin'
  | 'sales_summary'
  | 'newsletter'
  | 'promotional';

export type EmailTemplate = {
  subject: string;
  body: string;
};

export interface EmailRecipient {
  email: string;
  name?: string;
}

export interface EmailAttachment {
  content: string;
  filename: string;
  type?: string;
  disposition?: 'attachment' | 'inline';
}

export interface EmailOptions {
  to: EmailRecipient | EmailRecipient[];
  from: EmailRecipient;
  subject: string;
  text?: string;
  html: string;
  attachments?: EmailAttachment[];
  cc?: EmailRecipient | EmailRecipient[];
  bcc?: EmailRecipient | EmailRecipient[];
  replyTo?: EmailRecipient;
}

export interface OrderEmailData {
  order: Order;
  customerName: string;
  customerEmail: string; // Added customerEmail
  trackingNumber?: string;
  estimatedDelivery?: string;
  cancellationReason?: string;
}

export interface WelcomeEmailData {
  userName: string;
  verificationLink?: string;
}

export interface PasswordResetEmailData {
  userName: string;
  resetLink: string;
}

export interface AccountUpdateEmailData {
  userName: string;
  userId?: string; // Added optional userId
  updateType: 'profile' | 'password' | 'address' | 'payment_method';
  updateDetails?: string;
}

export interface LowStockAlertData {
  items: InventoryItem[];
  productNames: string[];
}

export interface NewOrderAdminData {
  order: Order;
  customerName: string;
  customerEmail: string;
}

export interface SalesSummaryData {
  period: 'daily' | 'weekly' | 'monthly';
  totalSales: number;
  orderCount: number;
  topProducts: {
    name: string;
    quantity: number;
    revenue: number;
  }[];
  date: string;
}

export interface NewsletterData {
  recipientName?: string;
  subject: string;
  content: string;
  featuredProducts?: {
    name: string;
    image: string;
    price: number;
    url: string;
  }[];
}

export interface PromotionalEmailData {
  recipientName?: string;
  subject: string;
  promotionTitle: string;
  promotionDescription: string;
  discountCode?: string;
  expiryDate?: string;
  featuredProducts?: {
    name: string;
    image: string;
    price: number;
    discountedPrice?: number;
    url: string;
  }[];
}

export interface ReceiptEmailData {
  order: Order;
  customerName: string;
  customerEmail: string; // Added customerEmail
  receiptUrl?: string;
}

export interface EmailPreferences {
  userId: string;
  orderNotifications: boolean;
  accountNotifications: boolean;
  marketingEmails: boolean;
  lastUpdated: string;
}
