import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Product } from "@/data/products";
import { useProducts } from "@/contexts/ProductContext";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { X, Plus, Image as ImageIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import ProductImageManager from "./ProductImageManager";

// Form validation schema
const productFormSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  fullDescription: z.string().min(20, "Full description must be at least 20 characters"),
  price: z.coerce.number().positive("Price must be positive"),
  type: z.enum(["bar", "liquid"], {
    required_error: "Please select a product type",
  }),
  ingredients: z.array(z.string()).min(1, "At least one ingredient is required"),
  benefits: z.array(z.string()).min(1, "At least one benefit is required"),
  skinType: z.string().optional(),
  application: z.string().optional(),
  storage: z.string().optional(),
  image: z.string().min(1, "Product image is required"),
});

type ProductFormValues = z.infer<typeof productFormSchema>;

interface ProductFormProps {
  product?: Product;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ProductForm = ({ product, onSuccess, onCancel }: ProductFormProps) => {
  const { createProduct, updateProduct, uploadProductImage } = useProducts();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [ingredients, setIngredients] = useState<string[]>(product?.ingredients || []);
  const [benefits, setBenefits] = useState<string[]>(product?.benefits || []);
  const [newIngredient, setNewIngredient] = useState("");
  const [newBenefit, setNewBenefit] = useState("");
  const [imagePreview, setImagePreview] = useState<string | null>(product?.image || null);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const isEditMode = !!product;

  // Initialize form with product data if in edit mode
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: product?.name || "",
      description: product?.description || "",
      fullDescription: product?.fullDescription || "",
      price: product?.price || 0,
      type: product?.type || "bar",
      ingredients: product?.ingredients || [],
      benefits: product?.benefits || [],
      skinType: product?.skinType || "",
      application: product?.application || "",
      storage: product?.storage || "",
      image: product?.image || "",
    },
  });

  // Update form values when ingredients or benefits change
  useEffect(() => {
    form.setValue("ingredients", ingredients);
  }, [ingredients, form]);

  useEffect(() => {
    form.setValue("benefits", benefits);
  }, [benefits, form]);

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    setImageFile(file);

    // Create a preview
    const reader = new FileReader();
    reader.onload = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Add a new ingredient
  const addIngredient = () => {
    if (!newIngredient.trim()) return;
    setIngredients([...ingredients, newIngredient.trim()]);
    setNewIngredient("");
  };

  // Remove an ingredient
  const removeIngredient = (index: number) => {
    const updated = [...ingredients];
    updated.splice(index, 1);
    setIngredients(updated);
  };

  // Add a new benefit
  const addBenefit = () => {
    if (!newBenefit.trim()) return;
    setBenefits([...benefits, newBenefit.trim()]);
    setNewBenefit("");
  };

  // Remove a benefit
  const removeBenefit = (index: number) => {
    const updated = [...benefits];
    updated.splice(index, 1);
    setBenefits(updated);
  };

  // Form submission handler
  const onSubmit = async (data: ProductFormValues) => {
    setIsSubmitting(true);

    try {
      // Upload image if a new one was selected
      let imagePath = data.image;
      if (imageFile) {
        imagePath = await uploadProductImage(imageFile);
        data.image = imagePath;
      }

      if (isEditMode && product) {
        // Update existing product
        await updateProduct(product.id, data);
      } else {
        // Create new product
        await createProduct(data);
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error saving product:", error);
      toast.error("Failed to save product. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Basic info */}
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">Product Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="e.g., Lemon Verbena Fresh Bar"
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">Short Description</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Brief product description (displayed in product cards)"
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream resize-none h-20"
                    />
                  </FormControl>
                  <FormDescription className="text-sabone-cream/50">
                    Keep it concise (50-100 characters)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Price ($)</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Product Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
                        <SelectItem value="bar" className="text-sabone-cream">Soap Bar</SelectItem>
                        <SelectItem value="liquid" className="text-sabone-cream">Liquid Shampoo</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Right column - Image */}
          <div>
            {isEditMode && product ? (
              <FormField
                control={form.control}
                name="image"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Product Image</FormLabel>
                    <FormControl>
                      <ProductImageManager
                        productId={product.id}
                        currentImage={field.value}
                        onImageChange={(imagePath) => {
                          field.onChange(imagePath);
                          setImagePreview(imagePath);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <>
                <FormLabel className="text-sabone-cream block mb-2">Product Image</FormLabel>
                <div className="border-2 border-dashed border-sabone-gold/30 rounded-md p-4 h-[250px] flex flex-col items-center justify-center bg-sabone-charcoal/50">
                  {imagePreview ? (
                    <div className="relative w-full h-full flex items-center justify-center">
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        className="max-h-full max-w-full object-contain"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 h-8 w-8"
                        onClick={() => {
                          setImagePreview(null);
                          setImageFile(null);
                          form.setValue("image", "");
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <label className="cursor-pointer flex flex-col items-center justify-center w-full h-full">
                      <div className="flex flex-col items-center justify-center">
                        <ImageIcon className="h-12 w-12 text-sabone-gold/50 mb-2" />
                        <p className="text-sabone-cream font-medium">Click to upload image</p>
                        <p className="text-sabone-cream/50 text-sm mt-1">PNG, JPG or WEBP</p>
                      </div>
                      <input
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={handleImageUpload}
                      />
                    </label>
                  )}
                </div>
                <FormField
                  control={form.control}
                  name="image"
                  render={() => (
                    <FormMessage />
                  )}
                />
              </>
            )}
          </div>
        </div>

        <Separator className="bg-sabone-gold/20" />

        {/* Full description */}
        <FormField
          control={form.control}
          name="fullDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sabone-cream">Full Description</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Detailed product description (displayed on product detail page)"
                  className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream resize-none h-32"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Ingredients */}
          <div>
            <FormLabel className="text-sabone-cream">Ingredients</FormLabel>
            <div className="flex mt-2 mb-4">
              <Input
                value={newIngredient}
                onChange={(e) => setNewIngredient(e.target.value)}
                placeholder="Add ingredient"
                className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream mr-2"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addIngredient();
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                onClick={addIngredient}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 min-h-[100px] max-h-[200px] overflow-y-auto p-2 bg-sabone-charcoal/30 rounded-md">
              {ingredients.length === 0 ? (
                <p className="text-sabone-cream/50 text-sm w-full text-center py-4">
                  No ingredients added yet
                </p>
              ) : (
                ingredients.map((ingredient, index) => (
                  <Badge
                    key={index}
                    className="bg-sabone-dark-olive text-sabone-cream border-sabone-gold/20 flex items-center gap-1 py-1.5"
                  >
                    {ingredient}
                    <button
                      type="button"
                      onClick={() => removeIngredient(index)}
                      className="ml-1 text-sabone-cream/70 hover:text-sabone-cream"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              )}
            </div>
            <FormField
              control={form.control}
              name="ingredients"
              render={() => (
                <FormMessage />
              )}
            />
          </div>

          {/* Benefits */}
          <div>
            <FormLabel className="text-sabone-cream">Benefits</FormLabel>
            <div className="flex mt-2 mb-4">
              <Input
                value={newBenefit}
                onChange={(e) => setNewBenefit(e.target.value)}
                placeholder="Add benefit"
                className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream mr-2"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addBenefit();
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                onClick={addBenefit}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 min-h-[100px] max-h-[200px] overflow-y-auto p-2 bg-sabone-charcoal/30 rounded-md">
              {benefits.length === 0 ? (
                <p className="text-sabone-cream/50 text-sm w-full text-center py-4">
                  No benefits added yet
                </p>
              ) : (
                benefits.map((benefit, index) => (
                  <Badge
                    key={index}
                    className="bg-sabone-dark-olive text-sabone-cream border-sabone-gold/20 flex items-center gap-1 py-1.5"
                  >
                    {benefit}
                    <button
                      type="button"
                      onClick={() => removeBenefit(index)}
                      className="ml-1 text-sabone-cream/70 hover:text-sabone-cream"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              )}
            </div>
            <FormField
              control={form.control}
              name="benefits"
              render={() => (
                <FormMessage />
              )}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Additional fields */}
          <FormField
            control={form.control}
            name="skinType"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Skin/Hair Type</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="e.g., All skin types"
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="application"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Application</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="How to use the product"
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="storage"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Storage</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Storage instructions"
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2 pt-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-dark-olive/50"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-sabone-charcoal" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <span>{isEditMode ? "Update Product" : "Create Product"}</span>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ProductForm;
