import { useCart } from "@/contexts/CartContext";
import { Button } from "@/components/ui/button";
import { Trash2, Minus, Plus, X } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useState, useRef } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

const CartSummary = () => {
  const { items, removeItem, updateQuantity } = useCart();
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});
  const [swipeState, setSwipeState] = useState<Record<string, { swiping: boolean, offset: number }>>({});
  const touchStartXRef = useRef<Record<string, number>>({});
  const isMobile = useIsMobile();

  const handleImageLoad = (productId: string) => {
    setLoadingImages(prev => ({ ...prev, [productId]: false }));
  };

  const handleImageError = (productId: string) => {
    setLoadingImages(prev => ({ ...prev, [productId]: false }));
  };

  // Mobile swipe handlers for cart items
  const handleTouchStart = (productId: string, e: React.TouchEvent) => {
    if (!isMobile) return;
    touchStartXRef.current[productId] = e.touches[0].clientX;
  };

  const handleTouchMove = (productId: string, e: React.TouchEvent) => {
    if (!isMobile) return;
    if (!touchStartXRef.current[productId]) return;

    const touchX = e.touches[0].clientX;
    const startX = touchStartXRef.current[productId];
    const diff = touchX - startX;

    // Only allow swiping left (negative diff) up to -80px
    if (diff < 0 && diff > -80) {
      setSwipeState(prev => ({
        ...prev,
        [productId]: { swiping: true, offset: diff }
      }));
    }
  };

  const handleTouchEnd = (productId: string) => {
    if (!isMobile) return;
    const state = swipeState[productId];

    // If swiped more than 40px to the left, show delete button
    if (state && state.offset < -40) {
      setSwipeState(prev => ({
        ...prev,
        [productId]: { swiping: true, offset: -80 }
      }));

      // Add haptic feedback if supported
      if (navigator.vibrate) {
        navigator.vibrate(10); // Short vibration for 10ms
      }
    } else {
      // Reset swipe state
      setSwipeState(prev => ({
        ...prev,
        [productId]: { swiping: false, offset: 0 }
      }));
    }

    delete touchStartXRef.current[productId];
  };

  return (
    <div className="space-y-4">
      {items.length === 0 ? (
        <p className="text-sabone-cream/80 py-4">Your cart is empty.</p>
      ) : (
        <>
          <div className="hidden md:grid md:grid-cols-12 text-sm text-sabone-cream/60 pb-2">
            <div className="md:col-span-6">Product</div>
            <div className="md:col-span-2 text-center">Quantity</div>
            <div className="md:col-span-2 text-center">Price</div>
            <div className="md:col-span-2 text-right">Subtotal</div>
          </div>

          <Separator className="bg-sabone-gold/20" />

          <ul className="divide-y divide-sabone-gold/20">
            {items.map((item) => {
              // Get swipe state for this item or default values
              const swipe = swipeState[item.product.id] || { swiping: false, offset: 0 };

              return (
                <li
                  key={item.product.id}
                  className="py-4 relative overflow-hidden"
                  onTouchStart={(e) => handleTouchStart(item.product.id, e)}
                  onTouchMove={(e) => handleTouchMove(item.product.id, e)}
                  onTouchEnd={() => handleTouchEnd(item.product.id)}
                >
                  {/* Delete button that appears when swiped on mobile */}
                  {isMobile && (
                    <div
                      className="absolute right-0 top-0 bottom-0 flex items-center justify-center bg-red-600 w-20 h-full"
                      onClick={() => removeItem(item.product.id)}
                      role="button"
                      tabIndex={0}
                      aria-label={`Remove ${item.product.name} from cart`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          removeItem(item.product.id);
                        }
                      }}
                    >
                      <div className="flex flex-col items-center">
                        <X className="h-6 w-6 text-white" />
                        <span className="text-white text-xs mt-1">Remove</span>
                      </div>
                    </div>
                  )}

                  <div
                    className="md:grid md:grid-cols-12 gap-4 flex flex-col relative bg-sabone-dark-olive/40"
                    style={isMobile ? { transform: `translateX(${swipe.offset}px)`, transition: swipe.swiping ? 'none' : 'transform 0.3s ease' } : {}}
                  >
                    {/* Product */}
                    <div className="md:col-span-6 flex space-x-4">
                      <div className="relative w-20 h-20 flex-shrink-0 rounded overflow-hidden bg-sabone-dark-olive/60">
                        {loadingImages[item.product.id] !== false && (
                          <Skeleton className="absolute inset-0 bg-sabone-dark-olive" />
                        )}
                        <img
                          src={item.product.image}
                          alt={item.product.name}
                          className="w-full h-full object-cover"
                          onLoad={() => handleImageLoad(item.product.id)}
                          onError={() => handleImageError(item.product.id)}
                        />
                      </div>
                      <div className="flex flex-col justify-center">
                        <h3 className="text-sabone-gold font-medium">{item.product.name}</h3>
                        <p className="text-sm text-sabone-cream/70 line-clamp-1">{item.product.type}</p>
                      </div>
                    </div>

                    {/* Quantity */}
                    <div className="md:col-span-2 flex items-center justify-between md:justify-center mt-4 md:mt-0">
                      <span className="md:hidden text-sabone-cream/70">Quantity:</span>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-10 w-10 md:h-8 md:w-8 rounded-full bg-sabone-dark-olive border-sabone-gold/30 touch-feedback"
                          onClick={() => {
                            updateQuantity(item.product.id, Math.max(1, item.quantity - 1));
                            // Add haptic feedback if supported
                            if (navigator.vibrate) {
                              navigator.vibrate(5);
                            }
                          }}
                          aria-label="Decrease quantity"
                        >
                          <Minus className="h-3 w-3 text-sabone-gold" />
                        </Button>
                        <span className="w-8 text-center text-sabone-cream font-medium">{item.quantity}</span>
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-10 w-10 md:h-8 md:w-8 rounded-full bg-sabone-dark-olive border-sabone-gold/30 touch-feedback"
                          onClick={() => {
                            updateQuantity(item.product.id, item.quantity + 1);
                            // Add haptic feedback if supported
                            if (navigator.vibrate) {
                              navigator.vibrate(5);
                            }
                          }}
                          aria-label="Increase quantity"
                        >
                          <Plus className="h-3 w-3 text-sabone-gold" />
                        </Button>
                      </div>
                    </div>

                    {/* Price */}
                    <div className="md:col-span-2 flex items-center justify-between md:justify-center mt-2 md:mt-0">
                      <span className="md:hidden text-sabone-cream/70">Price:</span>
                      <span className="text-sabone-cream">${item.product.price.toFixed(2)}</span>
                    </div>

                    {/* Subtotal */}
                    <div className="md:col-span-2 flex items-center justify-between md:justify-end mt-2 md:mt-0">
                      <span className="md:hidden text-sabone-cream/70">Subtotal:</span>
                      <span className="text-sabone-gold font-medium">
                        ${(item.product.price * item.quantity).toFixed(2)}
                      </span>
                    </div>

                    {/* Remove Button - Only visible on desktop */}
                    <div className="hidden md:flex justify-end items-center mt-4 md:mt-0">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-sabone-cream/60 hover:text-sabone-cream hover:bg-sabone-dark-olive"
                        onClick={() => removeItem(item.product.id)}
                        aria-label={`Remove ${item.product.name} from cart`}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Mobile swipe hint - only shown on first item */}
                  {isMobile && item === items[0] && !swipe.swiping && (
                    <div className="mt-2 text-xs text-sabone-gold/80 text-center flex items-center justify-center space-x-1 py-1 bg-sabone-dark-olive/30 rounded-md">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="animate-swipe-hint">
                        <path d="M14 5l-5 5 5 5"></path>
                      </svg>
                      <span>Swipe left to remove item</span>
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
        </>
      )}
    </div>
  );
};

export default CartSummary;
