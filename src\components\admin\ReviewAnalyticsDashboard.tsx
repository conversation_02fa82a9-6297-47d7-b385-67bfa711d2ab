import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { useReviews } from '@/contexts/ReviewContext';
import { useProducts } from '@/contexts/ProductContext';
import {
  getReviewAnalytics,
  exportAnalyticsToCSV,
  clearAnalyticsCache
} from '@/services/reviewAnalyticsService';
import { ReviewAnalytics } from '@/types/review';
import { toast } from 'sonner';
import {
  Star,
  TrendingUp,
  TrendingDown,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw,
  Calendar,
  ThumbsUp
} from 'lucide-react';

const ReviewAnalyticsDashboard: React.FC = () => {
  const { productReviews, getReviews } = useReviews();
  const { products } = useProducts();
  const [analytics, setAnalytics] = useState<ReviewAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load analytics data
  useEffect(() => {
    loadAnalytics();
  }, [productReviews]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      // Collect all reviews from all products
      const allReviews = Object.values(productReviews).flat();

      // If no reviews in context, try to load them
      if (allReviews.length === 0) {
        // Load reviews for all products
        for (const product of products) {
          await getReviews(product.id, 'all');
        }
      }

      const analyticsData = getReviewAnalytics(allReviews);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading review analytics:', error);
      toast.error('Failed to load review analytics');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    clearAnalyticsCache();
    await loadAnalytics();
    setRefreshing(false);
    toast.success('Analytics refreshed successfully');
  };

  const handleExport = () => {
    if (!analytics) return;

    try {
      const csvData = exportAnalyticsToCSV(analytics);
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `review-analytics-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      toast.success('Analytics exported successfully');
    } catch (error) {
      console.error('Error exporting analytics:', error);
      toast.error('Failed to export analytics');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-playfair font-bold text-sabone-gold">Review Analytics</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-sabone-gold/20 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-sabone-gold/20 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <MessageSquare className="h-12 w-12 text-sabone-gold/60 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-sabone-gold mb-2">No Review Data</h3>
        <p className="text-sabone-cream/60">No reviews available for analysis</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-playfair font-bold text-sabone-gold">Review Analytics</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center">
              <MessageSquare className="h-8 w-8 text-sabone-gold mr-3" />
              <div>
                <p className="text-sm text-sabone-cream/80">Total Reviews</p>
                <p className="text-2xl font-bold text-sabone-gold">
                  {analytics.totalReviews.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center">
              <Star className="h-8 w-8 text-sabone-gold mr-3" />
              <div>
                <p className="text-sm text-sabone-cream/80">Average Rating</p>
                <p className="text-2xl font-bold text-sabone-gold">
                  {analytics.averageRating.toFixed(1)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-sabone-gold mr-3" />
              <div>
                <p className="text-sm text-sabone-cream/80">This Month</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold text-sabone-gold">
                    {analytics.reviewsThisMonth}
                  </p>
                  {analytics.reviewGrowthRate !== 0 && (
                    <Badge
                      variant="outline"
                      className={`${
                        analytics.reviewGrowthRate > 0
                          ? 'bg-green-500/10 text-green-500 border-green-500/30'
                          : 'bg-red-500/10 text-red-500 border-red-500/30'
                      }`}
                    >
                      {analytics.reviewGrowthRate > 0 ? (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      )}
                      {Math.abs(analytics.reviewGrowthRate).toFixed(1)}%
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-sabone-gold mr-3" />
              <div>
                <p className="text-sm text-sabone-cream/80">Approval Rate</p>
                <p className="text-2xl font-bold text-sabone-gold">
                  {analytics.moderationStats.approvalRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-sabone-charcoal/50 border-sabone-gold/20">
          <TabsTrigger value="overview" className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal">
            Overview
          </TabsTrigger>
          <TabsTrigger value="products" className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal">
            Top Products
          </TabsTrigger>
          <TabsTrigger value="sentiment" className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal">
            Sentiment
          </TabsTrigger>
          <TabsTrigger value="moderation" className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal">
            Moderation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Moderation Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Pending Reviews
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-sabone-gold">
                  {analytics.moderationStats.pendingReviews}
                </p>
                <p className="text-sm text-sabone-cream/60 mt-1">
                  Awaiting moderation
                </p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Flagged Reviews
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-sabone-gold">
                  {analytics.moderationStats.flaggedReviews}
                </p>
                <p className="text-sm text-sabone-cream/60 mt-1">
                  Require attention
                </p>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold flex items-center gap-2">
                  <ThumbsUp className="h-5 w-5" />
                  Approval Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-sabone-gold">
                  {analytics.moderationStats.approvalRate.toFixed(1)}%
                </p>
                <Progress
                  value={analytics.moderationStats.approvalRate}
                  className="mt-2"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold">Top Rated Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.topRatedProducts.slice(0, 10).map((product, index) => (
                  <div key={product.productId} className="flex items-center justify-between p-3 bg-sabone-charcoal/30 rounded">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="bg-sabone-gold/10 text-sabone-gold border-sabone-gold/30">
                        #{index + 1}
                      </Badge>
                      <div>
                        <p className="font-medium text-sabone-cream">{product.productName}</p>
                        <p className="text-sm text-sabone-cream/60">
                          {product.reviewCount} review{product.reviewCount !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-sabone-gold">
                        {product.averageRating.toFixed(1)}
                      </p>
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < Math.round(product.averageRating)
                                ? 'text-sabone-gold fill-current'
                                : 'text-sabone-gold/30'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-6">
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold">Sentiment Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.sentimentTrends.slice(-7).map((trend) => (
                  <div key={trend.date} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-sabone-cream">{trend.date}</span>
                      <span className="text-sm text-sabone-cream/60">
                        {trend.positive + trend.neutral + trend.negative} reviews
                      </span>
                    </div>
                    <div className="flex gap-1 h-2 rounded overflow-hidden">
                      <div
                        className="bg-green-500"
                        style={{
                          width: `${(trend.positive / (trend.positive + trend.neutral + trend.negative)) * 100}%`
                        }}
                      />
                      <div
                        className="bg-yellow-500"
                        style={{
                          width: `${(trend.neutral / (trend.positive + trend.neutral + trend.negative)) * 100}%`
                        }}
                      />
                      <div
                        className="bg-red-500"
                        style={{
                          width: `${(trend.negative / (trend.positive + trend.neutral + trend.negative)) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="moderation" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold">Moderation Queue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sabone-cream">Pending Reviews</span>
                    <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 border-yellow-500/30">
                      {analytics.moderationStats.pendingReviews}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sabone-cream">Flagged Reviews</span>
                    <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/30">
                      {analytics.moderationStats.flaggedReviews}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sabone-cream">Approval Rate</span>
                    <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/30">
                      {analytics.moderationStats.approvalRate.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
              <CardHeader>
                <CardTitle className="text-sabone-gold">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full justify-start border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={() => window.location.href = '/admin/reviews'}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Review Pending Items
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={() => window.location.href = '/admin/reviews?filter=flagged'}
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Check Flagged Reviews
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={handleExport}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Full Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReviewAnalyticsDashboard;
