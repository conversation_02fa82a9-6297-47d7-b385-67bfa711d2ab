<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sabone Debug Tool</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #1c1c1c;
      color: #e5dcc5;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: #2a2a1f;
      border-radius: 8px;
      border: 1px solid #c6a870;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #c6a870;
    }
    .section {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #333328;
      border-radius: 4px;
    }
    button {
      background-color: #c6a870;
      color: #1c1c1c;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #d8ba82;
    }
    pre {
      background-color: #1c1c1c;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      color: #e5dcc5;
    }
    .error {
      color: #ff6b6b;
    }
    .success {
      color: #6bff6b;
    }
    #console-output {
      max-height: 200px;
      overflow-y: auto;
    }
    .test-result {
      margin-bottom: 10px;
      padding: 8px;
      border-radius: 4px;
    }
    .test-pass {
      background-color: rgba(107, 255, 107, 0.2);
    }
    .test-fail {
      background-color: rgba(255, 107, 107, 0.2);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Sabone Website Debug Tool</h1>
    
    <div class="section">
      <h2>Browser Information</h2>
      <div id="browser-info"></div>
    </div>
    
    <div class="section">
      <h2>Environment Tests</h2>
      <div id="env-tests"></div>
      <button id="run-env-tests">Run Environment Tests</button>
    </div>
    
    <div class="section">
      <h2>React Rendering Test</h2>
      <div id="react-test-container"></div>
      <button id="run-react-test">Test React Rendering</button>
    </div>
    
    <div class="section">
      <h2>Asset Loading Test</h2>
      <div id="asset-tests"></div>
      <button id="run-asset-tests">Test Asset Loading</button>
    </div>
    
    <div class="section">
      <h2>Console Output</h2>
      <pre id="console-output"></pre>
      <button id="clear-console">Clear Console</button>
    </div>
    
    <div class="section">
      <h2>Actions</h2>
      <button id="check-main-app">Check Main App</button>
      <button id="clear-cache">Clear Cache & Storage</button>
    </div>
  </div>

  <script>
    // Override console methods to capture output
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info
    };
    
    const consoleOutput = document.getElementById('console-output');
    
    console.log = function(...args) {
      originalConsole.log(...args);
      appendToConsole('log', args);
    };
    
    console.error = function(...args) {
      originalConsole.error(...args);
      appendToConsole('error', args);
    };
    
    console.warn = function(...args) {
      originalConsole.warn(...args);
      appendToConsole('warn', args);
    };
    
    console.info = function(...args) {
      originalConsole.info(...args);
      appendToConsole('info', args);
    };
    
    function appendToConsole(type, args) {
      const line = document.createElement('div');
      line.className = type === 'error' ? 'error' : '';
      line.textContent = `[${type.toUpperCase()}] ${args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')}`;
      consoleOutput.appendChild(line);
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }
    
    // Display browser information
    function showBrowserInfo() {
      const browserInfo = document.getElementById('browser-info');
      browserInfo.innerHTML = `
        <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
        <p><strong>Browser:</strong> ${getBrowserName()}</p>
        <p><strong>Screen Size:</strong> ${window.innerWidth}x${window.innerHeight}</p>
        <p><strong>Device Pixel Ratio:</strong> ${window.devicePixelRatio}</p>
      `;
    }
    
    function getBrowserName() {
      const userAgent = navigator.userAgent;
      if (userAgent.indexOf("Firefox") > -1) return "Firefox";
      if (userAgent.indexOf("Chrome") > -1) return "Chrome";
      if (userAgent.indexOf("Safari") > -1) return "Safari";
      if (userAgent.indexOf("Edge") > -1) return "Edge";
      if (userAgent.indexOf("MSIE") > -1 || userAgent.indexOf("Trident") > -1) return "Internet Explorer";
      return "Unknown";
    }
    
    // Environment tests
    function runEnvironmentTests() {
      const envTests = document.getElementById('env-tests');
      envTests.innerHTML = '';
      
      const tests = [
        { name: "JavaScript Enabled", test: () => true },
        { name: "localStorage Available", test: () => {
          try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return true;
          } catch (e) {
            return false;
          }
        }},
        { name: "sessionStorage Available", test: () => {
          try {
            sessionStorage.setItem('test', 'test');
            sessionStorage.removeItem('test');
            return true;
          } catch (e) {
            return false;
          }
        }},
        { name: "Cookies Enabled", test: () => navigator.cookieEnabled },
        { name: "Fetch API Available", test: () => typeof fetch === 'function' },
        { name: "ES6 Features", test: () => {
          try {
            eval('const test = () => {}; class Test {}; new Promise(() => {});');
            return true;
          } catch (e) {
            return false;
          }
        }}
      ];
      
      tests.forEach(({name, test}) => {
        try {
          const result = test();
          addTestResult(envTests, name, result);
        } catch (e) {
          addTestResult(envTests, name, false, e.message);
        }
      });
    }
    
    // Asset loading tests
    function runAssetTests() {
      const assetTests = document.getElementById('asset-tests');
      assetTests.innerHTML = '';
      
      // Test loading various assets
      testImageLoading('/favicon.ico.png', 'Favicon');
      testImageLoading('/public/olive-leaf-motif.svg', 'Olive Leaf Motif');
      testImageLoading('/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png', 'Logo');
    }
    
    function testImageLoading(src, name) {
      const assetTests = document.getElementById('asset-tests');
      const img = new Image();
      
      img.onload = () => {
        addTestResult(assetTests, `${name} (${src})`, true);
      };
      
      img.onerror = () => {
        addTestResult(assetTests, `${name} (${src})`, false, 'Failed to load');
      };
      
      img.src = src;
    }
    
    // React rendering test
    function testReactRendering() {
      const container = document.getElementById('react-test-container');
      container.innerHTML = '';
      
      // Check if React is available in the global scope
      if (typeof React === 'undefined' || typeof ReactDOM === 'undefined') {
        // Load React from CDN
        const reactScript = document.createElement('script');
        reactScript.src = 'https://unpkg.com/react@18/umd/react.development.js';
        
        const reactDOMScript = document.createElement('script');
        reactDOMScript.src = 'https://unpkg.com/react-dom@18/umd/react-dom.development.js';
        
        reactScript.onload = () => {
          document.body.appendChild(reactDOMScript);
        };
        
        reactDOMScript.onload = () => {
          renderReactTest();
        };
        
        document.body.appendChild(reactScript);
      } else {
        renderReactTest();
      }
    }
    
    function renderReactTest() {
      const container = document.getElementById('react-test-container');
      
      try {
        const element = React.createElement('div', { 
          style: { 
            padding: '10px', 
            backgroundColor: '#c6a870', 
            color: '#1c1c1c',
            borderRadius: '4px'
          } 
        }, 'React is working correctly!');
        
        ReactDOM.render(element, container);
        console.log('React test rendered successfully');
      } catch (e) {
        container.innerHTML = `<div class="error">React test failed: ${e.message}</div>`;
        console.error('React test failed:', e);
      }
    }
    
    // Helper function to add test results
    function addTestResult(container, name, passed, errorMsg = '') {
      const result = document.createElement('div');
      result.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
      result.innerHTML = `
        <strong>${name}:</strong> ${passed ? 
          '<span class="success">PASS</span>' : 
          `<span class="error">FAIL${errorMsg ? ': ' + errorMsg : ''}</span>`
        }
      `;
      container.appendChild(result);
    }
    
    // Check main app
    function checkMainApp() {
      console.log('Checking main app...');
      
      fetch('/')
        .then(response => response.text())
        .then(html => {
          console.log('Main app HTML loaded successfully');
          
          // Check for root element
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const rootElement = doc.getElementById('root');
          
          if (rootElement) {
            console.log('Root element found in HTML');
          } else {
            console.error('Root element not found in HTML');
          }
          
          // Check for script tags
          const scripts = doc.querySelectorAll('script');
          console.log(`Found ${scripts.length} script tags`);
          
          // Check for main script
          const mainScript = Array.from(scripts).find(script => 
            script.src && (script.src.includes('main') || script.src.includes('index'))
          );
          
          if (mainScript) {
            console.log('Main script found:', mainScript.src);
            
            // Try to load the script
            fetch(mainScript.src)
              .then(response => {
                if (response.ok) {
                  console.log('Main script loaded successfully');
                } else {
                  console.error('Failed to load main script:', response.status, response.statusText);
                }
              })
              .catch(error => {
                console.error('Error loading main script:', error);
              });
          } else {
            console.error('Main script not found');
          }
        })
        .catch(error => {
          console.error('Error loading main app:', error);
        });
    }
    
    // Clear cache and storage
    function clearCacheAndStorage() {
      console.log('Clearing cache and storage...');
      
      // Clear localStorage
      try {
        localStorage.clear();
        console.log('localStorage cleared');
      } catch (e) {
        console.error('Failed to clear localStorage:', e);
      }
      
      // Clear sessionStorage
      try {
        sessionStorage.clear();
        console.log('sessionStorage cleared');
      } catch (e) {
        console.error('Failed to clear sessionStorage:', e);
      }
      
      // Clear cookies
      document.cookie.split(';').forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
        document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
      });
      console.log('Cookies cleared');
      
      console.log('Cache and storage cleared. You may need to reload the page.');
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      showBrowserInfo();
      
      document.getElementById('run-env-tests').addEventListener('click', runEnvironmentTests);
      document.getElementById('run-react-test').addEventListener('click', testReactRendering);
      document.getElementById('run-asset-tests').addEventListener('click', runAssetTests);
      document.getElementById('clear-console').addEventListener('click', () => {
        document.getElementById('console-output').innerHTML = '';
      });
      document.getElementById('check-main-app').addEventListener('click', checkMainApp);
      document.getElementById('clear-cache').addEventListener('click', clearCacheAndStorage);
      
      console.log('Debug tool initialized');
    });
  </script>
</body>
</html>
