import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { CartProvider } from "@/contexts/CartContext";
import { Auth0Provider } from "@auth0/auth0-react";
import { AuthProvider } from "@/contexts/AuthContext";
import { OrderProvider } from "@/contexts/OrderContext";
import { InventoryProvider } from "@/contexts/InventoryContext";
import { EmailProvider } from "@/contexts/EmailContext";
import { ProductProvider } from "@/contexts/ProductContext";
import { BundleProvider } from "@/contexts/BundleContext";
import { ReviewProvider } from "@/contexts/ReviewContext";
import { WishlistProvider } from "@/contexts/WishlistContext";
import { SearchProvider } from "@/contexts/SearchContext";
import { RecommendationProvider } from "@/contexts/RecommendationContext";
import ExtensionErrorBanner from "@/components/ExtensionErrorBanner";
import AuthErrorHandler from "@/components/AuthErrorHandler";
import SectionErrorBoundary from "@/components/error/SectionErrorBoundary";
import { Suspense, useState, useEffect } from "react";
// Import next-intl components and utilities
import { NextIntlClientProvider } from 'next-intl';
import { defaultLocale, getMessages, getUserLocale, setUserLocale } from '@/i18n/config';
// Import Arabic text styles instead of RTL layout styles
import "@/i18n/ar-text-style.css";

// Import enhanced lazy loading utilities
import { createLazyRoute } from "@/utils/lazyLoading";

// Import lazy-loaded components from centralized file
import {
  LazyIndex as Index,
  LazyCheckout as Checkout,
  LazyProductDetailPage as ProductDetailPage,
  LazyAccount as Account,
  LazyDevAdminAccess as DevAdminAccess,
  LazyNotFound as NotFound,
  LazyAdminDashboard as AdminDashboard,
  LazyAdminInventory as AdminInventory,
  LazyAdminOrders as AdminOrders,
  LazyAdminReviews as AdminReviews,
  LazyAdminCustomers as AdminCustomers,
  LazyAdminSettings as AdminSettings
} from "@/components/LazyComponents";

// Enhanced lazy loading for frequently accessed pages
const LazySearchPage = createLazyRoute(
  () => import("@/pages/SearchPage"),
  {
    chunkName: 'SearchPage',
    preload: true,
    loadingType: 'skeleton'
  }
);

const LazyWishlistPage = createLazyRoute(
  () => import("@/pages/WishlistPage"),
  {
    chunkName: 'WishlistPage',
    preloadCondition: () => !!localStorage.getItem('auth_token'), // Preload for authenticated users
    loadingType: 'skeleton'
  }
);

const LazyLocalizationDemo = createLazyRoute(
  () => import("@/pages/LocalizationDemo"),
  {
    chunkName: 'LocalizationDemo',
    loadingType: 'spinner'
  }
);

const LazySecurityTestPage = createLazyRoute(
  () => import("@/pages/SecurityTestPage"),
  {
    chunkName: 'SecurityTestPage',
    loadingType: 'spinner'
  }
);

// Import shared PageLoader
import PageLoader from "@/components/ui/PageLoader"; // Assuming this is the correct path and component

const queryClient = new QueryClient();

// Check if we should skip Auth0 for development
const isDevelopmentMode = import.meta.env.DEV && import.meta.env.VITE_SKIP_AUTH === 'true';

const App = () => {
  const [currentLocale] = useState<string>(getUserLocale());
  const [messages, setMessages] = useState<Record<string, Record<string, unknown>>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    setUserLocale(currentLocale); // Ensure locale is set in localStorage

    // Set the HTML lang attribute for proper language identification
    document.documentElement.lang = currentLocale;

    const loadMessages = async () => {
      setLoading(true);
      setError(false);
      try {
        console.log(`App: Loading messages for locale ${currentLocale}`);
        const loadedMessages = await getMessages(currentLocale);
        console.log('App: Raw messages loaded:', loadedMessages);

        // Pass nested messages directly to NextIntlClientProvider
        if (!loadedMessages || Object.keys(loadedMessages).length === 0) {
          console.warn(`No messages found for locale ${currentLocale}, falling back to default`);
          const defaultMessages = await getMessages(defaultLocale);
          console.log('App: Default messages (nested):', defaultMessages);
          setMessages(defaultMessages);
        } else {
          setMessages(loadedMessages);
        }

        // Debug navigation translations specifically from the nested structure
        if (loadedMessages && loadedMessages.common && loadedMessages.common.navigation) {
          console.log('App: Navigation translations (from loadedMessages):', {
            shop: loadedMessages.common.navigation.shop,
            about: loadedMessages.common.navigation.about,
            contact: loadedMessages.common.navigation.contact
          });
        } else {
          console.warn('App: common.navigation namespace not found in loaded messages for debugging.');
        }
      } catch (err) {
        console.error("Failed to load messages:", err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [currentLocale]);

  // Conditionally wrap with Auth0Provider based on development mode
  const AppContent = () => (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ProductProvider>
          <BundleProvider>
            <CartProvider>
              <EmailProvider>
                <InventoryProvider>
                  <OrderProvider>
                    <ReviewProvider>
                      <WishlistProvider>
                        <SearchProvider>
                          <RecommendationProvider>
                            <TooltipProvider>
                          <Sonner />
                          <AuthErrorHandler />
                          <BrowserRouter>
                            <Routes>
                              <Route path="/" element={
                                <SectionErrorBoundary section="Homepage">
                                  <Suspense fallback={<PageLoader />}>
                                    <Index />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/checkout" element={
                                <SectionErrorBoundary section="Checkout">
                                  <Suspense fallback={<PageLoader />}>
                                    <Checkout />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/search" element={
                                <SectionErrorBoundary section="Search">
                                  <LazySearchPage />
                                </SectionErrorBoundary>
                              } />
                              <Route path="/wishlist" element={
                                <SectionErrorBoundary section="Wishlist">
                                  <LazyWishlistPage />
                                </SectionErrorBoundary>
                              } />
                              <Route path="/localization" element={
                                <SectionErrorBoundary section="Localization">
                                  <LazyLocalizationDemo />
                                </SectionErrorBoundary>
                              } />
                              <Route path="/security-test" element={
                                <SectionErrorBoundary section="Security Test">
                                  <LazySecurityTestPage />
                                </SectionErrorBoundary>
                              } />
                              <Route path="/product/:id" element={
                                <SectionErrorBoundary section="Product Details">
                                  <Suspense fallback={<PageLoader />}>
                                    <ProductDetailPage />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/account/*" element={
                                <SectionErrorBoundary section="Account">
                                  <Suspense fallback={<PageLoader />}>
                                    <Account />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />

                              {/* Development Admin Access */}
                              <Route path="/dev-admin" element={
                                <SectionErrorBoundary section="Dev Admin">
                                  <Suspense fallback={<PageLoader />}>
                                    <DevAdminAccess />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />

                              {/* Admin Routes */}
                              <Route path="/account/admin" element={
                                <SectionErrorBoundary section="Admin Dashboard">
                                  <Suspense fallback={<PageLoader />}>
                                    <AdminDashboard />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/account/admin/inventory" element={
                                <SectionErrorBoundary section="Admin Inventory">
                                  <Suspense fallback={<PageLoader />}>
                                    <AdminInventory />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/account/admin/orders" element={
                                <SectionErrorBoundary section="Admin Orders">
                                  <Suspense fallback={<PageLoader />}>
                                    <AdminOrders />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/account/admin/reviews" element={
                                <SectionErrorBoundary section="Admin Reviews">
                                  <Suspense fallback={<PageLoader />}>
                                    <AdminReviews />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/account/admin/customers" element={
                                <SectionErrorBoundary section="Admin Customers">
                                  <Suspense fallback={<PageLoader />}>
                                    <AdminCustomers />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                              <Route path="/account/admin/settings" element={
                                <SectionErrorBoundary section="Admin Settings">
                                  <Suspense fallback={<PageLoader />}>
                                    <AdminSettings />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />

                              {/* Catch-all route */}
                              <Route path="*" element={
                                <SectionErrorBoundary section="404 Page">
                                  <Suspense fallback={<PageLoader />}>
                                    <NotFound />
                                  </Suspense>
                                </SectionErrorBoundary>
                              } />
                            </Routes>
                          </BrowserRouter>
                            </TooltipProvider>
                          </RecommendationProvider>
                        </SearchProvider>
                      </WishlistProvider>
                    </ReviewProvider>
                  </OrderProvider>
                </InventoryProvider>
              </EmailProvider>
            </CartProvider>
          </BundleProvider>
        </ProductProvider>
      </AuthProvider>
    </QueryClientProvider>
  );

  if (loading) {
    return (
      <div className="app-loading-container">
        Loading localization...
      </div>
    );
  }

  if (error) {
    return (
      <div className="app-error-container">
        Error loading localization.
      </div>
    );
  }

  return (
    <>
      <ExtensionErrorBanner />
      <NextIntlClientProvider locale={currentLocale} messages={messages} key={`provider-${currentLocale}`}>
        {isDevelopmentMode ? (
          <AppContent key={`appcontent-${currentLocale}`} />
        ) : (
          <Auth0Provider
            domain={import.meta.env.VITE_AUTH0_DOMAIN || 'dev-placeholder.auth0.com'}
            clientId={import.meta.env.VITE_AUTH0_CLIENT_ID || 'placeholder123456'}
            authorizationParams={{
              redirect_uri: window.location.origin,
              audience: import.meta.env.VITE_AUTH0_AUDIENCE,
              scope: 'openid profile email read:current_user update:current_user_metadata'
            }}
            cacheLocation="localstorage"
            useRefreshTokens={true}
            useRefreshTokensFallback={true}
            onRedirectCallback={(appState) => {
              // Handle redirect after login/logout
              window.history.replaceState(
                {},
                document.title,
                appState?.returnTo || window.location.pathname
              );
            }}
          >
            <AppContent key={`appcontent-${currentLocale}`} />
          </Auth0Provider>
        )}
      </NextIntlClientProvider>
    </>
  );
};

export default App;
