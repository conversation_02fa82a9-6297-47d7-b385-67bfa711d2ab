# 🌐 Sabone.store Internationalization (i18n) Plan

## 📋 Overview

This document outlines the strategy for implementing internationalization (i18n) in the Sabone.store e-commerce platform, with a particular focus on Arabic language support to align with the brand's cultural roots.

## 🎯 Goals

1. **Cultural Authenticity**: Enhance the connection to Arabic bathing traditions through native language support
2. **Market Expansion**: Reach Arabic-speaking markets in the Middle East and North Africa
3. **User Experience**: Provide a seamless experience for users in their preferred language
4. **Brand Consistency**: Maintain the luxury brand identity across all languages

## 🌍 Target Languages

### Phase 1
- **English (en-US)**: Primary/default language
- **Arabic (ar)**: Primary target for internationalization

### Future Phases
- **French (fr)**: For North African markets
- **German (de)**: For European expansion
- **Spanish (es)**: For broader global reach

## 🛠️ Technical Implementation

### i18n Framework

We will use **next-intl** for internationalization:

```bash
npm install next-intl
```

### Directory Structure

```
src/
├── i18n/
│   ├── config.ts           # i18n configuration
│   ├── messages/           # Translation files
│   │   ├── en.json         # English translations
│   │   ├── ar.json         # Arabic translations
│   │   └── ...
│   └── hooks/              # Custom i18n hooks
├── components/
│   └── LanguageSwitcher.tsx # Language selection component
```

### Translation Files Structure

```json
// en.json
{
  "common": {
    "navigation": {
      "home": "Home",
      "shop": "Shop",
      "about": "About",
      "contact": "Contact"
    },
    "buttons": {
      "addToCart": "Add to Cart",
      "checkout": "Checkout",
      "signIn": "Sign In"
    }
  },
  "product": {
    "details": {
      "ingredients": "Ingredients",
      "benefits": "Benefits",
      "application": "How to Use"
    }
  },
  "cart": {
    "empty": "Your cart is empty",
    "subtotal": "Subtotal",
    "shipping": "Shipping",
    "total": "Total"
  }
}
```

### Implementation Steps

1. **Setup i18n Provider**

```tsx
// i18n/config.ts
import { createIntl } from 'next-intl';

export const locales = ['en', 'ar'];
export const defaultLocale = 'en';

export function getMessages(locale: string) {
  return import(`./messages/${locale}.json`).then((module) => module.default);
}

export async function createIntlInstance(locale: string) {
  const messages = await getMessages(locale);
  return createIntl({ locale, messages });
}
```

2. **Create Provider Component**

```tsx
// i18n/IntlProvider.tsx
import React, { useState, useEffect } from 'react';
import { IntlProvider as NextIntlProvider } from 'next-intl';
import { getMessages, defaultLocale } from './config';

export const IntlProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [messages, setMessages] = useState<Record<string, any>>({});
  const [locale, setLocale] = useState(defaultLocale);
  
  useEffect(() => {
    // Get user's preferred language from localStorage or browser settings
    const savedLocale = localStorage.getItem('sabone-locale') || 
      navigator.language.split('-')[0];
    
    // Load messages for the locale
    getMessages(savedLocale)
      .then(msgs => {
        setMessages(msgs);
        setLocale(savedLocale);
      })
      .catch(() => {
        // Fallback to default locale if translation not available
        getMessages(defaultLocale).then(msgs => {
          setMessages(msgs);
          setLocale(defaultLocale);
        });
      });
  }, []);
  
  if (Object.keys(messages).length === 0) {
    return <div>Loading...</div>; // Or a proper loading component
  }
  
  return (
    <NextIntlProvider locale={locale} messages={messages}>
      {children}
    </NextIntlProvider>
  );
};
```

3. **Create Language Switcher Component**

```tsx
// components/LanguageSwitcher.tsx
import { useTranslations } from 'next-intl';
import { useRouter } from 'react-router-dom';
import { locales } from '../i18n/config';

export const LanguageSwitcher = () => {
  const t = useTranslations('common');
  const router = useRouter();
  
  const changeLanguage = (locale: string) => {
    localStorage.setItem('sabone-locale', locale);
    window.location.reload(); // Simple approach - reload page with new locale
  };
  
  return (
    <div className="language-switcher">
      <select 
        onChange={(e) => changeLanguage(e.target.value)}
        value={localStorage.getItem('sabone-locale') || 'en'}
        className="bg-sabone-charcoal text-sabone-cream border border-sabone-gold/30 rounded-md"
      >
        {locales.map((locale) => (
          <option key={locale} value={locale}>
            {locale === 'en' ? 'English' : locale === 'ar' ? 'العربية' : locale}
          </option>
        ))}
      </select>
    </div>
  );
};
```

4. **Usage in Components**

```tsx
// Example component using translations
import { useTranslations } from 'next-intl';

const ProductDetails = ({ product }) => {
  const t = useTranslations('product.details');
  
  return (
    <div>
      <h2>{product.name}</h2>
      <p>{product.description}</p>
      
      <h3>{t('ingredients')}</h3>
      <ul>
        {product.ingredients.map((ingredient) => (
          <li key={ingredient}>{ingredient}</li>
        ))}
      </ul>
      
      <h3>{t('benefits')}</h3>
      <ul>
        {product.benefits.map((benefit) => (
          <li key={benefit}>{benefit}</li>
        ))}
      </ul>
      
      <h3>{t('application')}</h3>
      <p>{product.application}</p>
    </div>
  );
};
```

## 🖌️ RTL Support for Arabic

### CSS Configuration

1. **Add RTL Support to Tailwind**

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      // existing theme configuration
    },
  },
  plugins: [
    require('tailwindcss-rtl'), // Add RTL support plugin
  ],
}
```

2. **Create RTL Utility Hook**

```tsx
// hooks/useRTL.ts
import { useEffect, useState } from 'react';

export const useRTL = () => {
  const [isRTL, setIsRTL] = useState(false);
  
  useEffect(() => {
    const locale = localStorage.getItem('sabone-locale') || 'en';
    setIsRTL(locale === 'ar');
    
    // Set direction on html element
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    
    // Add RTL class to body if needed
    if (locale === 'ar') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, []);
  
  return isRTL;
};
```

3. **RTL-Aware Components**

```tsx
// Example of RTL-aware component
import { useRTL } from '../hooks/useRTL';

const ProductCard = ({ product }) => {
  const isRTL = useRTL();
  
  return (
    <div className={`product-card ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className={`flex ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
        <img src={product.image} alt={product.name} />
        <div className="product-info">
          {/* Product information */}
        </div>
      </div>
    </div>
  );
};
```

## 📝 Content Translation Strategy

### Product Information

1. **Static Content**
   - Translate all static UI elements through translation files
   - Include product categories, navigation, buttons, etc.

2. **Dynamic Content**
   - Store product information in multiple languages in the database
   - Structure product data to include language-specific fields:

```typescript
interface MultilingualProduct {
  id: string;
  images: string[];
  price: number;
  type: 'bar' | 'liquid';
  translations: {
    [locale: string]: {
      name: string;
      description: string;
      fullDescription: string;
      ingredients: string[];
      benefits: string[];
      application: string;
      storage: string;
    }
  }
}
```

### Translation Workflow

1. **Initial Translation**
   - Hire professional translators familiar with Arabic beauty/cosmetic terminology
   - Ensure cultural nuances are properly captured
   - Review translations with native speakers

2. **Ongoing Translation**
   - Establish a process for translating new content
   - Create a translation management system or workflow
   - Implement quality assurance for translations

## 🧪 Testing Strategy

1. **Automated Testing**
   - Create tests for i18n functionality
   - Verify correct loading of translations
   - Test language switching

2. **Manual Testing**
   - Test with native Arabic speakers
   - Verify RTL layout on various screen sizes
   - Check for cultural appropriateness

3. **Visual Regression Testing**
   - Ensure layouts work properly in both LTR and RTL modes
   - Test all components in both language directions

## 📱 Mobile Considerations

- Test RTL layouts thoroughly on mobile devices
- Ensure touch targets remain appropriate in RTL mode
- Verify that responsive designs adapt correctly to RTL

## 🚀 Implementation Phases

### Phase 1: Foundation (2-3 weeks)
- Set up i18n framework
- Implement language switching
- Add RTL support for layouts
- Translate core UI elements

### Phase 2: Content Translation (2-3 weeks)
- Translate product information
- Translate marketing content
- Implement multilingual product data structure

### Phase 3: Refinement (1-2 weeks)
- User testing with Arabic speakers
- Fix RTL layout issues
- Optimize performance

### Phase 4: Launch (1 week)
- Final QA testing
- Deploy Arabic version
- Monitor and gather feedback
