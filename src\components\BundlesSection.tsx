import { useState, useEffect } from "react";
import { useBundles } from "@/contexts/BundleContext";
import BundleCard from "@/components/bundle/BundleCard";

const BundlesSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { bundles, loading } = useBundles();

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsVisible(true);
      }
    }, { threshold: 0.1 });

    const element = document.getElementById("bundles-section");
    if (element) observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, []);

  return (
    <section id="bundles-section" className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-sabone-dark-olive/20"></div>
      <div className="pattern-dot absolute inset-0 opacity-5"></div>

      <div className="max-w-7xl mx-auto relative">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-playfair font-bold text-sabone-gold">Bundles & Offers</h2>
            <div className="arabesque-divider w-24 mx-auto my-6"></div>
            <p className="max-w-2xl mx-auto text-base md:text-lg text-sabone-cream/90">
              Curated rituals to elevate your self-care — and save while you indulge.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {loading ? (
              // Loading placeholders
              Array(3).fill(0).map((_, index) => (
                <div key={index} className="bg-sabone-dark-olive/30 rounded-lg h-96 animate-pulse"></div>
              ))
            ) : bundles.length > 0 ? (
              bundles.map((bundle) => (
                <BundleCard key={bundle.id} bundle={bundle} />
              ))
            ) : (
              <div className="col-span-3 text-center py-8 text-sabone-cream/70">
                No bundles available at this time.
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BundlesSection;
