import { ReactNode, useEffect, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

// Initialize Stripe with your publishable key from environment variables
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

interface StripeProviderProps {
  children: ReactNode;
  clientSecret?: string;
}

const StripeProvider = ({ children, clientSecret }: StripeProviderProps) => {
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before rendering Stripe Elements
  // This helps prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Enhanced options for Stripe Elements with luxury styling
  const options = {
    clientSecret,
    appearance: {
      theme: 'night',
      variables: {
        colorPrimary: '#C6A870', // sabone-gold
        colorBackground: '#1C1C1C', // sabone-charcoal
        colorText: '#E5DCC5', // sabone-cream
        colorDanger: '#EF4444', // red-500
        fontFamily: 'Montserrat, Arial, sans-serif',
        spacingUnit: '6px',
        borderRadius: '4px',
      },
      rules: {
        '.Input': {
          border: '1px solid rgba(198, 168, 112, 0.3)',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.3)',
          transition: 'border 0.3s, box-shadow 0.3s',
        },
        '.Input:focus': {
          border: '1px solid rgba(198, 168, 112, 0.6)',
          boxShadow: '0 1px 8px rgba(198, 168, 112, 0.2)',
        },
        '.Label': {
          color: 'rgba(229, 220, 197, 0.8)',
          fontSize: '14px',
          fontWeight: '500',
        },
        '.Error': {
          color: '#EF4444',
          fontSize: '13px',
        },
      },
    },
  };

  if (!mounted) {
    return null;
  }

  return (
    <Elements stripe={stripePromise} options={clientSecret ? options : undefined}>
      {children}
    </Elements>
  );
};

export default StripeProvider;
