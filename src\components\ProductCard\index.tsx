import { useState } from "react";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Link } from "react-router-dom";
import { ProductCardProps } from "./types";
import ProductImage from "./ProductImage";
import ProductOverlay from "./ProductOverlay";
import ProductDialog from "./ProductDialog";
import { useCart } from "@/contexts/CartContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { Heart } from "lucide-react";

const ProductCard = ({ product }: ProductCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const { addItem } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();

  const addToCart = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    addItem(product, 1);
  };

  const toggleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  return (
    <article
      className="product-card premium-card rounded-md overflow-hidden h-full transition-all duration-300 relative ambient-glow"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      itemScope
      itemType="https://schema.org/Product"
    >
      <meta itemProp="name" content={product.name} />
      <meta itemProp="description" content={product.description} />
      <meta itemProp="price" content={product.price.toString()} />
      <meta itemProp="priceCurrency" content="USD" />

      <div className="p-0 relative group">
        <Dialog>
          <DialogTrigger asChild>
            <button
              className="w-full text-left cursor-pointer relative block"
              aria-label={`View details for ${product.name}`}
            >
              <ProductImage product={product} isHovered={isHovered} />
              <ProductOverlay
                product={product}
                isHovered={isHovered}
                onAddToCart={addToCart}
              />
              <span className="sr-only">View details for {product.name}</span>
            </button>
          </DialogTrigger>
          <ProductDialog product={product} onAddToCart={addToCart} />
        </Dialog>

        {/* Link to product detail page */}
        <Link
          to={`/product/${product.id}`}
          className="absolute top-4 right-4 bg-sabone-charcoal/70 backdrop-blur-sm text-sabone-gold p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 hover:bg-sabone-gold hover:text-sabone-charcoal"
          aria-label={`View ${product.name} details page`}
        >
          <span className="sr-only">View product details</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
            <polyline points="15 3 21 3 21 9"></polyline>
            <line x1="10" y1="14" x2="21" y2="3"></line>
          </svg>
        </Link>

        {/* Wishlist button */}
        <button
          onClick={toggleWishlist}
          className={`absolute top-4 left-4 p-2 rounded-full z-10 transition-all duration-300 ${
            isInWishlist(product.id)
              ? 'bg-sabone-gold text-sabone-charcoal opacity-100'
              : 'bg-sabone-charcoal/70 backdrop-blur-sm text-sabone-gold opacity-0 group-hover:opacity-100 hover:bg-sabone-gold hover:text-sabone-charcoal'
          }`}
          aria-label={isInWishlist(product.id) ? `Remove ${product.name} from wishlist` : `Add ${product.name} to wishlist`}
        >
          <Heart size={16} className={isInWishlist(product.id) ? 'fill-current' : ''} />
          <span className="sr-only">
            {isInWishlist(product.id) ? 'Remove from wishlist' : 'Add to wishlist'}
          </span>
        </button>
      </div>
    </article>
  );
};

export default ProductCard;
