import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";

import {
  Truck,
  CheckCircle2,
  XCircle,
  Calendar,
  User,
  Package,
  FileText,
  Mail,
  CreditCard,
  Clock,
  MessageSquare,
  Send,
  RefreshCw,
  Printer
} from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

// Status colors for badges
const STATUS_COLORS = {
  pending: "bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30 border-yellow-500/30",
  processing: "bg-blue-500/20 text-blue-500 hover:bg-blue-500/30 border-blue-500/30",
  shipped: "bg-purple-500/20 text-purple-500 hover:bg-purple-500/30 border-purple-500/30",
  delivered: "bg-green-500/20 text-green-500 hover:bg-green-500/30 border-green-500/30",
  cancelled: "bg-red-500/20 text-red-500 hover:bg-red-500/30 border-red-500/30"
};

// Payment method display names
const PAYMENT_METHODS = {
  credit_card: "Credit Card",
  paypal: "PayPal",
  cash_on_delivery: "Cash on Delivery"
};

// Mock order notes
const mockOrderNotes = [
  {
    id: 1,
    orderId: "ORD-001",
    text: "Customer requested gift wrapping",
    createdBy: "Admin User",
    createdAt: "2023-05-15T14:30:00Z",
    isInternal: true
  },
  {
    id: 2,
    orderId: "ORD-001",
    text: "Package prepared with eco-friendly materials as requested",
    createdBy: "Shipping Team",
    createdAt: "2023-05-16T09:15:00Z",
    isInternal: true
  },
  {
    id: 3,
    orderId: "ORD-002",
    text: "Customer asked about estimated delivery time",
    createdBy: "Customer Support",
    createdAt: "2023-05-16T11:45:00Z",
    isInternal: true
  }
];

// Mock order history
const mockOrderHistory = [
  {
    id: 1,
    orderId: "ORD-001",
    status: "pending",
    timestamp: "2023-05-15T10:30:00Z",
    user: "System"
  },
  {
    id: 2,
    orderId: "ORD-001",
    status: "processing",
    timestamp: "2023-05-15T14:45:00Z",
    user: "Admin User"
  },
  {
    id: 3,
    orderId: "ORD-002",
    status: "pending",
    timestamp: "2023-05-16T09:30:00Z",
    user: "System"
  },
  {
    id: 4,
    orderId: "ORD-002",
    status: "processing",
    timestamp: "2023-05-16T11:15:00Z",
    user: "Admin User"
  },
  {
    id: 5,
    orderId: "ORD-002",
    status: "shipped",
    timestamp: "2023-05-17T13:20:00Z",
    user: "Shipping Team"
  }
];

interface OrderDetailViewProps {
  order: any;
  onUpdateStatus: (orderId: string, status: string) => void;
  isUpdatingStatus: boolean;
}

const OrderDetailView: React.FC<OrderDetailViewProps> = ({
  order,
  onUpdateStatus,
  isUpdatingStatus
}) => {
  const [activeTab, setActiveTab] = useState("details");
  const [newNote, setNewNote] = useState("");
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [orderNotes, setOrderNotes] = useState(
    mockOrderNotes.filter(note => note.orderId === order.id)
  );
  const [orderHistory, _setOrderHistory] = useState(
    mockOrderHistory.filter(history => history.orderId === order.id)
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    return (
      <Badge variant="outline" className={STATUS_COLORS[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Handle adding a new note
  const handleAddNote = () => {
    if (!newNote.trim()) {
      toast.error("Note cannot be empty");
      return;
    }

    setIsAddingNote(true);

    // Simulate API call
    setTimeout(() => {
      const newNoteObj = {
        id: Date.now(),
        orderId: order.id,
        text: newNote,
        createdBy: "Admin User",
        createdAt: new Date().toISOString(),
        isInternal: true
      };

      setOrderNotes(prev => [...prev, newNoteObj]);
      setNewNote("");
      setIsAddingNote(false);
      toast.success("Note added successfully");
    }, 1000);
  };

  // Get next status based on current status
  const getNextStatus = (currentStatus: string) => {
    switch (currentStatus) {
      case "pending": return "processing";
      case "processing": return "shipped";
      case "shipped": return "delivered";
      default: return currentStatus;
    }
  };

  // Get action button text based on status
  const getActionButtonText = (status: string) => {
    switch (status) {
      case "pending": return "Process Order";
      case "processing": return "Mark as Shipped";
      case "shipped": return "Mark as Delivered";
      default: return "View Order";
    }
  };

  // Get action button icon based on status
  const getActionButtonIcon = (status: string) => {
    switch (status) {
      case "pending": return <Package className="h-4 w-4 mr-2" />;
      case "processing": return <Truck className="h-4 w-4 mr-2" />;
      case "shipped": return <CheckCircle2 className="h-4 w-4 mr-2" />;
      default: return <FileText className="h-4 w-4 mr-2" />;
    }
  };

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="bg-sabone-dark-olive h-12 p-1 mb-6">
        <TabsTrigger
          value="details"
          className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px]"
        >
          <FileText className="h-4 w-4 mr-2" />
          Order Details
        </TabsTrigger>
        <TabsTrigger
          value="history"
          className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px]"
        >
          <Clock className="h-4 w-4 mr-2" />
          Order History
        </TabsTrigger>
        <TabsTrigger
          value="notes"
          className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal px-4 md:px-6 min-h-[44px]"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Notes
        </TabsTrigger>
      </TabsList>

      {/* Order Details Tab */}
      <TabsContent value="details" className="mt-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Order Summary */}
          <Card className="md:col-span-2 bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Order Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-sabone-gold font-medium">Order ID: {order.id}</h3>
                  <p className="text-sabone-cream/70 text-sm">
                    <Calendar className="h-4 w-4 inline-block mr-1" />
                    {order.date}
                  </p>
                </div>
                <div>
                  {getStatusBadge(order.status)}
                </div>
              </div>

              <Separator className="bg-sabone-gold/20 my-4" />

              <div className="space-y-4">
                <h4 className="text-sabone-gold font-medium">Items</h4>
                {order.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-sabone-gold/10 last:border-0">
                    <div>
                      <p className="text-sabone-cream">{item.name}</p>
                      <p className="text-sabone-cream/70 text-sm">Quantity: {item.quantity}</p>
                    </div>
                    <p className="text-sabone-cream">${(item.price * item.quantity).toFixed(2)}</p>
                  </div>
                ))}

                <div className="pt-4 space-y-2">
                  <div className="flex justify-between text-sabone-cream/70">
                    <span>Subtotal</span>
                    <span>${order.total.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sabone-cream/70">
                    <span>Shipping</span>
                    <span>$5.99</span>
                  </div>
                  <div className="flex justify-between text-sabone-cream/70">
                    <span>Tax</span>
                    <span>${(order.total * 0.07).toFixed(2)}</span>
                  </div>
                  <Separator className="bg-sabone-gold/20 my-2" />
                  <div className="flex justify-between text-sabone-gold font-medium">
                    <span>Total</span>
                    <span>${(order.total + 5.99 + (order.total * 0.07)).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer & Shipping Info */}
          <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
            <CardHeader>
              <CardTitle className="text-sabone-gold flex items-center">
                <User className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="text-sabone-gold font-medium">Contact</h4>
                <p className="text-sabone-cream mt-1">{order.customer.name}</p>
                <p className="text-sabone-cream/70 flex items-center mt-1">
                  <Mail className="h-4 w-4 mr-1" />
                  {order.customer.email}
                </p>
              </div>

              <Separator className="bg-sabone-gold/20 my-4" />

              <div>
                <h4 className="text-sabone-gold font-medium">Shipping Address</h4>
                <div className="text-sabone-cream/70 mt-1 space-y-1">
                  <p>{order.shippingAddress.line1}</p>
                  {order.shippingAddress.line2 && <p>{order.shippingAddress.line2}</p>}
                  <p>
                    {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                  </p>
                  <p>{order.shippingAddress.country}</p>
                </div>
              </div>

              <Separator className="bg-sabone-gold/20 my-4" />

              <div>
                <h4 className="text-sabone-gold font-medium">Payment Method</h4>
                <p className="text-sabone-cream/70 flex items-center mt-1">
                  <CreditCard className="h-4 w-4 mr-1" />
                  {PAYMENT_METHODS[order.paymentMethod]}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-6 flex flex-wrap gap-2 justify-end">
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => window.print()}
          >
            <Printer className="h-4 w-4 mr-2" />
            Print Order
          </Button>

          {order.status !== "delivered" && order.status !== "cancelled" && (
            <Button
              variant="default"
              className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
              onClick={() => onUpdateStatus(order.id, getNextStatus(order.status))}
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  {getActionButtonIcon(order.status)}
                  {getActionButtonText(order.status)}
                </>
              )}
            </Button>
          )}

          {order.status !== "cancelled" && order.status !== "delivered" && (
            <Button
              variant="outline"
              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
              onClick={() => onUpdateStatus(order.id, "cancelled")}
              disabled={isUpdatingStatus}
            >
              <XCircle className="h-4 w-4 mr-2" />
              Cancel Order
            </Button>
          )}
        </div>
      </TabsContent>

      {/* Order History Tab */}
      <TabsContent value="history" className="mt-0">
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader>
            <CardTitle className="text-sabone-gold flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Order Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {orderHistory.map((history, index) => (
                <div key={history.id} className="relative pl-6 pb-6">
                  {/* Timeline connector */}
                  {index < orderHistory.length - 1 && (
                    <div className="absolute left-[9px] top-[24px] bottom-0 w-[2px] bg-sabone-gold/20"></div>
                  )}

                  {/* Status dot */}
                  <div className={`absolute left-0 top-1 h-5 w-5 rounded-full border-2 ${STATUS_COLORS[history.status].split(' ')[0]} border-sabone-gold/30`}></div>

                  <div>
                    <div className="flex justify-between items-start">
                      <h4 className="text-sabone-gold font-medium">
                        Status changed to: {history.status.charAt(0).toUpperCase() + history.status.slice(1)}
                      </h4>
                      <span className="text-sabone-cream/50 text-xs">
                        {formatDate(history.timestamp)}
                      </span>
                    </div>
                    <p className="text-sabone-cream/70 text-sm mt-1">
                      Updated by: {history.user}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Order Notes Tab */}
      <TabsContent value="notes" className="mt-0">
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader>
            <CardTitle className="text-sabone-gold flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              Order Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {orderNotes.length === 0 ? (
                <p className="text-sabone-cream/70 text-center py-4">No notes for this order yet</p>
              ) : (
                orderNotes.map((note) => (
                  <div key={note.id} className="bg-sabone-charcoal/40 p-4 rounded-md">
                    <div className="flex justify-between items-start">
                      <h4 className="text-sabone-gold font-medium">{note.createdBy}</h4>
                      <span className="text-sabone-cream/50 text-xs">
                        {formatDate(note.createdAt)}
                      </span>
                    </div>
                    <p className="text-sabone-cream mt-2">{note.text}</p>
                    {note.isInternal && (
                      <Badge variant="outline" className="mt-2 bg-blue-500/10 text-blue-400 border-blue-500/30">
                        Internal Note
                      </Badge>
                    )}
                  </div>
                ))
              )}

              <div className="mt-6">
                <Label htmlFor="new-note" className="text-sabone-gold">Add Note</Label>
                <Textarea
                  id="new-note"
                  placeholder="Enter a note about this order..."
                  className="mt-2 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                />
                <div className="mt-4 flex justify-end">
                  <Button
                    variant="default"
                    className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                    onClick={handleAddNote}
                    disabled={isAddingNote || !newNote.trim()}
                  >
                    {isAddingNote ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Adding...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Add Note
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default OrderDetailView;
