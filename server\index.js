import dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { buffer } from 'micro';

// Import database configuration
import { initDatabase } from './db/config.js';

// Import rate limiting middleware
import { 
  generalLimiter, 
  paymentLimiter, 
  webhookLimiter 
} from './middleware/rateLimiter.js';

// Import API routes after dotenv is configured
import paymentRoutes from './api/create-payment-intent.js';
import stripeWebhookRoutes from './api/stripe-webhook.js';
import paypalRoutes from './api/create-paypal-order.js';
import paypalWebhookRoutes from './api/paypal-webhook.js';

// Get the directory name using ES modules approach
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());

// Apply general rate limiting to all requests
app.use(generalLimiter);

// Special handling for webhooks (needs raw body)
const handleRawBody = async (req, res, next) => {
  if (req.method === 'POST') {
    let rawBody = '';
    req.on('data', (chunk) => {
      rawBody += chunk.toString();
    });
    req.on('end', () => {
      req.rawBody = rawBody;
      next();
    });
  } else {
    next();
  }
};

// Apply raw body handling and rate limiting to webhook endpoints
app.use('/api/stripe-webhook', webhookLimiter, handleRawBody);
app.use('/api/paypal-webhook', webhookLimiter, handleRawBody);

// Regular middleware for other routes
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the public directory first (for development and direct asset access)
app.use(express.static(join(__dirname, '../public')));

// Serve static files from the dist directory (for production build assets)
app.use(express.static(join(__dirname, '../dist')));

// API routes with appropriate rate limiting
app.use('/api', paymentLimiter, paymentRoutes);
app.use('/api', stripeWebhookRoutes);
app.use('/api', paymentLimiter, paypalRoutes);
app.use('/api', paypalWebhookRoutes);

// Serve the index.html for any other requests (for SPA routing)
app.get('*', (req, res) => {
  res.sendFile(join(__dirname, '../dist/index.html'));
});

// Initialize database and start server
const startServer = async () => {
  try {
    // Initialize database connection
    const db = await initDatabase();
    const dbStatus = db ? 'connected' : 'using fallback';
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`✅ Server running on port ${PORT}`);
      console.log(`📊 Database: ${dbStatus}`);
      console.log(`🔌 API available at http://localhost:${PORT}/api`);
      console.log(`💳 Stripe integration: ${process.env.VITE_STRIPE_PUBLISHABLE_KEY ? 'configured' : 'missing'}`);
      console.log(`🔔 Stripe webhook endpoint: http://localhost:${PORT}/api/stripe-webhook`);
      console.log(`💰 PayPal integration: ${process.env.VITE_PAYPAL_CLIENT_ID ? 'configured' : 'missing'}`);
      console.log(`🌐 PayPal environment: ${process.env.VITE_PAYPAL_SANDBOX === 'true' ? 'Sandbox' : 'Production'}`);
      console.log(`🔔 PayPal webhook endpoint: http://localhost:${PORT}/api/paypal-webhook`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
