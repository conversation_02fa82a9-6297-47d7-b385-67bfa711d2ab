import { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="app">
      <h1>Minimal Vite React App</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          If you can see this, Vite + React is working correctly!
        </p>
      </div>
      <div className="info-section">
        <h2>Troubleshooting</h2>
        <p>If this works but your main app doesn't, check:</p>
        <ul>
          <li>Context providers in your main app</li>
          <li>Environment variables</li>
          <li>Auth0 configuration</li>
          <li>Circular dependencies</li>
          <li>Missing assets</li>
        </ul>
      </div>
    </div>
  )
}

export default App
