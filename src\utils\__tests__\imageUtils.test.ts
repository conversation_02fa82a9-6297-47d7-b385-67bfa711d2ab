import {
  normalizePath,
  encodeImagePath,
  decodeImagePath,
  checkImageExists,
  getValidImagePath,
  safelyStoreImagePaths,
  safelyRetrieveImagePaths,
  DEFAULT_FALLBACK_IMAGE,
} from '../imageUtils';

describe('imageUtils', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('normalizePath', () => {
    it('should return fallback image for empty path', () => {
      expect(normalizePath('')).toBe(DEFAULT_FALLBACK_IMAGE);
      expect(normalizePath(null as any)).toBe(DEFAULT_FALLBACK_IMAGE);
      expect(normalizePath(undefined as any)).toBe(DEFAULT_FALLBACK_IMAGE);
    });

    it('should convert relative paths to absolute', () => {
      expect(normalizePath('./image.jpg')).toBe('/image.jpg');
      expect(normalizePath('../images/photo.png')).toBe('/images/photo.png');
    });

    it('should add leading slash to paths without one', () => {
      expect(normalizePath('images/photo.jpg')).toBe('/images/photo.jpg');
    });

    it('should leave absolute paths unchanged', () => {
      expect(normalizePath('/images/photo.jpg')).toBe('/images/photo.jpg');
    });

    it('should leave HTTP URLs unchanged', () => {
      expect(normalizePath('https://example.com/image.jpg')).toBe('https://example.com/image.jpg');
      expect(normalizePath('http://example.com/image.jpg')).toBe('http://example.com/image.jpg');
    });
  });

  describe('encodeImagePath', () => {
    it('should encode special characters in paths', () => {
      const path = '/images/test image with spaces.jpg';
      const encoded = encodeImagePath(path);
      expect(encoded).toBe(encodeURIComponent('/images/test image with spaces.jpg'));
    });

    it('should handle empty paths', () => {
      const encoded = encodeImagePath('');
      expect(encoded).toBe(encodeURIComponent(DEFAULT_FALLBACK_IMAGE));
    });
  });

  describe('decodeImagePath', () => {
    it('should decode encoded paths correctly', () => {
      const originalPath = '/images/test image with spaces.jpg';
      const encoded = encodeURIComponent(originalPath);
      const decoded = decodeImagePath(encoded);
      expect(decoded).toBe(originalPath);
    });

    it('should return fallback for empty encoded path', () => {
      expect(decodeImagePath('')).toBe(DEFAULT_FALLBACK_IMAGE);
    });

    it('should return fallback for invalid encoded path', () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const decoded = decodeImagePath('%invalid%');
      expect(decoded).toBe(DEFAULT_FALLBACK_IMAGE);
      
      consoleSpy.mockRestore();
    });
  });

  describe('checkImageExists', () => {
    it('should resolve true when image loads successfully', async () => {
      // Mock Image constructor to simulate successful load
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        src: '',
      };

      global.Image = jest.fn().mockImplementation(() => {
        setTimeout(() => {
          if (mockImage.onload) {
            mockImage.onload();
          }
        }, 0);
        return mockImage;
      });

      const result = await checkImageExists('/test-image.jpg');
      expect(result).toBe(true);
    });

    it('should resolve false when image fails to load', async () => {
      // Mock Image constructor to simulate failed load
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        src: '',
      };

      global.Image = jest.fn().mockImplementation(() => {
        setTimeout(() => {
          if (mockImage.onerror) {
            mockImage.onerror();
          }
        }, 0);
        return mockImage;
      });

      const result = await checkImageExists('/non-existent-image.jpg');
      expect(result).toBe(false);
    });
  });

  describe('getValidImagePath', () => {
    it('should return original path when image exists', async () => {
      // Mock checkImageExists to return true
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        src: '',
      };

      global.Image = jest.fn().mockImplementation(() => {
        setTimeout(() => {
          if (mockImage.onload) {
            mockImage.onload();
          }
        }, 0);
        return mockImage;
      });

      const result = await getValidImagePath('/existing-image.jpg');
      expect(result).toBe('/existing-image.jpg');
    });

    it('should return fallback path when image does not exist', async () => {
      // Mock checkImageExists to return false
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        src: '',
      };

      global.Image = jest.fn().mockImplementation(() => {
        setTimeout(() => {
          if (mockImage.onerror) {
            mockImage.onerror();
          }
        }, 0);
        return mockImage;
      });

      const result = await getValidImagePath('/non-existent-image.jpg');
      expect(result).toBe(DEFAULT_FALLBACK_IMAGE);
    });

    it('should use custom fallback when provided', async () => {
      // Mock checkImageExists to return false
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        src: '',
      };

      global.Image = jest.fn().mockImplementation(() => {
        setTimeout(() => {
          if (mockImage.onerror) {
            mockImage.onerror();
          }
        }, 0);
        return mockImage;
      });

      const customFallback = '/custom-fallback.jpg';
      const result = await getValidImagePath('/non-existent-image.jpg', customFallback);
      expect(result).toBe(customFallback);
    });
  });

  describe('safelyStoreImagePaths', () => {
    it('should store encoded image paths in localStorage', () => {
      const paths = ['/image1.jpg', '/image2.png', '/image with spaces.gif'];
      
      safelyStoreImagePaths('test-key', paths);
      
      const stored = localStorage.getItem('test-key');
      expect(stored).toBeDefined();
      
      const parsed = JSON.parse(stored!);
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(3);
    });

    it('should handle errors gracefully', () => {
      // Mock localStorage.setItem to throw an error
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = jest.fn().mockImplementation(() => {
        throw new Error('Storage full');
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Should not throw
      expect(() => {
        safelyStoreImagePaths('test-key', ['/image.jpg']);
      }).not.toThrow();
      
      consoleSpy.mockRestore();
      localStorage.setItem = originalSetItem;
    });
  });

  describe('safelyRetrieveImagePaths', () => {
    it('should retrieve and decode image paths from localStorage', () => {
      const originalPaths = ['/image1.jpg', '/image2.png'];
      
      // Store paths first
      safelyStoreImagePaths('test-key', originalPaths);
      
      // Retrieve paths
      const retrieved = safelyRetrieveImagePaths('test-key');
      
      expect(retrieved).toEqual(originalPaths);
    });

    it('should return empty array when key does not exist', () => {
      const retrieved = safelyRetrieveImagePaths('non-existent-key');
      expect(retrieved).toEqual([]);
    });

    it('should return empty array when stored data is not an array', () => {
      localStorage.setItem('invalid-key', JSON.stringify('not-an-array'));
      
      const retrieved = safelyRetrieveImagePaths('invalid-key');
      expect(retrieved).toEqual([]);
    });

    it('should handle JSON parse errors gracefully', () => {
      localStorage.setItem('corrupt-key', 'invalid-json');
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const retrieved = safelyRetrieveImagePaths('corrupt-key');
      expect(retrieved).toEqual([]);
      
      consoleSpy.mockRestore();
    });
  });
}); 