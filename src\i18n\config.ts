/**
 * Internationalization (i18n) configuration for Sabone.store
 *
 * This file contains the configuration for the i18n system, including:
 * - Available locales
 * - Default locale
 * - Functions to load messages
 * - Helper functions for i18n
 */

// Define available locales
export const locales = ['en', 'ar'];
export const defaultLocale = 'en';

// Define locale names for display
export const localeNames = {
  en: 'English',
  ar: 'العربية'
};

// Define RTL locales
export const rtlLocales = ['ar'];

/**
 * Check if a locale is RTL
 * @param locale The locale to check
 * @returns True if the locale is RTL, false otherwise
 */
export const isRTL = (locale: string): boolean => {
  return rtlLocales.includes(locale);
};

/**
 * Get messages for a specific locale
 * @param locale The locale to get messages for
 * @returns A promise that resolves to the messages for the locale
 */
export const getMessages = async (locale: string) => {
  try {
    // Dynamic import of the messages file
    console.log(`Loading messages for locale: ${locale}`);
    // Vite typically resolves JSON imports to the content itself, not an object with a .default
    const messagesModule = await import(`./messages/${locale}.json`);
    // Ensure we handle both cases: module with default export or direct JSON object
    const messages = messagesModule.default || messagesModule;
    console.log(`Successfully loaded messages for ${locale}:`, messages);
    return messages;
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);
    // Fallback to default locale if the requested locale is not available
    if (locale !== defaultLocale) {
      console.warn(`Falling back to default locale (${defaultLocale})`);
      return getMessages(defaultLocale);
    }
    // If even the default locale fails, return an empty object
    return {};
  }
};

// Helper to flatten nested message objects for next-intl
export const flattenMessages = (nestedMessages: Record<string, any>, prefix = '') => {
  return Object.keys(nestedMessages).reduce((acc: Record<string, any>, key: string) => {
    const value = nestedMessages[key];
    const prefixedKey = prefix ? `${prefix}.${key}` : key;

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      Object.assign(acc, flattenMessages(value, prefixedKey));
    } else {
      acc[prefixedKey] = value;
    }
    return acc;
  }, {});
};

/**
 * Get the user's preferred locale
 * @returns The user's preferred locale, or the default locale if not available
 */
export const getUserLocale = (): string => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return defaultLocale;
  }

  // Try to get the locale from localStorage
  const savedLocale = localStorage.getItem('sabone-locale');
  if (savedLocale && locales.includes(savedLocale)) {
    return savedLocale;
  }

  // Try to get the locale from browser settings
  const browserLocale = navigator.language.split('-')[0];
  if (locales.includes(browserLocale)) {
    return browserLocale;
  }

  // Fallback to default locale
  return defaultLocale;
};

/**
 * Set the user's preferred locale
 * @param locale The locale to set
 */
export const setUserLocale = (locale: string): void => {
  if (!locales.includes(locale)) {
    console.warn(`Locale ${locale} is not supported. Using default locale (${defaultLocale})`);
    locale = defaultLocale;
  }

  // Save the locale to localStorage
  try {
    localStorage.setItem('sabone-locale', locale);
  } catch (error) {
    console.error('Error saving locale to localStorage:', error);
  }

  // Removed RTL handling - we'll only change the language without flipping the layout
  console.log(`Language switched to ${locale} without changing text direction`);
};
