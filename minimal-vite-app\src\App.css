.app {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  background-color: #2a2a1f;
  border-radius: 8px;
  border: 1px solid #c6a870;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #c6a87066);
}

.card {
  padding: 2em;
}

.info-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #333328;
  border-radius: 8px;
  text-align: left;
}

.info-section h2 {
  color: #c6a870;
  margin-top: 0;
}

.info-section ul {
  padding-left: 1.5rem;
}

.info-section li {
  margin-bottom: 0.5rem;
}
