/**
 * RTL-specific fixes for the Product Grid component
 * These styles help ensure proper rendering in Arabic locale
 */

/* Force proper text alignment in product grid headings */
.rtl #products-section h2,
.rtl #products-section p {
  text-align: center !important;
}

/* Force proper alignment for product grid tabs */
.rtl .tabs-list {
  direction: rtl !important;
  justify-content: center !important;
}

/* Ensure product cards display correctly */
.rtl .product-card {
  direction: rtl !important;
}

/* Fix tab trigger elements */
.rtl [data-state="active"] {
  direction: rtl !important;
  text-align: center !important;
}

/* Force re-render of product titles and descriptions */
.rtl #products-section * {
  text-rendering: geometricPrecision !important;
} 