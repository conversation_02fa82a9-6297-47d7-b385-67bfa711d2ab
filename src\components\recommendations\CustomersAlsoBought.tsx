import React, { useState, useEffect } from 'react';
import { Product } from '@/data/products';
import { useRecommendations } from '@/contexts/RecommendationContext';
import RecommendationSection from './RecommendationSection';

interface CustomersAlsoBoughtProps {
  productId: string;
  className?: string;
  maxVisible?: number;
}

const CustomersAlsoBought: React.FC<CustomersAlsoBoughtProps> = ({
  productId,
  className = '',
  maxVisible = 4
}) => {
  const [recommendations, setRecommendations] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { getCustomersAlsoBought } = useRecommendations();

  const fetchRecommendations = async () => {
    if (!productId) return;
    
    try {
      setLoading(true);
      setError(null);
      const products = await getCustomersAlsoBought(productId);
      setRecommendations(products);
    } catch (err) {
      setError('Failed to load recommendations');
      console.error('Error fetching customers also bought:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, [productId, getCustomersAlsoBought]);

  return (
    <RecommendationSection
      title="Customers Also Bought"
      subtitle="Products frequently purchased together"
      products={recommendations}
      loading={loading}
      error={error}
      onRefresh={fetchRecommendations}
      maxVisible={maxVisible}
      showRefreshButton={true}
      className={className}
      trackingType="customers_also_bought"
    />
  );
};

export default CustomersAlsoBought;
