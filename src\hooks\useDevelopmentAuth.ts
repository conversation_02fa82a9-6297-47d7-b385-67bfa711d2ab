import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

interface User {
  sub?: string;
  name?: string;
  email?: string;
  picture?: string;
  phone_number?: string;
  role?: 'user' | 'admin' | 'affiliate';
}

interface UseDevelopmentAuthResult {
  devUser: User | null;
  isDevelopmentMode: boolean;
  hasLoggedOut: boolean;
  devLogin: () => void;
  devRegister: () => void;
  devLogout: (callback?: () => void) => void;
  setAsAdmin: () => void;
  updateDevUser: (updates: Partial<User>) => void;
}

export const useDevelopmentAuth = (): UseDevelopmentAuthResult => {
  const isDevelopmentMode = import.meta.env.DEV && import.meta.env.VITE_SKIP_AUTH === 'true';
  const hasLoggedOut = localStorage.getItem('sabone-dev-logged-out') === 'true';
  const [devUser, setDevUser] = useState<User | null>(null);

  // Initialize development user
  useEffect(() => {
    if (isDevelopmentMode && !hasLoggedOut) {
      const isAdmin = localStorage.getItem('sabone-dev-admin') === 'true';
      setDevUser({
        sub: 'dev-user-123',
        name: 'Development User',
        email: isAdmin ? '<EMAIL>' : '<EMAIL>',
        role: isAdmin ? 'admin' : 'user'
      });
    }
  }, [isDevelopmentMode, hasLoggedOut]);

  const devLogin = useCallback(() => {
    if (!isDevelopmentMode) return;
    
    localStorage.removeItem('sabone-dev-logged-out');
    setDevUser({
      sub: 'dev-user-123',
      name: 'Development User',
      email: '<EMAIL>',
      role: 'user'
    });
    toast.success('Development mode: Logged in as Development User');
  }, [isDevelopmentMode]);

  const devRegister = useCallback(() => {
    if (!isDevelopmentMode) return;
    
    localStorage.removeItem('sabone-dev-logged-out');
    setDevUser({
      sub: 'dev-user-123',
      name: 'Development User',
      email: '<EMAIL>',
      role: 'user'
    });
    toast.success('Development mode: Account created successfully!');
  }, [isDevelopmentMode]);

  const devLogout = useCallback((callback?: () => void) => {
    if (!isDevelopmentMode) return;
    
    localStorage.setItem('sabone-dev-logged-out', 'true');
    localStorage.removeItem('sabone-dev-admin');
    setDevUser(null);
    toast.info('Development mode: Logged out');

    if (callback) {
      setTimeout(callback, 100);
    }
  }, [isDevelopmentMode]);

  const setAsAdmin = useCallback(() => {
    if (!isDevelopmentMode || !devUser) {
      toast.error('This function is only available in development mode when logged in');
      return;
    }

    setDevUser(prevUser => 
      prevUser ? { ...prevUser, role: 'admin', email: '<EMAIL>' } : null
    );
    localStorage.setItem('sabone-dev-admin', 'true');
    toast.success('Development user has been granted admin privileges');
  }, [isDevelopmentMode, devUser]);

  const updateDevUser = useCallback((updates: Partial<User>) => {
    if (!isDevelopmentMode) return;
    
    setDevUser(prevUser => 
      prevUser ? { ...prevUser, ...updates } : null
    );
  }, [isDevelopmentMode]);

  return {
    devUser,
    isDevelopmentMode,
    hasLoggedOut,
    devLogin,
    devRegister,
    devLogout,
    setAsAdmin,
    updateDevUser
  };
};

export default useDevelopmentAuth; 