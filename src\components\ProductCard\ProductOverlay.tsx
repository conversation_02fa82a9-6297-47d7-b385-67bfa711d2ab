import { Plus } from "lucide-react";
import { ProductOverlayProps } from "./types";
import ProductPrice from "./ProductPrice";

const ProductOverlay = ({ product, isHovered, onAddToCart }: ProductOverlayProps) => {
  return (
    <figcaption
      className={`absolute bottom-4 left-4 right-4 bg-sabone-charcoal-deep/60 backdrop-blur-[4px] p-3 rounded-md flex flex-col justify-between transition-all duration-300 ${
        isHovered ? 'max-h-[35%] opacity-100 translate-y-0' : 'max-h-[30%] opacity-95 translate-y-1'
      }`}
      aria-label={`${product.name} details`}
    >
      <div>
        <header>
          <h3 className="text-base font-playfair font-medium text-sabone-gold truncate">
            {product.name}
          </h3>
        </header>

        <p className="text-sm text-sabone-cream/90 line-clamp-1 min-h-[1.25rem]">
          {product.description}
        </p>
      </div>

      <div className="flex justify-between items-center mt-2">
        <ProductPrice price={product.price} className="text-sm md:text-base" />

        {/* Using div instead of <PERSON><PERSON> to avoid nested button warning */}
        <div
          className="h-9 w-9 rounded-full bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80 min-h-[44px] min-w-[44px] shadow-sm inline-flex items-center justify-center cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            onAddToCart(e);
          }}
          aria-label={`Add ${product.name} to cart`}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              e.stopPropagation();
              onAddToCart();
            }
          }}
        >
          <Plus className="h-4 w-4" />
        </div>
      </div>
    </figcaption>
  );
};

export default ProductOverlay;
