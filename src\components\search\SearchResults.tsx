import React from 'react';
import { Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import ProductCard from '@/components/ProductCard';
import { Pagination } from '@/components/ui/pagination';
import { useSearch } from '@/contexts/SearchContext';
import { highlightSearchTerms } from '@/utils/searchUtils';
import { cn } from '@/lib/utils';

interface SearchResultsProps {
  className?: string;
  showPagination?: boolean;
  gridCols?: 2 | 3 | 4;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  className,
  showPagination = true,
  gridCols = 3,
}) => {
  const {
    query,
    paginatedResults,
    totalResults,
    isSearching,
    hasActiveFilters,
    clearFilters,
    clearSearch,
    currentPage,
    totalPages,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage,
  } = useSearch();

  const hasQuery = query.trim().length > 0;
  const hasResults = totalResults > 0;

  // Loading state
  if (isSearching) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2 text-sabone-cream/60">
            <div className="animate-spin h-5 w-5 border-2 border-sabone-gold border-t-transparent rounded-full" />
            <span>Searching products...</span>
          </div>
        </div>
        
        {/* Loading skeleton */}
        <div className={cn(
          "grid gap-6",
          gridCols === 2 && "grid-cols-1 sm:grid-cols-2",
          gridCols === 3 && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
          gridCols === 4 && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        )}>
          {Array.from({ length: itemsPerPage }).map((_, index) => (
            <Card key={index} className="bg-sabone-charcoal border-sabone-gold/30">
              <CardContent className="p-4">
                <Skeleton className="h-48 w-full mb-4 bg-sabone-dark-olive/30" />
                <Skeleton className="h-4 w-3/4 mb-2 bg-sabone-dark-olive/30" />
                <Skeleton className="h-4 w-1/2 bg-sabone-dark-olive/30" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // No results state
  if (!hasResults && (hasQuery || hasActiveFilters)) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card className="bg-sabone-charcoal border-sabone-gold/30">
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="h-16 w-16 rounded-full bg-sabone-gold/10 flex items-center justify-center">
                <Search className="h-8 w-8 text-sabone-gold/60" />
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-sabone-cream">
                  No products found
                </h3>
                <p className="text-sabone-cream/60 max-w-md">
                  {hasQuery && hasActiveFilters
                    ? `No products match your search "${query}" with the current filters.`
                    : hasQuery
                    ? `No products found for "${query}".`
                    : "No products match your current filters."
                  }
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Clear Filters
                  </Button>
                )}
                {hasQuery && (
                  <Button
                    variant="outline"
                    onClick={clearSearch}
                    className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Clear Search
                  </Button>
                )}
              </div>

              <div className="text-sm text-sabone-cream/60 mt-4">
                <p>Try:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Checking your spelling</li>
                  <li>Using different keywords</li>
                  <li>Removing some filters</li>
                  <li>Browsing our categories</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Results header
  const ResultsHeader = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="space-y-1">
        {hasQuery && (
          <h2 className="text-xl font-semibold text-sabone-cream">
            Search results for{' '}
            <span 
              className="text-sabone-gold"
              dangerouslySetInnerHTML={{ 
                __html: `"${highlightSearchTerms(query, query)}"` 
              }}
            />
          </h2>
        )}
        <p className="text-sabone-cream/60">
          {totalResults} {totalResults === 1 ? 'product' : 'products'} found
          {hasActiveFilters && ' with current filters'}
        </p>
      </div>

      {/* Items per page selector */}
      <div className="hidden sm:flex items-center space-x-2">
        <span className="text-sm text-sabone-cream/60">Show:</span>
        {[12, 24, 48].map((count) => (
          <Button
            key={count}
            variant={itemsPerPage === count ? "default" : "ghost"}
            size="sm"
            onClick={() => setItemsPerPage(count)}
            className={cn(
              "text-xs",
              itemsPerPage === count
                ? "bg-sabone-gold text-sabone-charcoal"
                : "text-sabone-cream hover:bg-sabone-gold/10"
            )}
          >
            {count}
          </Button>
        ))}
      </div>
    </div>
  );

  // Results grid
  const ResultsGrid = () => (
    <div className={cn(
      "grid gap-6",
      gridCols === 2 && "grid-cols-1 sm:grid-cols-2",
      gridCols === 3 && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
      gridCols === 4 && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    )}>
      {paginatedResults.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          className="transform transition-all duration-200 hover:scale-105"
        />
      ))}
    </div>
  );

  // Pagination component
  const PaginationComponent = () => {
    if (!showPagination || totalPages <= 1) return null;

    return (
      <div className="flex items-center justify-center mt-8">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          className="text-sabone-cream"
        />
      </div>
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {hasResults && <ResultsHeader />}
      {hasResults && <ResultsGrid />}
      {hasResults && <PaginationComponent />}
    </div>
  );
};

export default SearchResults;
