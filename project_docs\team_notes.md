# 📝 Sabone.store Team Notes

This document provides guidance and conventions for developers working on the Sabone.store project. It includes information about file structure, naming conventions, design decisions, and best practices.

## 🏗️ Project Architecture

### File Structure

The project follows a feature-based organization with shared components:

```
src/
├── components/         # Reusable UI components
│   ├── ui/             # Base UI components (shadcn/ui)
│   ├── [feature]/      # Feature-specific components
├── contexts/           # React Context providers
├── data/               # Static data and mock data
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and helpers
├── pages/              # Page components
├── services/           # Service functions for data operations
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

### Key Architectural Decisions

1. **React Context for State Management**
   - We use React Context API for state management instead of Redux
   - Each domain has its own context provider (Cart, Auth, Products, etc.)
   - Contexts are composed in App.tsx in a specific order to handle dependencies

2. **Component Composition**
   - Prefer composition over inheritance
   - Use smaller, focused components that can be composed together
   - Implement container/presenter pattern where appropriate

3. **Data Flow**
   - Unidirectional data flow (parent to child)
   - Use context for cross-component communication
   - Avoid prop drilling more than 2-3 levels deep

## 🎨 Design System

### UI Components

We use shadcn/ui as our component library, which is built on top of Radix UI:

- **Installation**: `npx shadcn-ui@latest add [component-name]`
- **Customization**: Modify the component in `src/components/ui/[component-name].tsx`
- **Documentation**: [https://ui.shadcn.com/docs](https://ui.shadcn.com/docs)

### Theme and Styling

The project uses Tailwind CSS with a custom theme defined in `tailwind.config.ts`:

- **Color Palette**:
  - Primary colors: Dark theme with gold accents
  - `sabone-charcoal`: Primary background color
  - `sabone-gold`: Primary accent color
  - See `tailwind.config.ts` for the complete color palette

- **Typography**:
  - Headings: Playfair Display (serif)
  - Body text: Montserrat (sans-serif)
  - See `src/index.css` for base typography styles

- **Custom Utilities**:
  - Use the utility classes defined in `src/index.css`
  - Create new utilities in the `@layer utilities` section when needed

### Design Principles

1. **Luxury Aesthetic**
   - Dark backgrounds with gold accents
   - Subtle animations and transitions
   - Generous whitespace
   - High-quality imagery

2. **Responsive Design**
   - Mobile-first approach
   - Tailored layouts for different screen sizes
   - Touch-friendly interactions on mobile

3. **Accessibility**
   - Maintain sufficient color contrast
   - Provide text alternatives for images
   - Ensure keyboard navigability
   - Use semantic HTML elements

## 🧩 Coding Conventions

### Naming Conventions

- **Files and Folders**:
  - React components: PascalCase (e.g., `ProductCard.tsx`)
  - Utilities and hooks: camelCase (e.g., `useCart.ts`)
  - Context files: PascalCase with Context suffix (e.g., `CartContext.tsx`)

- **Component Props**:
  - Use PascalCase for prop interface names with Props suffix (e.g., `ProductCardProps`)
  - Use camelCase for prop names

- **CSS Classes**:
  - Use kebab-case for custom class names
  - Follow Tailwind's utility-first approach

### TypeScript Best Practices

- Define interfaces for all component props
- Use type inference where possible
- Avoid using `any` type
- Use union types for variants and states
- Define reusable types in `src/types/` directory

Example:
```typescript
// Good
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  children: React.ReactNode;
}

// Avoid
interface ButtonProps {
  variant: any;
  size: any;
  onClick: any;
  children: any;
}
```

### Component Structure

- Use functional components with hooks
- Destructure props at the beginning
- Define local state and effects next
- Define handler functions before the return statement
- Keep components focused on a single responsibility

Example:
```tsx
const ProductCard = ({ product, onAddToCart }: ProductCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAddToCart(product);
  };
  
  return (
    <div 
      className="product-card"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Component content */}
    </div>
  );
};
```

## 🔄 State Management

### Context Usage Guidelines

- Create a context file for each domain (e.g., `CartContext.tsx`)
- Export a provider component and a custom hook
- Handle loading and error states within the context
- Implement proper TypeScript typing

Example:
```tsx
// Create context with undefined as initial value
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component
export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State and functions
  
  return (
    <CartContext.Provider value={/* context value */}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook with proper error handling
export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
```

### Local State Management

- Use `useState` for simple component state
- Use `useReducer` for complex state logic
- Consider using custom hooks to encapsulate related state and logic

## 🧪 Testing Guidelines

### Component Testing

- Test component rendering and basic functionality
- Test user interactions (clicks, form submissions, etc.)
- Mock context providers and external dependencies

Example:
```tsx
test('adds item to cart when button is clicked', () => {
  const mockAddToCart = jest.fn();
  const product = { id: '1', name: 'Test Product', price: 10 };
  
  render(
    <CartProvider>
      <ProductCard product={product} onAddToCart={mockAddToCart} />
    </CartProvider>
  );
  
  fireEvent.click(screen.getByText('Add to Cart'));
  
  expect(mockAddToCart).toHaveBeenCalledWith(product);
});
```

### Hook Testing

- Test custom hooks with `renderHook` from `@testing-library/react-hooks`
- Test state updates and side effects
- Mock external dependencies

## 🚀 Performance Considerations

- Use React.memo for components that render often with the same props
- Implement virtualization for long lists (react-window or react-virtualized)
- Use code splitting with React.lazy and Suspense
- Optimize images with appropriate formats and sizes
- Minimize re-renders by avoiding unnecessary state updates

## 🔒 Security Best Practices

- Validate all user inputs
- Sanitize data before rendering to prevent XSS
- Use Auth0 for authentication and authorization
- Never store sensitive information in localStorage
- Implement proper CSRF protection
- Use HTTPS for all API requests

## 🌐 Internationalization (i18n)

- Use next-intl for translations
- Keep translation keys organized by feature
- Use ICU message format for complex translations
- Consider RTL layout support for Arabic language

## 📱 Responsive Design Guidelines

- Use Tailwind's responsive prefixes (sm, md, lg, xl)
- Test on various device sizes
- Implement different layouts for mobile and desktop where appropriate
- Use the `useIsMobile` hook for conditional rendering based on screen size
