import React from 'react';
import { useRTL } from '@/hooks/rtl-hooks';
import { Badge } from '@/components/ui/badge';
import { ArrowLeftRight } from 'lucide-react';

interface DirectionIndicatorProps {
  className?: string;
  showIcon?: boolean;
}

/**
 * Component to display the current text direction (RTL/LTR)
 */
export const DirectionIndicator: React.FC<DirectionIndicatorProps> = ({
  className = '',
  showIcon = true
}) => {
  const { isRTL, dir } = useRTL();

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showIcon && <ArrowLeftRight className="h-4 w-4 text-sabone-gold" />}
      <Badge
        variant="outline"
        className={`
          border-sabone-gold/30
          ${isRTL
            ? 'bg-sabone-dark-olive/70 text-sabone-gold'
            : 'bg-sabone-dark-olive/30 text-sabone-cream/70'
          }
        `}
      >
        {dir.toUpperCase()}
      </Badge>
    </div>
  );
};

export default DirectionIndicator;
