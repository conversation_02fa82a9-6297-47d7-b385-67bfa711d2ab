
import { useEffect, Suspense } from "react";
import Hero from "@/components/Hero";
import Navbar from "@/components/Navbar";
import ProductGrid from "@/components/ProductGrid";
import Footer from "@/components/Footer";
import { HelmetProvider } from 'react-helmet-async';
import { LazyBundlesSection, LazyAbout, LazyContact, LazyRecommendedForYou, LazyRecentlyViewed, LazyTrendingProducts } from "@/components/LazyComponents";
import { Skeleton } from "@/components/ui/skeleton";
import SEO from "@/components/seo/SEO";

const Index = () => {
  useEffect(() => {
    // Safely add class to body
    try {
      if (document.body && !document.body.classList.contains("bg-sabone-charcoal")) {
        document.body.classList.add("bg-sabone-charcoal");
      }
    } catch (error) {
      console.error("Error adding class to body:", error);
    }

    return () => {
      // Safely remove class from body
      try {
        if (document.body && document.body.classList.contains("bg-sabone-charcoal")) {
          document.body.classList.remove("bg-sabone-charcoal");
        }
      } catch (error) {
        console.error("Error removing class from body:", error);
      }
    };
  }, []);

  return (
    <HelmetProvider>
      <SEO
        title="Sabone | Luxury Natural Soaps & Shampoos Inspired by Arabic Traditions"
        description="Discover Sabone's handcrafted natural soaps and shampoos inspired by ancient Arabic rituals. Experience the luxury of clean beauty rooted in tradition and ritual purity."
        canonical=""
        keywords={[
          "natural soap",
          "organic shampoo",
          "handcrafted soap",
          "Arabic beauty",
          "hammam",
          "natural beauty",
          "artisan soap",
          "ritual purity",
          "luxury soap"
        ]}
        image="/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png"
        schemaMarkup={[
          // Organization Schema
          {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Sabone",
            "url": "https://sabone.store",
            "logo": "https://sabone.store/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png",
            "description": "Luxury natural soaps and shampoos inspired by ancient Arabic rituals.",
            "contactPoint": {
              "@type": "ContactPoint",
              "email": "<EMAIL>",
              "contactType": "customer service"
            },
            "sameAs": [
              "https://instagram.com/sabone.store",
              "https://facebook.com/sabone.store"
            ]
          },
          // Product Schema
          {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": "Sabone Natural Soaps",
            "description": "Handcrafted natural soaps and shampoos inspired by ancient Arabic rituals.",
            "brand": {
              "@type": "Brand",
              "name": "Sabone"
            },
            "offers": {
              "@type": "AggregateOffer",
              "priceCurrency": "USD",
              "lowPrice": "12.99",
              "highPrice": "26.99",
              "offerCount": "9"
            }
          }
        ]}
      />

      <div id="products" className="min-h-screen bg-sabone-charcoal bg-[radial-gradient(ellipse_at_center,_rgba(50,50,40,0.1)_0%,_transparent_70%)]">
        <Navbar />
        <Hero />
        <ProductGrid />

        {/* Recommendation Sections */}
        <Suspense fallback={
          <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
            <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
          </div>
        }>
          <LazyRecommendedForYou />
        </Suspense>

        <Suspense fallback={
          <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
            <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
          </div>
        }>
          <LazyRecentlyViewed />
        </Suspense>

        <Suspense fallback={
          <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
            <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
          </div>
        }>
          <LazyTrendingProducts />
        </Suspense>

        {/* Lazy load below-the-fold content */}
        <Suspense fallback={
          <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
            <Skeleton className="h-[400px] w-full rounded-md bg-sabone-dark-olive/30" />
          </div>
        }>
          <LazyBundlesSection />
        </Suspense>

        <Suspense fallback={
          <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
            <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
          </div>
        }>
          <LazyAbout />
        </Suspense>

        <Suspense fallback={
          <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
            <Skeleton className="h-[250px] w-full rounded-md bg-sabone-dark-olive/30" />
          </div>
        }>
          <LazyContact />
        </Suspense>

        <Footer />
      </div>
    </HelmetProvider>
  );
};

export default Index;
