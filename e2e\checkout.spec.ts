import { test, expect } from '@playwright/test';

test.describe('Checkout Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set development mode for testing
    await page.addInitScript(() => {
      window.localStorage.setItem('sabone-dev-logged-out', 'false');
    });
    
    await page.goto('/');
  });

  test('should add item to cart and navigate to checkout', async ({ page }) => {
    // Look for product or add to cart button
    const addToCartButton = page.getByRole('button', { name: /add to cart/i }).first();
    
    if (await addToCartButton.isVisible()) {
      await addToCartButton.click();
      
      // Check cart update
      await expect(page.getByText(/added to cart|cart updated/i)).toBeVisible();
      
      // Navigate to checkout
      const cartIcon = page.getByRole('button', { name: /cart/i });
      await cartIcon.click();
      
      const checkoutButton = page.getByRole('button', { name: /checkout/i });
      if (await checkoutButton.isVisible()) {
        await checkoutButton.click();
        
        // Verify we're on checkout page
        await expect(page).toHaveURL(/checkout/);
      }
    }
  });

  test('should display checkout form fields', async ({ page }) => {
    await page.goto('/checkout');
    
    // Check for essential checkout form fields
    const nameField = page.getByLabel(/name|full name/i);
    const emailField = page.getByLabel(/email/i);
    const addressField = page.getByLabel(/address/i);
    
    if (await nameField.isVisible()) {
      await expect(nameField).toBeVisible();
    }
    
    if (await emailField.isVisible()) {
      await expect(emailField).toBeVisible();
    }
    
    if (await addressField.isVisible()) {
      await expect(addressField).toBeVisible();
    }
  });

  test('should handle payment method selection', async ({ page }) => {
    await page.goto('/checkout');
    
    // Look for payment method options
    const stripeOption = page.getByText(/stripe|credit card/i);
    const paypalOption = page.getByText(/paypal/i);
    
    if (await stripeOption.isVisible()) {
      await stripeOption.click();
      await expect(stripeOption).toBeChecked();
    }
    
    if (await paypalOption.isVisible()) {
      await paypalOption.click();
      await expect(paypalOption).toBeChecked();
    }
  });

  test('should validate required fields before payment', async ({ page }) => {
    await page.goto('/checkout');
    
    // Try to submit without filling required fields
    const submitButton = page.getByRole('button', { name: /place order|pay now|complete order/i });
    
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Should show validation errors
      const errorMessage = page.getByText(/required|please fill|invalid/i);
      if (await errorMessage.isVisible()) {
        await expect(errorMessage).toBeVisible();
      }
    }
  });
}); 