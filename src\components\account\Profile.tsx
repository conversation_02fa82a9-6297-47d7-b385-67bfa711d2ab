import { useState, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Camera, Upload, Loader2 } from "lucide-react";

// Form validation schema
const profileFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }).optional(),
  phone: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const Profile = () => {
  const { user, updateProfile, updateProfilePicture } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false);
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize form with user data
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
      phone: user?.phone_number || "",
    },
  });

  // Function to get initials from user name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Handle profile picture file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Preview the image
    const reader = new FileReader();
    reader.onload = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Handle profile picture upload
  const handleUploadPicture = async () => {
    const file = fileInputRef.current?.files?.[0];
    if (!file) {
      toast.error("Please select an image to upload");
      return;
    }

    setIsUploadingPhoto(true);
    try {
      const picturePath = await updateProfilePicture(file);
      if (picturePath) {
        setPhotoDialogOpen(false);
        setImagePreview(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    } catch (error) {
      console.error("Error uploading profile picture:", error);
      toast.error("Failed to upload profile picture");
    } finally {
      setIsUploadingPhoto(false);
    }
  };

  // Handle form submission
  const onSubmit = async (data: ProfileFormValues) => {
    try {
      const success = await updateProfile({
        name: data.name,
        phone_number: data.phone
      });

      if (success) {
        setIsEditing(false);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Profile Information</h2>
        {!isEditing && (
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => setIsEditing(true)}
          >
            Edit Profile
          </Button>
        )}
      </div>

      <Separator className="bg-sabone-gold/20" />

      <div className="flex flex-col md:flex-row gap-8 items-start">
        <div className="flex flex-col items-center space-y-3">
          <div className="relative group">
            <Avatar className="h-24 w-24 border-2 border-sabone-gold/30">
              <AvatarImage src={user?.picture} alt={user?.name} />
              <AvatarFallback className="bg-sabone-gold/20 text-sabone-gold text-xl">
                {user?.name ? getInitials(user.name) : 'U'}
              </AvatarFallback>
            </Avatar>

            <Dialog open={photoDialogOpen} onOpenChange={setPhotoDialogOpen}>
              <DialogTrigger asChild>
                <button
                  className="absolute bottom-0 right-0 bg-sabone-gold text-sabone-charcoal rounded-full p-1.5 shadow-md hover:bg-sabone-gold/80 transition-colors"
                  aria-label="Change profile picture"
                >
                  <Camera className="h-4 w-4" />
                </button>
              </DialogTrigger>
              <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
                <DialogHeader>
                  <DialogTitle className="text-sabone-gold">Update Profile Picture</DialogTitle>
                </DialogHeader>

                <div className="space-y-4 py-4">
                  <div className="flex justify-center">
                    <Avatar className="h-32 w-32 border-2 border-sabone-gold/30">
                      <AvatarImage
                        src={imagePreview || user?.picture}
                        alt={user?.name}
                      />
                      <AvatarFallback className="bg-sabone-gold/20 text-sabone-gold text-2xl">
                        {user?.name ? getInitials(user.name) : 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-sabone-gold/30 rounded-md cursor-pointer bg-sabone-charcoal/50 hover:bg-sabone-charcoal transition-colors">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="h-8 w-8 text-sabone-gold/70 mb-2" />
                      <p className="text-sm text-sabone-cream/70">
                        <span className="font-medium text-sabone-gold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-sabone-cream/70 mt-1">
                        PNG, JPG or WEBP (max 2MB)
                      </p>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleFileChange}
                    />
                  </label>
                </div>

                <DialogFooter>
                  <Button
                    variant="outline"
                    className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={() => {
                      setPhotoDialogOpen(false);
                      setImagePreview(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                    onClick={handleUploadPicture}
                    disabled={isUploadingPhoto || !imagePreview}
                  >
                    {isUploadingPhoto ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      'Upload Picture'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          <p className="text-sabone-cream/70 text-sm">Profile Picture</p>
        </div>

        <div className="flex-1">
          {isEditing ? (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Full Name</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          disabled
                          className="bg-sabone-charcoal/50 border-sabone-gold/20 text-sabone-cream/70"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sabone-cream">Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex space-x-3">
                  <Button
                    type="submit"
                    className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                  >
                    Save Changes
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Form>
          ) : (
            <div className="space-y-4">
              <div>
                <p className="text-sabone-cream/70 text-sm">Full Name</p>
                <p className="text-sabone-cream">{user?.name}</p>
              </div>

              <div>
                <p className="text-sabone-cream/70 text-sm">Email</p>
                <p className="text-sabone-cream">{user?.email}</p>
              </div>

              <div>
                <p className="text-sabone-cream/70 text-sm">Phone Number</p>
                <p className="text-sabone-cream">{user?.phone_number || "Not provided"}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
