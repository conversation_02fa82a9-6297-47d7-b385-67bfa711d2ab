import { Review } from '@/types/review';
import { logger } from '@/utils/logger';

const VOTES_STORAGE_KEY = 'sabone-review-votes';
const USER_VOTES_KEY = 'sabone-user-votes';

interface ReviewVotes {
  [reviewId: string]: {
    helpful: number;
    unhelpful: number;
    userVotes: Record<string, 'helpful' | 'unhelpful'>;
  };
}

interface UserVoteHistory {
  [userId: string]: {
    [reviewId: string]: 'helpful' | 'unhelpful';
  };
}

/**
 * Get all review votes from storage
 */
const getReviewVotes = (): ReviewVotes => {
  try {
    const votesJson = localStorage.getItem(VOTES_STORAGE_KEY);
    return votesJson ? JSON.parse(votesJson) : {};
  } catch (error) {
    logger.error('Error getting review votes', error);
    return {};
  }
};

/**
 * Save review votes to storage
 */
const saveReviewVotes = (votes: ReviewVotes): void => {
  try {
    localStorage.setItem(VOTES_STORAGE_KEY, JSON.stringify(votes));
  } catch (error) {
    logger.error('Error saving review votes', error);
  }
};

/**
 * Get user vote history from storage
 */
const getUserVoteHistory = (): UserVoteHistory => {
  try {
    const historyJson = localStorage.getItem(USER_VOTES_KEY);
    return historyJson ? JSON.parse(historyJson) : {};
  } catch (error) {
    logger.error('Error getting user vote history', error);
    return {};
  }
};

/**
 * Save user vote history to storage
 */
const saveUserVoteHistory = (history: UserVoteHistory): void => {
  try {
    localStorage.setItem(USER_VOTES_KEY, JSON.stringify(history));
  } catch (error) {
    logger.error('Error saving user vote history', error);
  }
};

/**
 * Vote on a review's helpfulness
 */
export const voteOnReview = (
  reviewId: string, 
  userId: string, 
  voteType: 'helpful' | 'unhelpful'
): { success: boolean; newCounts: { helpful: number; unhelpful: number } } => {
  try {
    const votes = getReviewVotes();
    const userHistory = getUserVoteHistory();

    // Initialize review votes if not exists
    if (!votes[reviewId]) {
      votes[reviewId] = {
        helpful: 0,
        unhelpful: 0,
        userVotes: {}
      };
    }

    // Initialize user history if not exists
    if (!userHistory[userId]) {
      userHistory[userId] = {};
    }

    const currentVote = userHistory[userId][reviewId];
    const reviewVotes = votes[reviewId];

    // If user already voted, remove the previous vote
    if (currentVote) {
      if (currentVote === 'helpful') {
        reviewVotes.helpful = Math.max(0, reviewVotes.helpful - 1);
      } else {
        reviewVotes.unhelpful = Math.max(0, reviewVotes.unhelpful - 1);
      }
      delete reviewVotes.userVotes[userId];
    }

    // If the new vote is different from the current vote (or no current vote), add it
    if (currentVote !== voteType) {
      if (voteType === 'helpful') {
        reviewVotes.helpful++;
      } else {
        reviewVotes.unhelpful++;
      }
      reviewVotes.userVotes[userId] = voteType;
      userHistory[userId][reviewId] = voteType;
    } else {
      // If same vote, remove it (toggle off)
      delete userHistory[userId][reviewId];
    }

    // Save updated data
    saveReviewVotes(votes);
    saveUserVoteHistory(userHistory);

    logger.userAction('review_vote', {
      reviewId,
      userId,
      voteType: currentVote !== voteType ? voteType : 'removed',
      newHelpfulCount: reviewVotes.helpful,
      newUnhelpfulCount: reviewVotes.unhelpful
    });

    return {
      success: true,
      newCounts: {
        helpful: reviewVotes.helpful,
        unhelpful: reviewVotes.unhelpful
      }
    };
  } catch (error) {
    logger.error('Error voting on review', error);
    return {
      success: false,
      newCounts: { helpful: 0, unhelpful: 0 }
    };
  }
};

/**
 * Get vote counts for a specific review
 */
export const getReviewVoteCounts = (reviewId: string): { helpful: number; unhelpful: number } => {
  try {
    const votes = getReviewVotes();
    const reviewVotes = votes[reviewId];
    
    if (!reviewVotes) {
      return { helpful: 0, unhelpful: 0 };
    }

    return {
      helpful: reviewVotes.helpful,
      unhelpful: reviewVotes.unhelpful
    };
  } catch (error) {
    logger.error('Error getting review vote counts', error);
    return { helpful: 0, unhelpful: 0 };
  }
};

/**
 * Get user's vote for a specific review
 */
export const getUserVoteForReview = (reviewId: string, userId: string): 'helpful' | 'unhelpful' | null => {
  try {
    const userHistory = getUserVoteHistory();
    return userHistory[userId]?.[reviewId] || null;
  } catch (error) {
    logger.error('Error getting user vote for review', error);
    return null;
  }
};

/**
 * Get vote statistics for multiple reviews
 */
export const getBulkReviewVotes = (reviewIds: string[]): Record<string, { helpful: number; unhelpful: number }> => {
  try {
    const votes = getReviewVotes();
    const result: Record<string, { helpful: number; unhelpful: number }> = {};

    reviewIds.forEach(reviewId => {
      const reviewVotes = votes[reviewId];
      result[reviewId] = reviewVotes 
        ? { helpful: reviewVotes.helpful, unhelpful: reviewVotes.unhelpful }
        : { helpful: 0, unhelpful: 0 };
    });

    return result;
  } catch (error) {
    logger.error('Error getting bulk review votes', error);
    return {};
  }
};

/**
 * Calculate helpfulness ratio for a review
 */
export const calculateHelpfulnessRatio = (helpful: number, unhelpful: number): number => {
  const total = helpful + unhelpful;
  if (total === 0) return 0;
  return helpful / total;
};

/**
 * Get most helpful reviews for a product
 */
export const getMostHelpfulReviews = (reviews: Review[], limit: number = 5): Review[] => {
  try {
    const votes = getReviewVotes();
    
    const reviewsWithHelpfulness = reviews.map(review => {
      const reviewVotes = votes[review.id];
      const helpful = reviewVotes?.helpful || 0;
      const unhelpful = reviewVotes?.unhelpful || 0;
      const ratio = calculateHelpfulnessRatio(helpful, unhelpful);
      const totalVotes = helpful + unhelpful;
      
      // Score combines helpfulness ratio with total votes (Wilson score approximation)
      const score = totalVotes > 0 ? ratio * Math.log(totalVotes + 1) : 0;
      
      return {
        ...review,
        helpfulVotes: helpful,
        unhelpfulVotes: unhelpful,
        helpfulnessScore: score
      };
    });

    return reviewsWithHelpfulness
      .sort((a, b) => b.helpfulnessScore - a.helpfulnessScore)
      .slice(0, limit);
  } catch (error) {
    logger.error('Error getting most helpful reviews', error);
    return reviews.slice(0, limit);
  }
};

/**
 * Get reviews sorted by helpfulness
 */
export const sortReviewsByHelpfulness = (reviews: Review[], ascending: boolean = false): Review[] => {
  try {
    const votes = getReviewVotes();
    
    const reviewsWithScores = reviews.map(review => {
      const reviewVotes = votes[review.id];
      const helpful = reviewVotes?.helpful || 0;
      const unhelpful = reviewVotes?.unhelpful || 0;
      const ratio = calculateHelpfulnessRatio(helpful, unhelpful);
      const totalVotes = helpful + unhelpful;
      
      // Weighted score considering both ratio and vote count
      const score = totalVotes > 0 ? ratio * Math.log(totalVotes + 1) : 0;
      
      return {
        ...review,
        helpfulVotes: helpful,
        unhelpfulVotes: unhelpful,
        helpfulnessScore: score
      };
    });

    return reviewsWithScores.sort((a, b) => 
      ascending ? a.helpfulnessScore - b.helpfulnessScore : b.helpfulnessScore - a.helpfulnessScore
    );
  } catch (error) {
    logger.error('Error sorting reviews by helpfulness', error);
    return reviews;
  }
};

/**
 * Get helpfulness statistics for analytics
 */
export const getHelpfulnessStatistics = (reviewIds: string[]): {
  totalVotes: number;
  averageHelpfulnessRatio: number;
  mostHelpfulReviewId: string | null;
  leastHelpfulReviewId: string | null;
} => {
  try {
    const votes = getReviewVotes();
    let totalVotes = 0;
    let totalRatio = 0;
    let reviewCount = 0;
    let maxScore = -1;
    let minScore = Infinity;
    let mostHelpfulId: string | null = null;
    let leastHelpfulId: string | null = null;

    reviewIds.forEach(reviewId => {
      const reviewVotes = votes[reviewId];
      if (reviewVotes) {
        const helpful = reviewVotes.helpful;
        const unhelpful = reviewVotes.unhelpful;
        const total = helpful + unhelpful;
        
        if (total > 0) {
          totalVotes += total;
          const ratio = calculateHelpfulnessRatio(helpful, unhelpful);
          totalRatio += ratio;
          reviewCount++;

          const score = ratio * Math.log(total + 1);
          if (score > maxScore) {
            maxScore = score;
            mostHelpfulId = reviewId;
          }
          if (score < minScore) {
            minScore = score;
            leastHelpfulId = reviewId;
          }
        }
      }
    });

    return {
      totalVotes,
      averageHelpfulnessRatio: reviewCount > 0 ? totalRatio / reviewCount : 0,
      mostHelpfulReviewId: mostHelpfulId,
      leastHelpfulReviewId: leastHelpfulId
    };
  } catch (error) {
    logger.error('Error getting helpfulness statistics', error);
    return {
      totalVotes: 0,
      averageHelpfulnessRatio: 0,
      mostHelpfulReviewId: null,
      leastHelpfulReviewId: null
    };
  }
};

/**
 * Clear all voting data (for testing/reset purposes)
 */
export const clearAllVotes = (): void => {
  localStorage.removeItem(VOTES_STORAGE_KEY);
  localStorage.removeItem(USER_VOTES_KEY);
  logger.system('review_votes_cleared');
};
