import { renderHook, act } from '@testing-library/react';import { toast } from 'sonner';import * as React from 'react';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Create a test version of the hook that doesn't rely on import.meta.env
interface User {
  sub?: string;
  name?: string;
  email?: string;
  picture?: string;
  phone_number?: string;
  role?: 'user' | 'admin' | 'affiliate';
}

interface UseDevelopmentAuthResult {
  devUser: User | null;
  isDevelopmentMode: boolean;
  hasLoggedOut: boolean;
  devLogin: () => void;
  devRegister: () => void;
  devLogout: (callback?: () => void) => void;
  setAsAdmin: () => void;
  updateDevUser: (updates: Partial<User>) => void;
}

// Test implementation that we can control
const createTestDevelopmentAuth = (isDev: boolean = true, skipAuth: boolean = true): (() => UseDevelopmentAuthResult) => {
  return () => {
    const [devUser, setDevUser] = React.useState<User | null>(null);
    const isDevelopmentMode = isDev && skipAuth;
    const hasLoggedOut = localStorage.getItem('sabone-dev-logged-out') === 'true';

    React.useEffect(() => {
      if (isDevelopmentMode && !hasLoggedOut) {
        const isAdmin = localStorage.getItem('sabone-dev-admin') === 'true';
        setDevUser({
          sub: 'dev-user-123',
          name: 'Development User',
          email: isAdmin ? '<EMAIL>' : '<EMAIL>',
          role: isAdmin ? 'admin' : 'user'
        });
      }
    }, [isDevelopmentMode, hasLoggedOut]);

    const devLogin = React.useCallback(() => {
      if (!isDevelopmentMode) return;
      
      localStorage.removeItem('sabone-dev-logged-out');
      setDevUser({
        sub: 'dev-user-123',
        name: 'Development User',
        email: '<EMAIL>',
        role: 'user'
      });
      toast.success('Development mode: Logged in as Development User');
    }, [isDevelopmentMode]);

    const devRegister = React.useCallback(() => {
      if (!isDevelopmentMode) return;
      
      localStorage.removeItem('sabone-dev-logged-out');
      setDevUser({
        sub: 'dev-user-123',
        name: 'Development User',
        email: '<EMAIL>',
        role: 'user'
      });
      toast.success('Development mode: Account created successfully!');
    }, [isDevelopmentMode]);

    const devLogout = React.useCallback((callback?: () => void) => {
      if (!isDevelopmentMode) return;
      
      localStorage.setItem('sabone-dev-logged-out', 'true');
      localStorage.removeItem('sabone-dev-admin');
      setDevUser(null);
      toast.info('Development mode: Logged out');

      if (callback) {
        setTimeout(callback, 100);
      }
    }, [isDevelopmentMode]);

    const setAsAdmin = React.useCallback(() => {
      if (!isDevelopmentMode || !devUser) {
        toast.error('This function is only available in development mode when logged in');
        return;
      }

      setDevUser(prevUser => 
        prevUser ? { ...prevUser, role: 'admin', email: '<EMAIL>' } : null
      );
      localStorage.setItem('sabone-dev-admin', 'true');
      toast.success('Development user has been granted admin privileges');
    }, [isDevelopmentMode, devUser]);

    const updateDevUser = React.useCallback((updates: Partial<User>) => {
      if (!isDevelopmentMode) return;
      
      setDevUser(prevUser => 
        prevUser ? { ...prevUser, ...updates } : null
      );
    }, [isDevelopmentMode]);

    return {
      devUser,
      isDevelopmentMode,
      hasLoggedOut,
      devLogin,
      devRegister,
      devLogout,
      setAsAdmin,
      updateDevUser
    };
  };
};

describe('useDevelopmentAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('initialization', () => {
    it('should initialize in development mode when conditions are met', () => {
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.isDevelopmentMode).toBe(true);
      expect(result.current.hasLoggedOut).toBe(false);
      expect(result.current.devUser).toEqual({
        sub: 'dev-user-123',
        name: 'Development User',
        email: '<EMAIL>',
        role: 'user'
      });
    });

    it('should not be in development mode when DEV is false', () => {
      const useTestHook = createTestDevelopmentAuth(false, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.isDevelopmentMode).toBe(false);
      expect(result.current.devUser).toBeNull();
    });

    it('should not be in development mode when VITE_SKIP_AUTH is not true', () => {
      const useTestHook = createTestDevelopmentAuth(true, false);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.isDevelopmentMode).toBe(false);
      expect(result.current.devUser).toBeNull();
    });

    it('should respect logged out state', () => {
      localStorage.setItem('sabone-dev-logged-out', 'true');
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.hasLoggedOut).toBe(true);
      expect(result.current.devUser).toBeNull();
    });

    it('should initialize as admin when admin flag is set', () => {
      localStorage.setItem('sabone-dev-admin', 'true');
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.devUser).toEqual({
        sub: 'dev-user-123',
        name: 'Development User',
        email: '<EMAIL>',
        role: 'admin'
      });
    });
  });

  describe('devLogin', () => {
    it('should login development user', () => {
      localStorage.setItem('sabone-dev-logged-out', 'true');
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.devUser).toBeNull();

      act(() => {
        result.current.devLogin();
      });

      expect(localStorage.getItem('sabone-dev-logged-out')).toBeNull();
      expect(result.current.devUser).toEqual({
        sub: 'dev-user-123',
        name: 'Development User',
        email: '<EMAIL>',
        role: 'user'
      });
      expect(toast.success).toHaveBeenCalledWith('Development mode: Logged in as Development User');
    });

    it('should not work outside development mode', () => {
      const useTestHook = createTestDevelopmentAuth(false, true);
      const { result } = renderHook(useTestHook);

      act(() => {
        result.current.devLogin();
      });

      expect(result.current.devUser).toBeNull();
      expect(toast.success).not.toHaveBeenCalled();
    });
  });

  describe('devRegister', () => {
    it('should register development user', () => {
      localStorage.setItem('sabone-dev-logged-out', 'true');
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.devUser).toBeNull();

      act(() => {
        result.current.devRegister();
      });

      expect(localStorage.getItem('sabone-dev-logged-out')).toBeNull();
      expect(result.current.devUser).toEqual({
        sub: 'dev-user-123',
        name: 'Development User',
        email: '<EMAIL>',
        role: 'user'
      });
      expect(toast.success).toHaveBeenCalledWith('Development mode: Account created successfully!');
    });

    it('should not work outside development mode', () => {
      const useTestHook = createTestDevelopmentAuth(false, true);
      const { result } = renderHook(useTestHook);

      act(() => {
        result.current.devRegister();
      });

      expect(result.current.devUser).toBeNull();
      expect(toast.success).not.toHaveBeenCalled();
    });
  });

  describe('devLogout', () => {
    it('should logout development user', () => {
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      const mockCallback = jest.fn();
      
      expect(result.current.devUser).not.toBeNull();

      act(() => {
        result.current.devLogout(mockCallback);
      });

      expect(localStorage.getItem('sabone-dev-logged-out')).toBe('true');
      expect(localStorage.getItem('sabone-dev-admin')).toBeNull();
      expect(result.current.devUser).toBeNull();
      expect(toast.info).toHaveBeenCalledWith('Development mode: Logged out');

      // Test callback execution
      setTimeout(() => {
        expect(mockCallback).toHaveBeenCalled();
      }, 150);
    });

    it('should not work outside development mode', () => {
      const useTestHook = createTestDevelopmentAuth(false, true);
      const { result } = renderHook(useTestHook);

      act(() => {
        result.current.devLogout();
      });

      expect(toast.info).not.toHaveBeenCalled();
    });
  });

  describe('setAsAdmin', () => {
    it('should set user as admin when logged in', () => {
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.devUser?.role).toBe('user');

      act(() => {
        result.current.setAsAdmin();
      });

      expect(result.current.devUser?.role).toBe('admin');
      expect(result.current.devUser?.email).toBe('<EMAIL>');
      expect(localStorage.getItem('sabone-dev-admin')).toBe('true');
      expect(toast.success).toHaveBeenCalledWith('Development user has been granted admin privileges');
    });

    it('should not work when not logged in', () => {
      localStorage.setItem('sabone-dev-logged-out', 'true');
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      expect(result.current.devUser).toBeNull();

      act(() => {
        result.current.setAsAdmin();
      });

      expect(toast.error).toHaveBeenCalledWith('This function is only available in development mode when logged in');
      expect(localStorage.getItem('sabone-dev-admin')).toBeNull();
    });

    it('should not work outside development mode', () => {
      const useTestHook = createTestDevelopmentAuth(false, true);
      const { result } = renderHook(useTestHook);

      act(() => {
        result.current.setAsAdmin();
      });

      expect(toast.error).toHaveBeenCalledWith('This function is only available in development mode when logged in');
    });
  });

  describe('updateDevUser', () => {
    it('should update development user properties', () => {
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      const updates = {
        name: 'Updated Dev User',
        email: '<EMAIL>',
        phone_number: '+1234567890'
      };

      act(() => {
        result.current.updateDevUser(updates);
      });

      expect(result.current.devUser).toEqual({
        sub: 'dev-user-123',
        name: 'Updated Dev User',
        email: '<EMAIL>',
        phone_number: '+1234567890',
        role: 'user'
      });
    });

    it('should not update when not in development mode', () => {
      const useTestHook = createTestDevelopmentAuth(false, true);
      const { result } = renderHook(useTestHook);

      act(() => {
        result.current.updateDevUser({ name: 'Should Not Update' });
      });

      expect(result.current.devUser).toBeNull();
    });

    it('should handle partial updates', () => {
      const useTestHook = createTestDevelopmentAuth(true, true);
      const { result } = renderHook(useTestHook);
      
      act(() => {
        result.current.updateDevUser({ name: 'Partially Updated' });
      });

      expect(result.current.devUser?.name).toBe('Partially Updated');
      expect(result.current.devUser?.email).toBe('<EMAIL>');
      expect(result.current.devUser?.role).toBe('user');
    });
  });
}); 