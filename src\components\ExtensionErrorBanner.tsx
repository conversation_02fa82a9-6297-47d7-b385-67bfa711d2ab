import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface ExtensionErrorBannerProps {
  onClose?: () => void;
}

const ExtensionErrorBanner = ({ onClose }: ExtensionErrorBannerProps) => {
  // Only show in development mode
  const isDevelopment = import.meta.env.DEV;
  const [visible, setVisible] = useState(false);
  const [extensionErrors, setExtensionErrors] = useState<string[]>([]);

  useEffect(() => {
    // Only monitor for extension errors in development mode
    if (!isDevelopment) return;

    const originalConsoleError = console.error;

    console.error = function(...args: any[]) {
      // Call original function
      originalConsoleError.apply(console, args);

      // Check if this is an extension error
      const errorString = args.join(' ').toLowerCase();
      if (
        errorString.includes('contentscript.bundle.js') ||
        errorString.includes('message port closed') ||
        errorString.includes('extension')
      ) {
        setVisible(true);
        setExtensionErrors(prev => {
          const newError = args.join(' ');
          if (!prev.includes(newError)) {
            return [...prev, newError];
          }
          return prev;
        });
      }
    };

    // Add a global error handler
    const originalOnError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
      if (source?.includes('contentscript.bundle.js') ||
          (error && error.toString().includes('message port closed'))) {
        setVisible(true);
        setExtensionErrors(prev => {
          const newError = `${message} (${source}:${lineno}:${colno})`;
          if (!prev.includes(newError)) {
            return [...prev, newError];
          }
          return prev;
        });
      }

      // Call the original handler if it exists
      if (typeof originalOnError === 'function') {
        return originalOnError(message, source, lineno, colno, error);
      }
      return false;
    };

    return () => {
      console.error = originalConsoleError;
      window.onerror = originalOnError;
    };
  }, [isDevelopment]);

  const handleClose = () => {
    setVisible(false);
    if (onClose) onClose();
  };

  // Don't show in production or if not visible
  if (!isDevelopment || !visible) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-amber-50 text-amber-900 p-4 shadow-md">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-bold text-lg flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              Browser Extension Issue Detected
            </h3>
            <p className="mt-2">
              A browser extension is causing errors in the application. This is not an issue with the Sabone website itself.
            </p>
            <div className="mt-3 space-y-2">
              <p className="font-medium">To resolve this issue, try one of the following:</p>
              <ol className="list-decimal list-inside ml-2 space-y-1">
                <li>Open this site in an incognito/private window</li>
                <li>Temporarily disable browser extensions, especially developer tools or ad blockers</li>
                <li>Try a different browser</li>
              </ol>
            </div>
            {extensionErrors.length > 0 && (
              <details className="mt-3">
                <summary className="cursor-pointer text-sm font-medium">Technical Details</summary>
                <div className="mt-2 p-2 bg-amber-100 rounded text-xs font-mono overflow-x-auto max-h-32 overflow-y-auto">
                  {extensionErrors.map((error, index) => (
                    <div key={index} className="mb-1">{error}</div>
                  ))}
                </div>
              </details>
            )}
          </div>
          <button
            onClick={handleClose}
            className="text-amber-900 hover:bg-amber-100 p-1 rounded"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExtensionErrorBanner;
