import { ReactNode } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>Footer,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel?: () => void;
  variant?: "destructive" | "warning" | "default";
  children?: ReactNode;
  isLoading?: boolean;
}

const ConfirmationDialog = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
  variant = "default",
  children,
  isLoading = false
}: ConfirmationDialogProps) => {
  // Determine title color based on variant
  const getTitleColor = () => {
    switch (variant) {
      case "destructive":
        return "text-red-500";
      case "warning":
        return "text-amber-500";
      default:
        return "text-sabone-gold";
    }
  };
  
  // Determine confirm button variant
  const getConfirmButtonVariant = () => {
    switch (variant) {
      case "destructive":
        return "destructive";
      case "warning":
        return "warning";
      default:
        return "default";
    }
  };
  
  // Handle cancel action
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
        <DialogHeader>
          <DialogTitle className={`flex items-center gap-2 ${getTitleColor()}`}>
            {variant === "destructive" || variant === "warning" ? (
              <AlertTriangle className="h-5 w-5" />
            ) : null}
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription className="text-sabone-cream/70">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        
        {children && <div className="py-4">{children}</div>}
        
        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-dark-olive/50"
            onClick={handleCancel}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            variant={getConfirmButtonVariant() as any}
            onClick={onConfirm}
            disabled={isLoading}
            className={variant === "default" ? "bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80" : ""}
          >
            {isLoading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              confirmText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmationDialog;
