// Import React explicitly
import React from 'react'
// Import ReactDOM
import * as ReactDOM from 'react-dom/client'
// Import components
import App from './AppOptimized.tsx'
import SimplifiedApp from './SimplifiedApp.tsx'
import ReactTest from './ReactTest.tsx'
// Import error boundary
import GlobalErrorBoundary from './components/GlobalErrorBoundary.tsx'
// Import styles
import './index.css'
import './fixes.css' // Import fixes for missing assets
import './i18n/rtl.css' // Import RTL styles
// Import extension debugger
import { initExtensionDebugging, isExtensionPromiseRejection } from './utils/extensionDebugger'
// Import preload utilities
import { preloadCriticalResources, preconnect, dnsPrefetch } from './utils/preload'
// Import image middleware
import { initImageMiddleware } from './middleware/imageMiddleware'

import { Auth0Provider } from '@auth0/auth0-react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'sonner'
import { Helmet, HelmetProvider } from 'react-helmet-async'
import { initializeSecurity } from '@/middleware/securityMiddleware'

// Flags for debugging - gradually transition to full app
const USE_TEST_COMPONENT = false; // Set to true to use the test component for debugging
const USE_SIMPLIFIED_APP = false; // Set to false to use the full app

console.log('🚀 Sabone app starting...');
// Log loaded modules
console.log('📦 Modules loaded:', {
  React: React ? 'Available' : 'Not available',
  ReactDOM: ReactDOM ? 'Available' : 'Not available',
  createRoot: ReactDOM.createRoot ? 'Available' : 'Not available'
});

// Initialize extension debugging
initExtensionDebugging();
console.log('🛡️ Extension debugging initialized');

// Initialize image middleware
initImageMiddleware();
console.log('🖼️ Image middleware initialized');

// Initialize security middleware
initializeSecurity({
  enableCSP: true,
  enableRateLimit: true,
  enableInputValidation: true,
  enableSecurityHeaders: true,
  logSecurityEvents: true,
  blockSuspiciousRequests: true,
});

// Run error handling tests in development
if (import.meta.env.DEV) {
  import('./utils/errorTestUtils').then(({ verifyFixes, runAllErrorTests, testExtensionErrorDetection }) => {
    verifyFixes();

    // Run extension error detection tests specifically
    setTimeout(() => {
      testExtensionErrorDetection();
    }, 2000);

    // Uncomment the line below to run comprehensive tests
    // runAllErrorTests();
  }).catch(error => {
    console.warn('Error loading test utilities:', error);
  });
}

// Add global error handlers for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);

  // Enhanced error categorization and handling
  const reason = event.reason;
  let shouldPreventDefault = false;

  // Check if it's an i18n related error
  if (reason && typeof reason === 'object' &&
      (reason.message?.includes('i18n') ||
       reason.message?.includes('locale') ||
       reason.message?.includes('messages'))) {
    console.warn('i18n loading error detected, this is usually non-critical');
    shouldPreventDefault = true;
  }

  // Check if it's an image loading error
  if (reason && typeof reason === 'object' &&
      (reason.message?.includes('image') ||
       reason.message?.includes('srcset') ||
       reason.message?.includes('Failed to load') ||
       reason.name === 'NetworkError')) {
    console.warn('Image loading error detected, using fallback handling');
    shouldPreventDefault = true;
  }

  // Check if it's an Auth0 related error
  if (reason && typeof reason === 'object' &&
      (reason.message?.includes('auth0') ||
       reason.message?.includes('authentication') ||
       reason.message?.includes('login'))) {
    console.warn('Auth0 error detected, this may be handled by Auth0 error boundaries');
    shouldPreventDefault = true;
  }

  // Check if it's a browser extension error using enhanced detection
  if (isExtensionPromiseRejection(reason)) {
    console.warn(
      '%c Browser Extension Promise Rejection ',
      'background: #FFA500; color: white; font-weight: bold; padding: 2px 6px; border-radius: 3px;',
      'Promise rejection from browser extension detected and suppressed. This is not an application error.'
    );

    // Log additional context for debugging in development
    if (import.meta.env.DEV) {
      console.info(
        '%c Extension Rejection Details ',
        'background: #2196F3; color: white; font-weight: bold; padding: 2px 6px; border-radius: 3px;',
        {
          reason,
          type: typeof reason,
          isEvent: reason instanceof Event,
          timestamp: new Date().toISOString()
        }
      );
    }

    shouldPreventDefault = true;
  }

  // Only prevent default for known non-critical errors
  if (shouldPreventDefault) {
    event.preventDefault();
  }

  // For unknown errors, let them bubble up but log additional context
  if (!shouldPreventDefault) {
    console.error('Unknown promise rejection - this may need investigation:', {
      reason,
      stack: reason?.stack,
      type: typeof reason,
      timestamp: new Date().toISOString()
    });
  }
});

// Preload critical resources
console.log('🚀 Preloading critical resources...');
// Preconnect to external domains
preconnect('https://fonts.googleapis.com');
preconnect('https://fonts.gstatic.com');
preconnect('https://cdn.auth0.com');
dnsPrefetch('https://cdn.auth0.com');

// For payment providers
if (import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY) {
  preconnect('https://js.stripe.com');
  preconnect('https://api.stripe.com');
  dnsPrefetch('https://js.stripe.com');
  dnsPrefetch('https://api.stripe.com');
}

if (import.meta.env.VITE_PAYPAL_CLIENT_ID) {
  preconnect('https://www.paypal.com');
  preconnect('https://www.paypalobjects.com');
  dnsPrefetch('https://www.paypal.com');
  dnsPrefetch('https://www.paypalobjects.com');
}

// Preload critical resources (images, fonts, etc.)
if (!USE_TEST_COMPONENT && !USE_SIMPLIFIED_APP) {
  preloadCriticalResources();
}

// Check if the root element exists
const rootElement = document.getElementById("root");
console.log('🔍 Root element:', rootElement ? 'Found' : 'Not found');

// QueryClient will be created in AppOptimized.tsx to avoid duplication

// Auth0 configuration
const domain = import.meta.env.VITE_AUTH0_DOMAIN
const clientId = import.meta.env.VITE_AUTH0_CLIENT_ID
const redirectUri = window.location.origin

// Add error boundary for development
const renderApp = () => {
  try {
    console.log('⚙️ Attempting to render App component...');

    // Now try to render the actual app
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    // Use test component for debugging if flag is set
    if (USE_TEST_COMPONENT) {
      console.log('🔧 Using React test component for debugging');
      ReactDOM.createRoot(rootElement).render(
        <React.StrictMode>
          <GlobalErrorBoundary>
            <ReactTest />
          </GlobalErrorBoundary>
        </React.StrictMode>
      );
    } else if (USE_SIMPLIFIED_APP) {
      console.log('🔧 Using simplified app for debugging');
      ReactDOM.createRoot(rootElement).render(
        <React.StrictMode>
          <GlobalErrorBoundary>
            <SimplifiedApp />
          </GlobalErrorBoundary>
        </React.StrictMode>
      );
    } else {
      console.log('🔧 Using full app');
      ReactDOM.createRoot(rootElement).render(
        <React.StrictMode>
          <HelmetProvider>
            <Helmet>
              <title>Sabone.store - Premium E-commerce Experience</title>
              <meta name="description" content="Discover premium products at Sabone.store. Secure, fast, and user-friendly e-commerce platform with international payment support." />
              <meta name="viewport" content="width=device-width, initial-scale=1.0" />
              <meta name="theme-color" content="#000000" />
              <meta property="og:title" content="Sabone.store - Premium E-commerce" />
              <meta property="og:description" content="Premium e-commerce platform with secure payments and international shipping" />
              <meta property="og:type" content="website" />
              <meta property="og:url" content={window.location.origin} />
              <link rel="icon" type="image/svg+xml" href="/vite.svg" />
              <link rel="canonical" href={window.location.origin} />

              {/* Security Headers - Note: X-Frame-Options moved to HTTP headers in vite.config.ts */}
              <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
              <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
              <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
              <meta httpEquiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()" />
            </Helmet>

            <Auth0Provider
              domain={domain}
              clientId={clientId}
              authorizationParams={{
                redirect_uri: redirectUri,
                scope: "openid profile email phone",
                audience: import.meta.env.VITE_AUTH0_AUDIENCE
              }}
              useRefreshTokens={true}
              cacheLocation="memory"
            >
              <QueryClientProvider client={queryClient}>
                <App />
              </QueryClientProvider>
            </Auth0Provider>
          </HelmetProvider>
        </React.StrictMode>
      );
    }
    console.log('✅ App rendered successfully');

    // Remove the test div after successful render
    setTimeout(() => {
      const testDivElement = document.getElementById('test-render');
      if (testDivElement) {
        testDivElement.remove();
      }
    }, 5000);
  } catch (error) {
    console.error('❌ Error rendering application:', error);

    if (rootElement) {
      rootElement.innerHTML = `
        <div style="padding: 20px; font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; margin-top: 50px; background-color: #2a2a1f; border-radius: 8px; border: 1px solid #c6a870;">
          <h1 style="color: #c6a870;">Sabone Application Error</h1>
          <p style="color: #e5dcc5;">There was an error rendering the application. Please check the console for details.</p>
          <pre style="background: #1c1c1c; color: #e5dcc5; padding: 10px; border-radius: 4px; overflow: auto;">${error instanceof Error ? error.message : String(error)}</pre>

          <div style="margin-top: 20px; padding: 15px; background-color: #333328; border-radius: 4px;">
            <h2 style="color: #c6a870; margin-top: 0;">Troubleshooting Steps</h2>
            <ul style="color: #e5dcc5;">
              <li>Check browser console for detailed error messages</li>
              <li>Verify that all required environment variables are set</li>
              <li>Check if Auth0 credentials are correct</li>
              <li>Clear browser cache and local storage</li>
              <li>Try using a different browser</li>
            </ul>
          </div>
        </div>
      `;
    }
  }
};

// Wait for DOM to be fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', renderApp);
} else {
  renderApp();
}
