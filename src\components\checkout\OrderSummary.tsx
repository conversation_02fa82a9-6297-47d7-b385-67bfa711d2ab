import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState } from "react";
import { ChevronUp, ChevronDown, ShieldCheck } from "lucide-react";

const OrderSummary = () => {
  const { subtotal, shipping, total, itemCount } = useCart();
  const { isAuthenticated } = useAuth();
  const isMobile = useIsMobile();
  const [isExpanded, setIsExpanded] = useState(false);

  const handlePlaceOrder = () => {
    // The actual order placement is now handled in the CustomerForm component
    // This button is just for display purposes in the order summary
    if (!isAuthenticated) {
      toast.error("Please log in to place an order");
      return;
    }

    document.getElementById("customer-form")?.scrollIntoView({ behavior: "smooth" });
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="space-y-4">
      {/* Mobile Order Summary Toggle */}
      {isMobile && (
        <button
          onClick={toggleExpand}
          className="flex w-full justify-between items-center bg-sabone-dark-olive/80 p-4 rounded-md"
        >
          <div className="flex flex-col items-start">
            <span className="text-sabone-cream text-sm">Order Summary</span>
            <span className="text-sabone-gold font-semibold">${total.toFixed(2)}</span>
          </div>
          <div className="text-sabone-gold">
            {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </div>
        </button>
      )}

      {/* Order Summary Details - Always visible on desktop, conditionally on mobile */}
      <div className={`space-y-2 ${isMobile && !isExpanded ? 'hidden' : 'block'}`}>
        <div className="flex justify-between text-sabone-cream">
          <span>Subtotal ({itemCount} {itemCount === 1 ? 'item' : 'items'})</span>
          <span>${subtotal.toFixed(2)}</span>
        </div>

        <div className="flex justify-between text-sabone-cream">
          <span>Shipping</span>
          <span>${shipping.toFixed(2)}</span>
        </div>

        <Separator className="my-4 bg-sabone-gold/20" />

        <div className="flex justify-between text-sabone-gold font-semibold text-lg">
          <span>Total</span>
          <span>${total.toFixed(2)}</span>
        </div>
      </div>

      {/* Proceed to Payment Button - Always visible */}
      <div className="pt-6">
        <Button
          className="w-full bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium h-14 text-base"
          onClick={handlePlaceOrder}
          disabled={itemCount === 0 || !isAuthenticated}
        >
          {itemCount === 0 ? 'Cart is Empty' : 'Proceed to Payment'}
        </Button>

        {!isAuthenticated && (
          <p className="text-red-400 text-xs mt-2 text-center">
            Please log in to place an order
          </p>
        )}

        <p className="text-xs text-sabone-cream/60 text-center mt-4">
          By placing your order, you agree to our Terms of Service and Privacy Policy.
        </p>
      </div>

      {/* Secure Checkout Info - Always visible on desktop, conditionally on mobile */}
      <div className={`pt-4 ${isMobile && !isExpanded ? 'hidden' : 'block'}`}>
        <div className="bg-sabone-dark-olive/60 p-4 rounded-md">
          <div className="flex items-center mb-2">
            <ShieldCheck className="h-5 w-5 text-sabone-gold mr-2" />
            <h3 className="text-sabone-gold font-medium">Secure Checkout</h3>
          </div>
          <p className="text-sm text-sabone-cream/70">
            Your payment information is processed securely. We do not store credit card details.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
