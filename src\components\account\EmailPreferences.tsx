import { useState, useEffect } from "react";
import { useEmail } from "@/contexts/EmailContext";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Bell, Mail, Tag } from "lucide-react";

const EmailPreferences = () => {
  const { emailPreferences, updateEmailPreferences } = useEmail();

  const [orderNotifications, setOrderNotifications] = useState(true);
  const [accountNotifications, setAccountNotifications] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form with user preferences
  useEffect(() => {
    if (emailPreferences) {
      setOrderNotifications(emailPreferences.orderNotifications);
      setAccountNotifications(emailPreferences.accountNotifications);
      setMarketingEmails(emailPreferences.marketingEmails);
      setHasChanges(false);
    }
  }, [emailPreferences]);

  // Check for changes
  useEffect(() => {
    if (emailPreferences) {
      const hasChanged =
        orderNotifications !== emailPreferences.orderNotifications ||
        accountNotifications !== emailPreferences.accountNotifications ||
        marketingEmails !== emailPreferences.marketingEmails;

      setHasChanges(hasChanged);
    }
  }, [orderNotifications, accountNotifications, marketingEmails, emailPreferences]);

  const handleSavePreferences = () => {
    updateEmailPreferences({
      orderNotifications,
      accountNotifications,
      marketingEmails
    });
    setHasChanges(false);
  };

  const handleResetPreferences = () => {
    if (emailPreferences) {
      setOrderNotifications(emailPreferences.orderNotifications);
      setAccountNotifications(emailPreferences.accountNotifications);
      setMarketingEmails(emailPreferences.marketingEmails);
      setHasChanges(false);
    }
  };

  if (!emailPreferences) {
    return (
      <div className="p-6 bg-sabone-dark-olive/40 rounded-lg gold-border">
        <p className="text-sabone-cream/70 text-center">Loading email preferences...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-playfair font-semibold text-sabone-gold">Email Preferences</h2>
        <p className="text-sabone-cream/70 mt-1">
          Manage what types of emails you receive from Sabone
        </p>
      </div>

      <Card className="bg-sabone-dark-olive/40 border-sabone-gold/30">
        <CardHeader>
          <CardTitle className="text-sabone-gold flex items-center gap-2">
            <Bell size={18} />
            Order Notifications
          </CardTitle>
          <CardDescription className="text-sabone-cream/70">
            Emails about your orders, including confirmations, shipping updates, delivery notifications, and order receipts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <Label htmlFor="order-notifications" className="text-sabone-cream">
              Receive order notifications
            </Label>
            <Switch
              id="order-notifications"
              checked={orderNotifications}
              onCheckedChange={setOrderNotifications}
              className="data-[state=checked]:bg-sabone-gold"
            />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-sabone-dark-olive/40 border-sabone-gold/30">
        <CardHeader>
          <CardTitle className="text-sabone-gold flex items-center gap-2">
            <Mail size={18} />
            Account Notifications
          </CardTitle>
          <CardDescription className="text-sabone-cream/70">
            Emails about your account, including welcome messages, password resets, and account changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <Label htmlFor="account-notifications" className="text-sabone-cream">
              Receive account notifications
            </Label>
            <Switch
              id="account-notifications"
              checked={accountNotifications}
              onCheckedChange={setAccountNotifications}
              className="data-[state=checked]:bg-sabone-gold"
            />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-sabone-dark-olive/40 border-sabone-gold/30">
        <CardHeader>
          <CardTitle className="text-sabone-gold flex items-center gap-2">
            <Tag size={18} />
            Marketing Emails
          </CardTitle>
          <CardDescription className="text-sabone-cream/70">
            Promotional emails, newsletters, and special offers from Sabone
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <Label htmlFor="marketing-emails" className="text-sabone-cream">
              Receive marketing emails
            </Label>
            <Switch
              id="marketing-emails"
              checked={marketingEmails}
              onCheckedChange={setMarketingEmails}
              className="data-[state=checked]:bg-sabone-gold"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end space-x-3 mt-6">
        <Button
          variant="outline"
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          onClick={handleResetPreferences}
          disabled={!hasChanges}
        >
          Cancel
        </Button>
        <Button
          className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
          onClick={handleSavePreferences}
          disabled={!hasChanges}
        >
          Save Preferences
        </Button>
      </div>

      <Separator className="bg-sabone-gold/10 my-8" />

      <div className="text-sm text-sabone-cream/60">
        <p>
          Last updated: {new Date(emailPreferences.lastUpdated).toLocaleDateString()} at {new Date(emailPreferences.lastUpdated).toLocaleTimeString()}
        </p>
        <p className="mt-2">
          You can unsubscribe from marketing emails at any time by clicking the unsubscribe link in any marketing email we send you.
        </p>
        <p className="mt-2">
          We will always send you important account-related emails, such as password resets, even if you opt out of marketing emails.
        </p>
      </div>
    </div>
  );
};

export default EmailPreferences;
