// Import required modules
import express from 'express';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Initialize environment variables
dotenv.config();

const router = express.Router();

// PayPal webhook secret
const PAYPAL_WEBHOOK_ID = process.env.VITE_PAYPAL_WEBHOOK_ID;

/**
 * Verify PayPal webhook signature
 * 
 * @param {string} requestBody - The raw request body
 * @param {Object} headers - The request headers
 * @returns {boolean} Whether the signature is valid
 */
const verifyPayPalWebhookSignature = (requestBody, headers) => {
  try {
    // In a production environment, you would verify the webhook signature
    // using the PayPal SDK or a custom implementation
    
    // For development purposes, we'll skip the verification
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Skipping PayPal webhook signature verification in development');
      return true;
    }
    
    // In production, you would implement proper signature verification
    // This is a simplified example and should not be used in production
    const transmissionId = headers['paypal-transmission-id'];
    const timestamp = headers['paypal-transmission-time'];
    const webhookId = PAYPAL_WEBHOOK_ID;
    const eventBody = requestBody;
    const certUrl = headers['paypal-cert-url'];
    const authAlgo = headers['paypal-auth-algo'];
    const transmissionSig = headers['paypal-transmission-sig'];
    
    // Construct the data string that was signed
    const data = `${transmissionId}|${timestamp}|${webhookId}|${crypto.createHash('sha256').update(eventBody).digest('hex')}`;
    
    // In a real implementation, you would:
    // 1. Download the certificate from certUrl
    // 2. Verify the signature using the certificate and authAlgo
    // 3. Return true if valid, false otherwise
    
    // For now, we'll return true
    return true;
  } catch (error) {
    console.error('Error verifying PayPal webhook signature:', error);
    return false;
  }
};

/**
 * Handle PayPal webhook events
 * 
 * This endpoint receives webhook events from PayPal and processes them accordingly.
 * 
 * @route POST /api/paypal-webhook
 * @returns {Object} Status of the webhook processing
 */
router.post('/paypal-webhook', async (req, res) => {
  try {
    // Get the raw body for signature verification
    const rawBody = req.rawBody || JSON.stringify(req.body);
    
    // Verify the webhook signature
    const isSignatureValid = verifyPayPalWebhookSignature(rawBody, req.headers);
    
    if (!isSignatureValid) {
      console.error('PayPal webhook signature verification failed');
      return res.status(400).send('Webhook signature verification failed');
    }
    
    const event = req.body;
    
    // Handle different event types
    switch (event.event_type) {
      case 'PAYMENT.CAPTURE.COMPLETED':
        // Payment was successful
        console.log(`PayPal payment completed: ${event.resource.id}`);
        
        // Update order status in your database
        // In a real application, you would call your database service here
        
        break;
      case 'PAYMENT.CAPTURE.DENIED':
        // Payment was denied
        console.log(`PayPal payment denied: ${event.resource.id}`);
        
        // Handle denied payment
        // Update order status, notify customer, etc.
        
        break;
      case 'PAYMENT.CAPTURE.REFUNDED':
        // Payment was refunded
        console.log(`PayPal payment refunded: ${event.resource.id}`);
        
        // Handle refunded payment
        // Update order status, notify customer, etc.
        
        break;
      default:
        console.log(`Unhandled PayPal event type: ${event.event_type}`);
    }
    
    // Return a 200 response to acknowledge receipt of the event
    res.json({ received: true });
  } catch (error) {
    console.error('Error handling PayPal webhook:', error);
    res.status(500).send('Error handling webhook');
  }
});

export default router;
