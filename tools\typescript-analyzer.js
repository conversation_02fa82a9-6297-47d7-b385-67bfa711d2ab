#!/usr/bin/env node

/**
 * TypeScript Analyzer MCP Tool
 * Analyzes TypeScript errors and provides fixes for Sabone project
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class TypeScriptAnalyzer {
  constructor() {
    this.projectRoot = process.cwd();
    this.errors = [];
    this.fixes = [];
  }

  async analyzeProject() {
    console.log('🔍 Analyzing TypeScript errors in Sabone project...');
    
    try {
      // Run TypeScript compiler to get errors
      const tscOutput = await this.runTypeScriptCheck();
      this.parseErrors(tscOutput);
      
      // Generate fixes for common issues
      await this.generateFixes();
      
      // Create report
      await this.createReport();
      
      return {
        totalErrors: this.errors.length,
        fixableErrors: this.fixes.length,
        report: './typescript-analysis-report.json'
      };
    } catch (error) {
      console.error('❌ TypeScript analysis failed:', error.message);
      throw error;
    }
  }

  async runTypeScriptCheck() {
    return new Promise((resolve, reject) => {
      const tsc = spawn('npx', ['tsc', '--noEmit', '--pretty', 'false'], {
        cwd: this.projectRoot,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      tsc.stdout.on('data', (data) => {
        output += data.toString();
      });

      tsc.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      tsc.on('close', (code) => {
        // TypeScript returns non-zero exit code when there are errors
        // We want to capture the output regardless
        resolve(output + errorOutput);
      });

      tsc.on('error', (error) => {
        reject(error);
      });
    });
  }

  parseErrors(output) {
    const lines = output.split('\n');
    const errorPattern = /^(.+?)\((\d+),(\d+)\):\s+(error|warning)\s+TS(\d+):\s+(.+)$/;
    
    for (const line of lines) {
      const match = line.match(errorPattern);
      if (match) {
        const [, file, line, column, severity, code, message] = match;
        this.errors.push({
          file: path.relative(this.projectRoot, file),
          line: parseInt(line),
          column: parseInt(column),
          severity,
          code: `TS${code}`,
          message,
          fixable: this.isFixable(code)
        });
      }
    }
  }

  isFixable(errorCode) {
    // Common fixable TypeScript errors
    const fixableErrors = [
      '7006', // Parameter implicitly has 'any' type
      '7031', // Binding element implicitly has 'any' type
      '2304', // Cannot find name
      '2307', // Cannot find module
      '2322', // Type is not assignable
      '2339', // Property does not exist
      '2345', // Argument of type is not assignable
      '2571', // Object is of type 'unknown'
      '2769', // No overload matches this call
    ];
    return fixableErrors.includes(errorCode);
  }

  async generateFixes() {
    for (const error of this.errors) {
      if (error.fixable) {
        const fix = await this.generateFixForError(error);
        if (fix) {
          this.fixes.push({
            error,
            fix,
            confidence: fix.confidence || 'medium'
          });
        }
      }
    }
  }

  async generateFixForError(error) {
    switch (error.code) {
      case 'TS7006':
      case 'TS7031':
        return {
          type: 'add-type-annotation',
          description: 'Add explicit type annotation',
          suggestion: 'Add type annotation to remove implicit any',
          confidence: 'high'
        };
      
      case 'TS2304':
        return {
          type: 'add-import',
          description: 'Add missing import',
          suggestion: 'Import the missing identifier',
          confidence: 'medium'
        };
      
      case 'TS2307':
        return {
          type: 'fix-import-path',
          description: 'Fix import path',
          suggestion: 'Correct the module import path',
          confidence: 'high'
        };
      
      default:
        return null;
    }
  }

  async createReport() {
    const report = {
      timestamp: new Date().toISOString(),
      project: 'Sabone E-commerce',
      analysis: {
        totalErrors: this.errors.length,
        errorsByType: this.groupErrorsByType(),
        fixableErrors: this.fixes.length,
        criticalErrors: this.errors.filter(e => e.severity === 'error').length,
        warnings: this.errors.filter(e => e.severity === 'warning').length
      },
      errors: this.errors,
      fixes: this.fixes,
      recommendations: this.generateRecommendations()
    };

    await fs.writeFile(
      path.join(this.projectRoot, 'typescript-analysis-report.json'),
      JSON.stringify(report, null, 2)
    );

    console.log(`📊 TypeScript Analysis Complete:`);
    console.log(`   Total Errors: ${report.analysis.totalErrors}`);
    console.log(`   Fixable Errors: ${report.analysis.fixableErrors}`);
    console.log(`   Critical Errors: ${report.analysis.criticalErrors}`);
    console.log(`   Report saved: typescript-analysis-report.json`);
  }

  groupErrorsByType() {
    const groups = {};
    for (const error of this.errors) {
      groups[error.code] = (groups[error.code] || 0) + 1;
    }
    return groups;
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (this.errors.length > 100) {
      recommendations.push({
        priority: 'high',
        action: 'Enable TypeScript strict mode gradually',
        description: 'Too many errors to fix at once. Enable strict mode file by file.'
      });
    }

    const anyTypeErrors = this.errors.filter(e => e.code === 'TS7006' || e.code === 'TS7031');
    if (anyTypeErrors.length > 20) {
      recommendations.push({
        priority: 'high',
        action: 'Add explicit type annotations',
        description: `${anyTypeErrors.length} implicit any types found. Add explicit types.`
      });
    }

    return recommendations;
  }
}

// CLI interface
if (require.main === module) {
  const analyzer = new TypeScriptAnalyzer();
  analyzer.analyzeProject()
    .then(result => {
      console.log('✅ Analysis complete:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Analysis failed:', error);
      process.exit(1);
    });
}

module.exports = TypeScriptAnalyzer;
