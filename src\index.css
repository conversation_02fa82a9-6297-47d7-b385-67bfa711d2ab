
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600&family=Tajawal:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 60 6% 12%;
    --foreground: 42 33% 88%;

    --card: 60 6% 12%;
    --card-foreground: 42 33% 88%;

    --popover: 60 6% 12%;
    --popover-foreground: 42 33% 88%;

    --primary: 42 46% 61%;
    --primary-foreground: 0 0% 8%;

    --secondary: 40 6% 16%;
    --secondary-foreground: 42 33% 88%;

    --muted: 40 6% 16%;
    --muted-foreground: 42 10% 70%;

    --accent: 42 46% 61%;
    --accent-foreground: 0 0% 8%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 40 6% 20%;
    --input: 40 6% 20%;
    --ring: 42 46% 61%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-sabone-charcoal text-sabone-cream font-montserrat;
    background-image:
      linear-gradient(120deg, rgba(22, 22, 22, 0.97), rgba(44, 36, 32, 0.97), rgba(22, 22, 22, 0.97)),
      url('/texture-subtle.png'),
      url('/leaf-pattern.png');
    background-size: cover, 200px 200px, cover;
    background-attachment: fixed;
    background-blend-mode: normal, overlay, normal;
    overflow-x: hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-playfair font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl leading-tight;
    letter-spacing: -0.02em;
  }

  h2 {
    @apply text-3xl md:text-4xl leading-tight;
    letter-spacing: -0.01em;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  p {
    @apply leading-relaxed;
    letter-spacing: 0.01em;
  }

  /* Enhanced touch targets */
  @media (max-width: 768px) {
    button, a, [role="button"], input[type="button"], input[type="submit"] {
      @apply min-h-[44px] min-w-[44px];
    }

    .text-sabone-cream {
      @apply text-sabone-cream/95;
    }
  }
}

@layer components {
  .pattern-dot {
    background-image: radial-gradient(rgba(198, 168, 112, 0.3) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .arabesque-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, #C6A870, transparent);
    position: relative;
  }

  .arabesque-divider::before {
    content: "";
    position: absolute;
    top: -14px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 30px;
    background-image: url('/arabesque-icon.svg');
    background-repeat: no-repeat;
    background-position: center;
  }

  .product-card:hover .product-image {
    transform: scale(1.03);
    transition: transform 0.5s ease;
  }

  .gold-text {
    @apply bg-gold-gradient text-transparent bg-clip-text animate-shimmer bg-[length:200%_auto];
  }

  .gold-border {
    @apply border border-sabone-gold/30 shadow-[0_0_10px_rgba(198,168,112,0.15)];
    transition: box-shadow 0.3s ease, border-color 0.3s ease;
  }

  .gold-border:hover {
    @apply border-sabone-gold/40 shadow-[0_0_15px_rgba(198,168,112,0.25)];
  }

  .premium-card {
    @apply bg-sabone-charcoal-warm/80 backdrop-blur-[3px] border border-sabone-gold/20;
    box-shadow: 0 8px 20px -8px rgba(0, 0, 0, 0.3), 0 0 15px rgba(198, 168, 112, 0.1);
    transition: all 0.3s ease;
  }

  .premium-card:hover {
    @apply border-sabone-gold/30;
    box-shadow: 0 10px 25px -8px rgba(0, 0, 0, 0.4), 0 0 20px rgba(198, 168, 112, 0.15);
    transform: translateY(-2px);
  }

  .ambient-glow {
    position: relative;
  }

  .ambient-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(198, 168, 112, 0.08) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
    z-index: 1;
  }

  .ambient-glow:hover::before {
    opacity: 1;
  }

  /* Enhanced touch targets for mobile */
  @media (max-width: 768px) {
    .touch-target {
      @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
    }
  }

  /* Enhanced product card styles */
  .product-card figure {
    @apply w-full h-full;
  }

  /* Mobile-specific product card styles */
  @media (max-width: 640px) {
    .product-card {
      @apply shadow-md;
    }

    .product-card figcaption {
      @apply bg-black/60; /* Slightly darker overlay for better readability on mobile */
      @apply bottom-3 left-3 right-3; /* Slightly smaller margins on mobile */
      @apply p-2; /* Smaller padding on mobile */
    }

    .product-card h3 {
      @apply text-sm; /* Smaller font size on mobile */
    }

    .product-card p {
      @apply line-clamp-1 text-xs; /* Show only one line of description with smaller text on mobile */
    }

    .product-card .price {
      @apply text-xs; /* Smaller price text on mobile */
    }
  }

  /* Product detail page styles */
  .product-detail-image {
    @apply max-h-[600px] w-full object-cover;
  }

  .product-detail-badge {
    @apply inline-flex items-center rounded-full bg-sabone-dark-olive/80 px-2.5 py-0.5 text-xs font-medium text-sabone-gold mr-2 mb-2;
  }

  /* Hover effect for related products */
  .related-product:hover .related-product-image {
    @apply scale-105;
  }

  .gold-border {
    @apply border border-sabone-gold/30 shadow-[0_0_10px_rgba(198,168,112,0.2)];
  }

  /* Enhanced product card overlay */
  .product-card figcaption {
    transform: translateY(0);
    transition: all 0.4s ease;
    backdrop-filter: blur(3px);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }

  .product-card:hover figcaption {
    transform: translateY(-4px);
    background-color: rgba(0, 0, 0, 0.6);
  }

  /* Dropdown menu enhancements */
  [data-radix-popper-content-wrapper] {
    z-index: 100 !important;
  }

  /* Ensure dropdown menus appear above all other content */
  .dropdown-menu-content {
    z-index: 100 !important;
    position: relative;
  }

  /* Fix for dropdown positioning issues */
  [data-state="open"][data-side="bottom"] {
    animation: slideInFromTop 0.2s ease-out;
  }

  [data-state="closed"][data-side="bottom"] {
    animation: slideOutToTop 0.2s ease-in;
  }

  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideOutToTop {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }
}

/* Enhanced animations */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

@keyframes soft-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(198, 168, 112, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(198, 168, 112, 0.5);
  }
}

.animate-soft-glow {
  animation: soft-glow 3s ease-in-out infinite;
}

/* Hero section specific animations */
@keyframes hero-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-fade-in {
  animation: hero-fade-in 1s ease-out forwards;
}

.hero-fade-in-delay-1 {
  animation: hero-fade-in 1s ease-out 0.2s forwards;
  opacity: 0;
}

.hero-fade-in-delay-2 {
  animation: hero-fade-in 1s ease-out 0.4s forwards;
  opacity: 0;
}

.hero-fade-in-delay-3 {
  animation: hero-fade-in 1s ease-out 0.6s forwards;
  opacity: 0;
}

/* Mobile swipe hint animation */
@keyframes swipe-hint {
  0%, 100% {
    transform: translateX(0);
    opacity: 0.7;
  }
  50% {
    transform: translateX(-3px);
    opacity: 1;
  }
}

.animate-swipe-hint {
  animation: swipe-hint 1.5s ease-in-out infinite;
}

/* Mobile touch feedback */
.touch-feedback {
  position: relative;
  overflow: hidden;
}

.touch-feedback::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(198, 168, 112, 0.3);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1) translate(-50%, -50%);
  pointer-events: none;
}

.touch-feedback:active::after {
  opacity: 1;
  transform: scale(20) translate(-50%, -50%);
  transition: transform 0.3s ease-out, opacity 0.2s ease-out;
}

/* App loading and error states */
.app-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: #c6a870;
}

.app-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: #ef4444;
}
