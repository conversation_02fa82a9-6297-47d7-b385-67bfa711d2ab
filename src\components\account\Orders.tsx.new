import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrder } from "@/contexts/OrderContext";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { 
  Eye, 
  AlertCircle, 
  Download, 
  Filter, 
  SortAsc, 
  SortDesc, 
  Calendar, 
  Search,
  RefreshCw,
  Loader2
} from "lucide-react";
import { formatDate, getStatusColor } from "@/types/order";
import OrderReceipt from "./OrderReceipt";

const Orders = () => {
  const { user } = useAuth();
  const { orders, loading, cancelOrder, refreshOrders } = useOrder();
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isReceiptOpen, setIsReceiptOpen] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  
  // Filtering and sorting state
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<'date' | 'total'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [dateRange, setDateRange] = useState<{ from: string | null, to: string | null }>({ from: null, to: null });

  useEffect(() => {
    refreshOrders();
  }, [refreshOrders]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshOrders();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleCancelOrder = async (orderId: string) => {
    setIsCancelling(true);
    try {
      await cancelOrder(orderId);
      setIsDialogOpen(false);
    } finally {
      setIsCancelling(false);
    }
  };
  
  // Filter orders based on current filters
  const filteredOrders = orders.filter(order => {
    // Filter by status
    if (filterStatus && order.status !== filterStatus) {
      return false;
    }
    
    // Filter by search query (order ID)
    if (searchQuery && !order.id.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by date range
    if (dateRange.from) {
      const fromDate = new Date(dateRange.from);
      const orderDate = new Date(order.createdAt);
      if (orderDate < fromDate) {
        return false;
      }
    }
    
    if (dateRange.to) {
      const toDate = new Date(dateRange.to);
      toDate.setHours(23, 59, 59, 999); // End of the day
      const orderDate = new Date(order.createdAt);
      if (orderDate > toDate) {
        return false;
      }
    }
    
    return true;
  });
  
  // Sort filtered orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    if (sortField === 'date') {
      return sortDirection === 'asc' 
        ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else if (sortField === 'total') {
      return sortDirection === 'asc' 
        ? a.total - b.total
        : b.total - a.total;
    }
    return 0;
  });
  
  // Paginate sorted orders
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOrders = sortedOrders.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedOrders.length / itemsPerPage);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Toggle sort direction
  const toggleSort = (field: 'date' | 'total') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Reset filters
  const resetFilters = () => {
    setFilterStatus(null);
    setSearchQuery('');
    setDateRange({ from: null, to: null });
    setSortField('date');
    setSortDirection('desc');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Order History</h2>
          <p className="text-sabone-cream/70 mt-1">View and track your orders</p>
        </div>
        
        <Button 
          variant="outline" 
          size="sm"
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      <Separator className="bg-sabone-gold/20" />
      
      {/* Filters */}
      <div className="bg-sabone-charcoal/50 p-4 rounded-md border border-sabone-gold/20">
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <div className="flex-1">
            <Label htmlFor="search" className="text-sabone-cream mb-2 block">Search Order ID</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-sabone-cream/50" />
              <Input
                id="search"
                placeholder="Search by order ID"
                className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          <div className="w-full md:w-1/4">
            <Label htmlFor="status" className="text-sabone-cream mb-2 block">Status</Label>
            <Select value={filterStatus || ''} onValueChange={(value) => setFilterStatus(value || null)}>
              <SelectTrigger id="status" className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream">
                <SelectItem value="">All statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="w-full md:w-1/4">
            <Label htmlFor="date-from" className="text-sabone-cream mb-2 block">From Date</Label>
            <Input
              id="date-from"
              type="date"
              className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
              value={dateRange.from || ''}
              onChange={(e) => setDateRange({ ...dateRange, from: e.target.value || null })}
            />
          </div>
          
          <div className="w-full md:w-1/4">
            <Label htmlFor="date-to" className="text-sabone-cream mb-2 block">To Date</Label>
            <Input
              id="date-to"
              type="date"
              className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
              value={dateRange.to || ''}
              onChange={(e) => setDateRange({ ...dateRange, to: e.target.value || null })}
            />
          </div>
          
          <Button
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={resetFilters}
          >
            Reset Filters
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-sabone-gold mx-auto" />
          <p className="text-sabone-cream/70 mt-4">Loading your orders...</p>
        </div>
      ) : filteredOrders.length > 0 ? (
        <>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-sabone-gold/20">
                  <TableHead className="text-sabone-gold">Order ID</TableHead>
                  <TableHead className="text-sabone-gold">
                    <button 
                      className="flex items-center focus:outline-none"
                      onClick={() => toggleSort('date')}
                    >
                      Date
                      {sortField === 'date' && (
                        sortDirection === 'asc' ? 
                          <SortAsc className="ml-1 h-4 w-4" /> : 
                          <SortDesc className="ml-1 h-4 w-4" />
                      )}
                    </button>
                  </TableHead>
                  <TableHead className="text-sabone-gold">Status</TableHead>
                  <TableHead className="text-sabone-gold text-right">
                    <button 
                      className="flex items-center ml-auto focus:outline-none"
                      onClick={() => toggleSort('total')}
                    >
                      Total
                      {sortField === 'total' && (
                        sortDirection === 'asc' ? 
                          <SortAsc className="ml-1 h-4 w-4" /> : 
                          <SortDesc className="ml-1 h-4 w-4" />
                      )}
                    </button>
                  </TableHead>
                  <TableHead className="text-sabone-gold text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentOrders.map((order) => (
                  <TableRow key={order.id} className="border-sabone-gold/20">
                    <TableCell className="text-sabone-cream">{order.id}</TableCell>
                    <TableCell className="text-sabone-cream">{formatDate(order.createdAt)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(order.status)}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sabone-cream text-right">${order.total.toFixed(2)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Dialog open={isDialogOpen && selectedOrder?.id === order.id} onOpenChange={(open) => {
                          setIsDialogOpen(open);
                          if (!open) setSelectedOrder(null);
                        }}>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-sabone-gold hover:bg-sabone-gold/10"
                              onClick={() => {
                                setSelectedOrder(order);
                                setIsDialogOpen(true);
                              }}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-3xl">
                            <DialogHeader>
                              <DialogTitle className="text-sabone-gold">Order Details</DialogTitle>
                              <DialogDescription className="text-sabone-cream/70">
                                Order placed on {formatDate(order.createdAt)}
                              </DialogDescription>
                            </DialogHeader>
                            
                            {selectedOrder && (
                              <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                  <div>
                                    <p className="text-sabone-cream/70">Order ID</p>
                                    <p className="text-sabone-cream font-medium">{selectedOrder.id}</p>
                                  </div>
                                  <Badge className={getStatusColor(selectedOrder.status)}>
                                    {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                                  </Badge>
                                </div>
                                
                                <Separator className="bg-sabone-gold/20" />
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <p className="text-sabone-cream/70 mb-1">Shipping Address</p>
                                    <p className="text-sabone-cream">
                                      {selectedOrder.shippingAddress.fullName}<br />
                                      {selectedOrder.shippingAddress.addressLine1}<br />
                                      {selectedOrder.shippingAddress.addressLine2 && `${selectedOrder.shippingAddress.addressLine2}<br />`}
                                      {selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.state} {selectedOrder.shippingAddress.zipCode}<br />
                                      {selectedOrder.shippingAddress.country}
                                    </p>
                                  </div>
                                  
                                  <div>
                                    <p className="text-sabone-cream/70 mb-1">Payment Method</p>
                                    <p className="text-sabone-cream">
                                      {selectedOrder.paymentMethod === 'credit_card' ? 'Credit Card' : 
                                       selectedOrder.paymentMethod === 'paypal' ? 'PayPal' : 
                                       'Cash on Delivery'}
                                    </p>
                                    <p className="text-sabone-cream/70 mt-2 mb-1">Payment Status</p>
                                    <Badge className={
                                      selectedOrder.paymentStatus === 'paid' ? 'bg-green-500/20 text-green-500' :
                                      selectedOrder.paymentStatus === 'pending' ? 'bg-yellow-500/20 text-yellow-500' :
                                      selectedOrder.paymentStatus === 'failed' ? 'bg-red-500/20 text-red-500' :
                                      'bg-blue-500/20 text-blue-500'
                                    }>
                                      {selectedOrder.paymentStatus.charAt(0).toUpperCase() + selectedOrder.paymentStatus.slice(1)}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-sabone-gold hover:bg-sabone-gold/10"
                          onClick={() => {
                            setSelectedOrder(order);
                            setIsReceiptOpen(true);
                          }}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Receipt
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <div className="flex flex-col items-center justify-center space-y-4">
            <AlertCircle className="h-12 w-12 text-sabone-gold/50" />
            <p className="text-sabone-cream/70">
              {orders.length > 0 
                ? "No orders match your current filters." 
                : "You haven't placed any orders yet."}
            </p>
            {orders.length > 0 ? (
              <Button
                variant="outline"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                onClick={resetFilters}
              >
                Reset Filters
              </Button>
            ) : (
              <Button
                className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
                onClick={() => window.location.href = '/'}
              >
                Start Shopping
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Orders;
