{"common": {"navigation": {"home": "Home", "shop": "Shop", "about": "About", "contact": "Contact", "account": "Account", "admin": "Admin", "wishlist": "Wishlist"}, "buttons": {"addToCart": "Add to Cart", "buyNow": "Buy Now", "checkout": "Checkout", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "apply": "Apply", "reset": "Reset", "filter": "Filter", "sort": "Sort", "search": "Search", "clear": "Clear", "close": "Close", "addToWishlist": "Add to Wishlist", "removeFromWishlist": "Remove from Wishlist", "moveToCart": "Move to Cart"}, "languageSwitcher": {"label": "Change Language", "currentLanguage": "Current Language", "selectLanguage": "Select Language", "languageChanged": "Language changed to {language}"}, "footer": {"aboutUsTitle": "About Us", "aboutUsDescription": "Sabone crafts premium natural soaps and shampoos inspired by ancient Arabic bathing traditions, using only the finest organic ingredients.", "quickLinksTitle": "Quick Links", "linkOurStory": "Our Story", "linkProducts": "Products", "linkIngredients": "Ingredients", "linkSustainability": "Sustainability", "linkBlog": "Blog", "customerCareTitle": "Customer Care", "linkShippingPolicy": "Shipping Policy", "linkReturnsRefunds": "Returns & Refunds", "linkFAQ": "FAQ", "linkTrackOrder": "Track Order", "linkContactUs": "Contact Us", "legalTitle": "Legal", "linkPrivacyPolicy": "Privacy Policy", "linkTermsOfService": "Terms of Service", "linkAccessibility": "Accessibility", "linkCookiePolicy": "<PERSON><PERSON>", "copyright": "Crafted in Berlin. Rooted in Aleppo. © {year} Sabone.store", "navHome": "Home", "navCheckout": "Checkout", "navPrivacy": "Privacy", "navContact": "Contact", "scrollToTopLabel": "Scroll to top"}, "cart": {"title": "Your Cart", "empty": "Your cart is empty", "subtotal": "Subtotal", "shipping": "Shipping", "tax": "Tax", "total": "Total", "checkout": "Proceed to Checkout", "continueShopping": "Continue Shopping", "items": "{count, plural, =0 {0 items} one {1 item} other {# items}}", "remove": "Remove", "update": "Update"}, "wishlist": {"title": "Your Wishlist", "empty": "Your wishlist is empty", "addToCart": "Add to Cart", "remove": "Remove", "moveAllToCart": "Move All to Cart", "clearWishlist": "Clear Wishlist", "items": "{count, plural, =0 {0 items} one {1 item} other {# items}}"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "or": "Or", "signInWith": "Sign in with {provider}", "signUpWith": "Sign up with {provider}", "rememberMe": "Remember me", "welcomeBack": "Welcome Back", "joinUs": "Join Us"}, "hero": {"tagline": "Handcrafted Rituals", "title": "Nature in Ritual", "descriptionHtml": "Artisanal soaps and shampoos rooted in Arabic purity traditions.<br />Reconnect with nature's sacred cleansing ritual.", "ctaButton": "Explore the Collection", "scrollIndicator": "Scroll to discover"}, "productGrid": {"title": "Our Ritual Collection", "description": "Discover our handcrafted products, made with natural ingredients and inspired by ancient Arabic rituals. Each item is carefully formulated to bring the essence of tradition to your daily self-care.", "tabAllProducts": "All Products", "tabSoapBars": "Soap Bars", "tabShampoos": "Shampoos"}}, "product": {"details": {"ingredients": "Ingredients", "benefits": "Benefits", "application": "How to Use", "storage": "Storage", "reviews": "Reviews", "relatedProducts": "Related Products", "inStock": "In Stock", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "quantity": "Quantity", "price": "Price", "totalPrice": "Total Price", "description": "Description", "specifications": "Specifications", "type": "Type", "weight": "Weight", "dimensions": "Dimensions", "madeIn": "Made In", "addToCart": "Add to Cart", "buyNow": "Buy Now", "addToWishlist": "Add to Wishlist", "removeFromWishlist": "Remove from Wishlist"}, "reviews": {"title": "Customer Reviews", "writeReview": "Write a Review", "rating": "Rating", "reviewTitle": "Title", "reviewContent": "Review", "submitReview": "Submit Review", "verifiedPurchase": "Verified Purchase", "helpful": "Helpful", "reportAbuse": "Report Abuse", "noReviews": "No reviews yet", "beTheFirst": "Be the first to review this product", "filterBy": "Filter by", "sortBy": "Sort by", "newest": "Newest", "oldest": "Oldest", "highestRated": "Highest Rated", "lowestRated": "Lowest Rated", "withPhotos": "With Photos", "verifiedOnly": "Verified Purchases Only", "starRating": "{rating} Stars", "reviewCount": "{count, plural, =0 {0 reviews} one {1 review} other {# reviews}}"}}, "checkout": {"title": "Checkout", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "sameAsShipping": "Same as shipping address", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "orderConfirmation": "Order Confirmation", "thankYou": "Thank you for your order!", "orderNumber": "Order Number", "orderDate": "Order Date", "orderStatus": "Order Status", "orderTotal": "Order Total", "shippingMethod": "Shipping Method", "paymentInformation": "Payment Information", "billingInformation": "Billing Information", "shippingInformation": "Shipping Information", "orderDetails": "Order Details", "continueShopping": "Continue Shopping", "viewOrder": "View Order", "firstName": "First Name", "lastName": "Last Name", "address": "Address", "city": "City", "state": "State/Province", "zipCode": "Zip/Postal Code", "country": "Country", "phone": "Phone", "email": "Email", "cardNumber": "Card Number", "cardName": "Name on Card", "expiryDate": "Expiry Date", "cvv": "CVV", "standard": "Standard Shipping", "express": "Express Shipping", "free": "Free Shipping"}, "account": {"title": "My Account", "profile": "Profile", "orders": "Orders", "addresses": "Addresses", "wishlist": "Wishlist", "settings": "Settings", "logout": "Logout", "personalInformation": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "password": "Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "saveChanges": "Save Changes", "orderHistory": "Order History", "noOrders": "No orders yet", "orderNumber": "Order Number", "orderDate": "Order Date", "orderStatus": "Status", "orderTotal": "Total", "viewOrder": "View Order", "reorder": "Reorder", "trackOrder": "Track Order", "cancelOrder": "Cancel Order", "addressBook": "Address Book", "addAddress": "Add Address", "editAddress": "Edit Address", "deleteAddress": "Delete Address", "defaultAddress": "<PERSON><PERSON><PERSON> Address", "makeDefault": "Make Default", "noAddresses": "No addresses yet", "addNewAddress": "Add New Address"}}