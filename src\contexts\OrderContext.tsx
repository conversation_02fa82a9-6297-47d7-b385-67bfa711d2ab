import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { toast } from 'sonner';
import {
  Order,
  Address,
  PaymentMethod
} from '@/types/order';
import {
  getUserOrders,
  createOrder as createOrderService,
  updateOrderStatus,
  cancelOrder as cancelOrderService
} from '@/services/orderService';
import { useInventory } from '@/contexts/InventoryContext';
import { useEmail } from '@/contexts/EmailContext';

interface OrderContextType {
  orders: Order[];
  loading: boolean;
  createOrder: (
    shippingAddress: Address,
    billingAddress: Address | undefined,
    paymentMethod: PaymentMethod,
    paymentId?: string // Add paymentId parameter
  ) => Promise<Order | null>;
  cancelOrder: (orderId: string) => Promise<Order | null>;
  updateStatus: (orderId: string, status: 'processing' | 'shipped' | 'delivered') => Promise<Order | null>;
  refreshOrders: () => Promise<void>;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

export const OrderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const { items, clearCart } = useCart();
  const { checkInventory, processOrderInventory } = useInventory();
  const {
    sendOrderConfirmation,
    sendOrderCancelled,
    sendOrderShipped,
    sendOrderDelivered,
    sendOrderReceipt,
    sendNewOrderAdmin
  } = useEmail();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);

  // Load user orders when authenticated
  useEffect(() => {
    if (isAuthenticated && user?.sub) {
      refreshOrders();
    } else {
      setOrders([]);
    }
  }, [isAuthenticated, user?.sub]);

  // Refresh orders from storage/API
  const refreshOrders = async (): Promise<void> => {
    if (!isAuthenticated || !user?.sub) return;

    setLoading(true);
    try {
      const userOrders = getUserOrders(user.sub);
      setOrders(userOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  // Create a new order
  const createOrder = async (
    shippingAddress: Address,
    billingAddress: Address | undefined,
    paymentMethod: PaymentMethod,
    paymentId?: string // Add paymentId parameter
  ): Promise<Order | null> => {
    if (!isAuthenticated || !user?.sub) {
      toast.error('You must be logged in to place an order');
      return null;
    }

    if (items.length === 0) {
      toast.error('Your cart is empty');
      return null;
    }

    setLoading(true);
    try {
      // Check inventory before creating order
      const inventoryCheck = await checkInventory(items);
      const allItemsAvailable = inventoryCheck.every(item => item.available);

      if (!allItemsAvailable) {
        // Find items that are not available
        const unavailableItems = inventoryCheck.filter(item => !item.available);
        const itemNames = unavailableItems.map(item => {
          const cartItem = items.find(i => i.product.id === item.productId);
          return cartItem ? cartItem.product.name : item.productId;
        });

        toast.error(`Some items are out of stock: ${itemNames.join(', ')}`);
        return null;
      }

      // Create the order with payment ID
      const newOrder = createOrderService(
        user.sub,
        items,
        shippingAddress,
        billingAddress,
        paymentMethod,
        paymentId // Pass the payment ID from Stripe
      );

      // Update inventory
      await processOrderInventory(items);

      // Update the orders list
      setOrders(prevOrders => [...prevOrders, newOrder]);

      // Clear the cart after successful order
      clearCart();

      // Send order confirmation email to customer
      const customerName = shippingAddress.fullName;
      const estimatedDelivery = '5-7 business days'; // This could be calculated based on shipping method

      try {
        // Send confirmation email to customer
        await sendOrderConfirmation(newOrder, customerName, estimatedDelivery);

        // Send notification to admin
        await sendNewOrderAdmin(newOrder, customerName, user.email);
      } catch (error) {
        console.error('Error sending order emails:', error);
        // Don't fail the order creation if email sending fails
      }

      toast.success('Order placed successfully!');
      return newOrder;
    } catch (error) {
      console.error('Error creating order:', error);
      toast.error('Failed to place order');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Cancel an order
  const cancelOrder = async (orderId: string): Promise<Order | null> => {
    if (!isAuthenticated || !user?.sub) {
      toast.error('You must be logged in to cancel an order');
      return null;
    }

    setLoading(true);
    try {
      const cancelledOrder = cancelOrderService(orderId);

      if (cancelledOrder) {
        // Update the orders list
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId ? cancelledOrder : order
          )
        );

        // Send cancellation email
        try {
          const customerName = cancelledOrder.shippingAddress.fullName;
          const cancellationReason = 'Customer requested cancellation'; // This could be a parameter

          await sendOrderCancelled(cancelledOrder, customerName, cancellationReason);
        } catch (error) {
          console.error('Error sending cancellation email:', error);
          // Don't fail the cancellation if email sending fails
        }

        toast.success('Order cancelled successfully');
        return cancelledOrder;
      } else {
        toast.error('Failed to cancel order');
        return null;
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      toast.error('Failed to cancel order');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Update order status
  const updateStatus = async (orderId: string, status: 'processing' | 'shipped' | 'delivered'): Promise<Order | null> => {
    if (!isAuthenticated || !user?.sub) {
      toast.error('You must be logged in to update an order');
      return null;
    }

    setLoading(true);
    try {
      const updatedOrder = updateOrderStatus(orderId, status);

      if (updatedOrder) {
        // Update the orders list
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId ? updatedOrder : order
          )
        );

        // Send appropriate email based on the new status
        try {
          const customerName = updatedOrder.shippingAddress.fullName;

          switch (status) {
            case 'shipped':
              // Generate a tracking number (in a real app, this would come from a shipping provider)
              const trackingNumber = `TRK${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
              await sendOrderShipped(updatedOrder, customerName, trackingNumber);
              break;

            case 'delivered':
              await sendOrderDelivered(updatedOrder, customerName);

              // Also send a receipt email when the order is delivered
              const receiptUrl = `/receipts/order-${updatedOrder.id}.pdf`;
              await sendOrderReceipt(updatedOrder, customerName, receiptUrl);
              break;

            default:
              // No email for processing status
              break;
          }
        } catch (error) {
          console.error(`Error sending ${status} email:`, error);
          // Don't fail the status update if email sending fails
        }

        toast.success(`Order ${status} successfully`);
        return updatedOrder;
      } else {
        toast.error(`Failed to update order to ${status}`);
        return null;
      }
    } catch (error) {
      console.error(`Error updating order to ${status}:`, error);
      toast.error(`Failed to update order to ${status}`);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return (
    <OrderContext.Provider
      value={{
        orders,
        loading,
        createOrder,
        cancelOrder,
        updateStatus,
        refreshOrders
      }}
    >
      {children}
    </OrderContext.Provider>
  );
};

export const useOrder = () => {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrder must be used within an OrderProvider');
  }
  return context;
};
