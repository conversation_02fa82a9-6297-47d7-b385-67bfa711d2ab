import { renderHook, act } from '@testing-library/react';
import { CartProvider, useCart } from '../CartContext';
import { mockProduct, mockLocalStorage, setupTest, cleanupTest } from '@/utils/testHelpers';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

jest.mock('@/services/inventoryService', () => ({
  getProductInventory: jest.fn((productId: string) => ({
    productId,
    isInStock: true,
    stockQuantity: 10,
    reservedQuantity: 0,
    availableQuantity: 10,
  })),
}));

describe('CartContext', () => {
  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
  });

  afterEach(() => {
    cleanupTest();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <CartProvider>{children}</CartProvider>
  );

  describe('Initial State', () => {
    it('should initialize with empty cart', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      expect(result.current.items).toEqual([]);
      expect(result.current.itemCount).toBe(0);
      expect(result.current.subtotal).toBe(0);
      expect(result.current.shipping).toBe(5.99);
      expect(result.current.total).toBe(5.99);
    });

    it('should load cart from localStorage if available', () => {
      const savedCart = [{ product: mockProduct, quantity: 2 }];
      mockLocalStorage.setItem('sabone-cart', JSON.stringify(savedCart));

      const { result } = renderHook(() => useCart(), { wrapper });

      expect(result.current.items).toEqual(savedCart);
      expect(result.current.itemCount).toBe(2);
    });
  });

  describe('addItem', () => {
    it('should add new item to cart', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      expect(result.current.items).toHaveLength(1);
      expect(result.current.items[0]).toEqual({
        product: mockProduct,
        quantity: 1,
      });
      expect(result.current.itemCount).toBe(1);
      expect(toast.success).toHaveBeenCalledWith(`${mockProduct.name} added to cart`);
    });

    it('should increase quantity if item already exists', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      act(() => {
        result.current.addItem(mockProduct, 2);
      });

      expect(result.current.items).toHaveLength(1);
      expect(result.current.items[0].quantity).toBe(3);
      expect(result.current.itemCount).toBe(3);
    });

    it('should respect inventory limits', () => {
      // Mock inventory service to return limited stock
      const { getProductInventory } = require('@/services/inventoryService');
      getProductInventory.mockReturnValue({
        productId: mockProduct.id,
        isInStock: true,
        stockQuantity: 2,
        reservedQuantity: 0,
        availableQuantity: 2,
      });

      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 5); // Try to add more than available
      });

      expect(result.current.items[0].quantity).toBe(2); // Should be limited to available stock
      expect(toast.warning).toHaveBeenCalledWith('Only 2 units available');
    });

    it('should not add out of stock items', () => {
      // Mock inventory service to return out of stock
      const { getProductInventory } = require('@/services/inventoryService');
      getProductInventory.mockReturnValue({
        productId: mockProduct.id,
        isInStock: false,
        stockQuantity: 0,
        reservedQuantity: 0,
        availableQuantity: 0,
      });

      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      expect(result.current.items).toHaveLength(0);
      expect(toast.error).toHaveBeenCalledWith(`${mockProduct.name} is out of stock`);
    });

    it('should save to localStorage after adding item', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      const savedCart = JSON.parse(mockLocalStorage.getItem('sabone-cart') || '[]');
      expect(savedCart).toEqual([{ product: mockProduct, quantity: 1 }]);
    });
  });

  describe('removeItem', () => {
    it('should remove item from cart', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item first
      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      // Remove item
      act(() => {
        result.current.removeItem(mockProduct.id);
      });

      expect(result.current.items).toHaveLength(0);
      expect(result.current.itemCount).toBe(0);
      expect(toast.success).toHaveBeenCalledWith(`${mockProduct.name} removed from cart`);
    });

    it('should update localStorage after removing item', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item first
      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      // Remove item
      act(() => {
        result.current.removeItem(mockProduct.id);
      });

      const savedCart = JSON.parse(mockLocalStorage.getItem('sabone-cart') || '[]');
      expect(savedCart).toEqual([]);
    });
  });

  describe('updateQuantity', () => {
    it('should update item quantity', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item first
      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      // Update quantity
      act(() => {
        result.current.updateQuantity(mockProduct.id, 3);
      });

      expect(result.current.items[0].quantity).toBe(3);
      expect(result.current.itemCount).toBe(3);
    });

    it('should not update to quantity less than 1', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item first
      act(() => {
        result.current.addItem(mockProduct, 2);
      });

      // Try to update to invalid quantity
      act(() => {
        result.current.updateQuantity(mockProduct.id, 0);
      });

      expect(result.current.items[0].quantity).toBe(2); // Should remain unchanged
    });

    it('should respect inventory limits when updating', () => {
      // Mock inventory service to return limited stock
      const { getProductInventory } = require('@/services/inventoryService');
      getProductInventory.mockReturnValue({
        productId: mockProduct.id,
        isInStock: true,
        stockQuantity: 2,
        reservedQuantity: 0,
        availableQuantity: 2,
      });

      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item first
      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      // Try to update to more than available
      act(() => {
        result.current.updateQuantity(mockProduct.id, 5);
      });

      expect(result.current.items[0].quantity).toBe(2); // Should be limited
      expect(toast.warning).toHaveBeenCalledWith('Only 2 units available');
    });
  });

  describe('clearCart', () => {
    it('should clear all items from cart', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add items first
      act(() => {
        result.current.addItem(mockProduct, 2);
      });

      // Clear cart
      act(() => {
        result.current.clearCart();
      });

      expect(result.current.items).toHaveLength(0);
      expect(result.current.itemCount).toBe(0);
      expect(result.current.subtotal).toBe(0);
      expect(toast.success).toHaveBeenCalledWith('Cart cleared');
    });

    it('should remove cart from localStorage', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item first
      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      // Clear cart
      act(() => {
        result.current.clearCart();
      });

      expect(mockLocalStorage.getItem('sabone-cart')).toBeNull();
    });
  });

  describe('Calculations', () => {
    it('should calculate subtotal correctly', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 2); // 2 * 12.99 = 25.98
      });

      expect(result.current.subtotal).toBe(25.98);
    });

    it('should calculate total correctly', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      act(() => {
        result.current.addItem(mockProduct, 2); // 2 * 12.99 = 25.98
      });

      expect(result.current.total).toBe(31.97); // 25.98 + 5.99 shipping
    });

    it('should update calculations when items change', () => {
      const { result } = renderHook(() => useCart(), { wrapper });

      // Add item
      act(() => {
        result.current.addItem(mockProduct, 1);
      });

      expect(result.current.subtotal).toBe(12.99);
      expect(result.current.total).toBe(18.98);

      // Update quantity
      act(() => {
        result.current.updateQuantity(mockProduct.id, 3);
      });

      expect(result.current.subtotal).toBe(38.97);
      expect(result.current.total).toBe(44.96);
    });
  });

  describe('Error Handling', () => {
    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        renderHook(() => useCart());
      }).toThrow('useCart must be used within a CartProvider');

      consoleSpy.mockRestore();
    });
  });
});
