import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  Target,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Package,
  Lightbulb
} from 'lucide-react';
import { useProducts } from '@/contexts/ProductContext';
import { inventoryAnalyticsService, InventoryForecast } from '@/services/inventoryAnalyticsService';
import { logger } from '@/utils/logger';

interface InventoryForecastingProps {
  className?: string;
}

const InventoryForecasting: React.FC<InventoryForecastingProps> = ({ className = '' }) => {
  const { products } = useProducts();
  const [forecasts, setForecasts] = useState<InventoryForecast[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [forecastPeriod, setForecastPeriod] = useState<number>(30);

  // Load forecasts
  useEffect(() => {
    loadForecasts();
  }, [products, forecastPeriod]);

  const loadForecasts = async () => {
    setLoading(true);
    try {
      const forecastData = inventoryAnalyticsService.getInventoryForecasts(products);
      setForecasts(forecastData);

      if (forecastData.length > 0 && !selectedProduct) {
        setSelectedProduct(forecastData[0].productId);
      }

      logger.userAction('inventory_forecasts_loaded', {
        productCount: forecastData.length,
        forecastPeriod
      });
    } catch (error) {
      logger.error('Failed to load inventory forecasts', error);
    } finally {
      setLoading(false);
    }
  };

  const getSelectedForecast = (): InventoryForecast | null => {
    return forecasts.find(f => f.productId === selectedProduct) || null;
  };

  const getActionPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-500 border-red-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-500 border-yellow-500/30';
      case 'low':
        return 'bg-blue-500/20 text-blue-500 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-500 border-gray-500/30';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'reorder':
        return <Package className="h-4 w-4" />;
      case 'reduce_price':
        return <TrendingDown className="h-4 w-4" />;
      case 'promote':
        return <TrendingUp className="h-4 w-4" />;
      case 'discontinue':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getStockTrend = (forecast: InventoryForecast) => {
    if (forecast.forecastedStock.length < 2) return 'stable';

    const firstWeek = forecast.forecastedStock.slice(0, 7);
    const lastWeek = forecast.forecastedStock.slice(-7);

    const firstWeekAvg = firstWeek.reduce((sum, f) => sum + f.predictedStock, 0) / firstWeek.length;
    const lastWeekAvg = lastWeek.reduce((sum, f) => sum + f.predictedStock, 0) / lastWeek.length;

    const change = ((lastWeekAvg - firstWeekAvg) / firstWeekAvg) * 100;

    if (change > 10) return 'increasing';
    if (change < -10) return 'decreasing';
    return 'stable';
  };

  const getStockTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <BarChart3 className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStockOutDate = (forecast: InventoryForecast): string | null => {
    const stockOutDay = forecast.forecastedStock.find(f => f.predictedStock <= 0);
    return stockOutDay ? stockOutDay.date : null;
  };

  if (loading) {
    return (
      <Card className={`p-6 bg-sabone-dark-olive/60 border-sabone-gold/20 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-sabone-gold/20 rounded w-1/3"></div>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-20 bg-sabone-gold/10 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  const selectedForecast = getSelectedForecast();

  return (
    <Card className={`p-6 bg-sabone-dark-olive/60 border-sabone-gold/20 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Calendar className="h-6 w-6 text-sabone-gold mr-2" />
          <h3 className="text-xl font-playfair font-semibold text-sabone-gold">
            Inventory Forecasting
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <select
            value={forecastPeriod}
            onChange={(e) => setForecastPeriod(Number(e.target.value))}
            className="bg-sabone-charcoal/30 border border-sabone-gold/20 text-sabone-cream rounded px-3 py-2"
          >
            <option value={7}>7 Days</option>
            <option value={14}>14 Days</option>
            <option value={30}>30 Days</option>
          </select>
          <Button
            onClick={loadForecasts}
            size="sm"
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            Refresh
          </Button>
        </div>
      </div>

      {forecasts.length === 0 ? (
        <div className="text-center py-8">
          <Calendar className="h-12 w-12 text-sabone-cream/40 mx-auto mb-4" />
          <p className="text-sabone-cream/60">No forecast data available</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Product Selection */}
          <div className="space-y-4">
            <h4 className="font-medium text-sabone-gold">Select Product</h4>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {forecasts.map((forecast) => {
                const trend = getStockTrend(forecast);
                const stockOutDate = getStockOutDate(forecast);

                return (
                  <div
                    key={forecast.productId}
                    onClick={() => setSelectedProduct(forecast.productId)}
                    className={`p-3 rounded cursor-pointer transition-all duration-200 ${
                      selectedProduct === forecast.productId
                        ? 'bg-sabone-gold/20 border border-sabone-gold/40'
                        : 'bg-sabone-charcoal/30 hover:bg-sabone-charcoal/50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sabone-cream text-sm">
                          {forecast.productName}
                        </p>
                        <p className="text-xs text-sabone-cream/60">
                          Current: {forecast.currentStock} units
                        </p>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getStockTrendIcon(trend)}
                        {stockOutDate && (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Forecast Details */}
          <div className="lg:col-span-2">
            {selectedForecast ? (
              <Tabs defaultValue="forecast" className="space-y-4">
                <TabsList className="grid w-full grid-cols-2 bg-sabone-charcoal/30">
                  <TabsTrigger value="forecast" className="data-[state=active]:bg-sabone-gold/20">
                    Forecast
                  </TabsTrigger>
                  <TabsTrigger value="actions" className="data-[state=active]:bg-sabone-gold/20">
                    Recommendations
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="forecast" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="p-4 bg-sabone-charcoal/30 border-sabone-gold/20">
                      <div className="flex items-center">
                        <Package className="h-5 w-5 text-sabone-gold mr-2" />
                        <div>
                          <p className="text-sm text-sabone-cream/80">Current Stock</p>
                          <p className="text-lg font-bold text-sabone-gold">
                            {selectedForecast.currentStock}
                          </p>
                        </div>
                      </div>
                    </Card>

                    <Card className="p-4 bg-sabone-charcoal/30 border-sabone-gold/20">
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 text-sabone-gold mr-2" />
                        <div>
                          <p className="text-sm text-sabone-cream/80">Forecast Period</p>
                          <p className="text-lg font-bold text-sabone-gold">
                            {forecastPeriod} Days
                          </p>
                        </div>
                      </div>
                    </Card>

                    <Card className="p-4 bg-sabone-charcoal/30 border-sabone-gold/20">
                      <div className="flex items-center">
                        {getStockTrendIcon(getStockTrend(selectedForecast))}
                        <div className="ml-2">
                          <p className="text-sm text-sabone-cream/80">Trend</p>
                          <p className="text-lg font-bold text-sabone-gold capitalize">
                            {getStockTrend(selectedForecast)}
                          </p>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Stock Out Warning */}
                  {getStockOutDate(selectedForecast) && (
                    <Card className="p-4 bg-red-500/10 border-red-500/30">
                      <div className="flex items-center">
                        <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                        <div>
                          <p className="font-medium text-red-500">Stock Out Warning</p>
                          <p className="text-sm text-red-400">
                            Predicted to run out on {new Date(getStockOutDate(selectedForecast)!).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </Card>
                  )}

                  {/* Forecast Chart (Simplified) */}
                  <Card className="p-4 bg-sabone-charcoal/30 border-sabone-gold/20">
                    <h5 className="font-medium text-sabone-gold mb-4">Stock Forecast</h5>
                    <div className="space-y-2">
                      {selectedForecast.forecastedStock.slice(0, 10).map((forecast, index) => (
                        <div key={index} className="flex items-center justify-between text-sm">
                          <span className="text-sabone-cream/80">
                            {new Date(forecast.date).toLocaleDateString()}
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sabone-cream">
                              {forecast.predictedStock} units
                            </span>
                            <Badge variant="outline" className="bg-sabone-gold/20 text-sabone-gold border-sabone-gold/30 text-xs">
                              {Math.round(forecast.confidence * 100)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </TabsContent>

                <TabsContent value="actions" className="space-y-4">
                  <div className="space-y-3">
                    {selectedForecast.recommendedActions.length === 0 ? (
                      <Card className="p-4 bg-green-500/10 border-green-500/30">
                        <div className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                          <div>
                            <p className="font-medium text-green-500">No Actions Needed</p>
                            <p className="text-sm text-green-400">
                              Inventory levels are optimal for this product
                            </p>
                          </div>
                        </div>
                      </Card>
                    ) : (
                      selectedForecast.recommendedActions.map((action, index) => (
                        <Card key={index} className="p-4 bg-sabone-charcoal/30 border-sabone-gold/20">
                          <div className="flex items-start space-x-3">
                            <div className="flex items-center space-x-2">
                              {getActionIcon(action.action)}
                              <Badge variant="outline" className={getActionPriorityColor(action.priority)}>
                                {action.priority}
                              </Badge>
                            </div>
                            <div className="flex-1">
                              <h6 className="font-medium text-sabone-cream capitalize mb-1">
                                {action.action.replace('_', ' ')}
                              </h6>
                              <p className="text-sm text-sabone-cream/80 mb-2">
                                {action.description}
                              </p>
                              <p className="text-xs text-sabone-cream/60">
                                <strong>Impact:</strong> {action.estimatedImpact}
                              </p>
                            </div>
                          </div>
                        </Card>
                      ))
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            ) : (
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-sabone-cream/40 mx-auto mb-4" />
                <p className="text-sabone-cream/60">Select a product to view forecast</p>
              </div>
            )}
          </div>
        </div>
      )}
    </Card>
  );
};

export default InventoryForecasting;
