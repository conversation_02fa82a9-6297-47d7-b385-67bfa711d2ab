import { useEffect } from "react";
import { useOrder } from "@/contexts/OrderContext";
import { Helmet, Helm<PERSON>Provider } from 'react-helmet-async';
import PageLoader from "@/components/ui/PageLoader";
import { useAuth } from "@/contexts/AuthContext";

import { formatCurrency } from "@/utils/formatCurrency";
import { format } from "date-fns";
import { toast } from "sonner";

const AdminOrders = () => {
  const { orders, loading, refreshOrders } = useOrder();
  const { user } = useAuth();

  useEffect(() => {
    if (user?.role === 'admin') {
      refreshOrders();
    }
  }, [user?.role, refreshOrders]);

  if (loading) {
    return <PageLoader />;
  }

  return (
    <HelmetProvider>
      <Helmet>
        <title>Sabone | Admin - Order Management</title>
        <meta name="description" content="Admin order management dashboard for Sabone's luxury natural soaps and shampoos." />
      </Helmet>

      <div className="min-h-screen bg-sabone-charcoal">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold text-sabone-gold mb-8">Order Management</h1>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-sabone-gold/20">
                  <th className="py-3 text-left text-sabone-gold">Order ID</th>
                  <th className="py-3 text-left text-sabone-gold">Date</th>
                  <th className="py-3 text-left text-sabone-gold">Customer</th>
                  <th className="py-3 text-left text-sabone-gold">Total</th>
                  <th className="py-3 text-left text-sabone-gold">Status</th>
                  <th className="py-3 text-left text-sabone-gold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {orders.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-6 text-center text-sabone-gold/70">
                      No orders found
                    </td>
                  </tr>
                ) : (
                  orders.map((order) => (
                    <tr key={order.id} className="border-b border-sabone-gold/10">
                      <td className="py-4 text-sabone-gold/80">{order.id}</td>
                      <td className="py-4 text-sabone-gold/80">
                        {format(new Date(order.createdAt), 'MMM d, yyyy')}
                      </td>
                      <td className="py-4 text-sabone-gold/80">
                        {order.shippingAddress.fullName}
                      </td>
                      <td className="py-4 text-sabone-gold/80">
                        {formatCurrency(order.total)}
                      </td>
                      <td className="py-4 text-sabone-gold/80">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          order.status === 'processing' ? 'bg-sabone-gold/20 text-sabone-gold' :
                          order.status === 'shipped' ? 'bg-sabone-olive/20 text-sabone-olive' :
                          'bg-sabone-rose/20 text-sabone-rose'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="py-4">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              // In a real app, this would open an order details modal
                              toast.info(`View order details: ${order.id}`);
                            }}
                            className="px-3 py-1 bg-sabone-gold/20 text-sabone-gold rounded hover:bg-sabone-gold/30 transition-colors"
                          >
                            View
                          </button>
                          <button
                            onClick={() => {
                              // In a real app, this would update the order status
                              toast.success(`Order ${order.id} status updated to shipped`);
                            }}
                            className="px-3 py-1 bg-sabone-olive/20 text-sabone-olive rounded hover:bg-sabone-olive/30 transition-colors"
                          >
                            Mark as Shipped
                          </button>
                          <button
                            onClick={() => {
                              // In a real app, this would cancel the order
                              toast.error(`Order ${order.id} cancelled`);
                            }}
                            className="px-3 py-1 bg-sabone-rose/20 text-sabone-rose rounded hover:bg-sabone-rose/30 transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </HelmetProvider>
  );
};

export default AdminOrders;
