import sgMail from '@sendgrid/mail';
import {
  EmailOptions,
  EmailType,
  OrderEmailData,
  WelcomeEmailData,
  PasswordResetEmailData,
  AccountUpdateEmailData,
  LowStockAlertData,
  NewOrderAdminData,
  SalesSummaryData,
  NewsletterData,
  PromotionalEmailData,
  ReceiptEmailData,
  EmailPreferences
} from '@/types/email';

import { toast } from 'sonner';

// Initialize SendGrid with API key
const initializeSendGrid = (): boolean => {
  try {
    const apiKey = import.meta.env.VITE_SENDGRID_API_KEY;
    if (!apiKey) {
      console.warn('SendGrid API key not found. Email functionality will be limited.');
      return false;
    }

    sgMail.setApiKey(apiKey);
    return true;
  } catch (error) {
    console.error('Error initializing SendGrid:', error);
    return false;
  }
};

// Default sender information
const DEFAULT_SENDER = {
  email: '<EMAIL>',
  name: 'Sabone Store'
};

// Admin email for notifications
const ADMIN_EMAIL = '<EMAIL>';

// Storage key for email preferences
const EMAIL_PREFERENCES_STORAGE_KEY = 'sabone-email-preferences';

// Get user email preferences
export const getUserEmailPreferences = (userId: string): EmailPreferences | null => {
  try {
    const preferencesJson = localStorage.getItem(EMAIL_PREFERENCES_STORAGE_KEY);
    if (!preferencesJson) return null;

    const allPreferences: EmailPreferences[] = JSON.parse(preferencesJson);
    return allPreferences.find(pref => pref.userId === userId) || null;
  } catch (error) {
    console.error('Error getting email preferences:', error);
    return null;
  }
};

// Save user email preferences
export const saveUserEmailPreferences = (preferences: EmailPreferences): boolean => {
  try {
    const preferencesJson = localStorage.getItem(EMAIL_PREFERENCES_STORAGE_KEY);
    const allPreferences: EmailPreferences[] = preferencesJson ? JSON.parse(preferencesJson) : [];

    const existingIndex = allPreferences.findIndex(pref => pref.userId === preferences.userId);
    if (existingIndex >= 0) {
      allPreferences[existingIndex] = preferences;
    } else {
      allPreferences.push(preferences);
    }

    localStorage.setItem(EMAIL_PREFERENCES_STORAGE_KEY, JSON.stringify(allPreferences));
    return true;
  } catch (error) {
    console.error('Error saving email preferences:', error);
    return false;
  }
};

// Create default email preferences for a new user
export const createDefaultEmailPreferences = (userId: string): EmailPreferences => {
  const preferences: EmailPreferences = {
    userId, // userId will always be a string here
    orderNotifications: true,
    accountNotifications: true,
    marketingEmails: true,
    lastUpdated: new Date().toISOString()
  };

  saveUserEmailPreferences(preferences);
  return preferences;
};

// Check if user has opted in for a specific email type
export const hasUserOptedIn = (userId: string | undefined, emailType: EmailType): boolean => {
  if (!userId) return false; // If no userId, assume opted out or not applicable
  const preferences = getUserEmailPreferences(userId) || createDefaultEmailPreferences(userId);

  switch (emailType) {
    case 'order_confirmation':
    case 'order_shipped':
    case 'order_delivered':
    case 'order_cancelled':
    case 'order_receipt':
      return preferences.orderNotifications;

    case 'welcome':
    case 'password_reset':
    case 'account_update':
      return preferences.accountNotifications;

    case 'newsletter':
    case 'promotional':
      return preferences.marketingEmails;

    // Admin notifications are always sent
    case 'low_stock_alert':
    case 'new_order_admin':
    case 'sales_summary':
      return true;

    default:
      return true;
  }
};

// Send email using SendGrid
export const sendEmail = async (options: EmailOptions): Promise<boolean> => {
  if (!initializeSendGrid()) {
    console.error('Failed to initialize SendGrid');
    return false;
  }

  try {
    await sgMail.send(options);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
};

// Preview email (for development)
export const previewEmail = (options: EmailOptions): void => {
  console.log('Email Preview:');
  console.log('To:', options.to);
  console.log('From:', options.from);
  console.log('Subject:', options.subject);
  console.log('HTML Content:', options.html);

  // In a real application, you might want to render this in a modal or a new window
  toast.info('Email preview logged to console');
};

// Generate email content based on template and data
import {
  generateOrderConfirmationEmail,
  generateOrderShippedEmail,
  generateOrderDeliveredEmail,
  generateOrderCancelledEmail,
  generateWelcomeEmail,
  generatePasswordResetEmail,
  generateAccountUpdateEmail,
  generateLowStockAlertEmail,
  generateNewOrderAdminEmail,
  generateSalesSummaryEmail,
  generateNewsletterEmail,
  generatePromotionalEmail,
  generateReceiptEmail
} from '@/templates/emailTemplates';

// Send order confirmation email
export const sendOrderConfirmationEmail = async (data: OrderEmailData): Promise<boolean> => {
  if (!hasUserOptedIn(data.order.userId, 'order_confirmation')) return false;

  const { subject, html } = generateOrderConfirmationEmail(data);

  const options: EmailOptions = {
    to: { email: data.customerEmail, name: data.customerName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send order shipped email
export const sendOrderShippedEmail = async (data: OrderEmailData): Promise<boolean> => {
  if (!hasUserOptedIn(data.order.userId, 'order_shipped')) return false;

  const { subject, html } = generateOrderShippedEmail(data);

  const options: EmailOptions = {
    to: { email: data.customerEmail, name: data.customerName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send order delivered email
export const sendOrderDeliveredEmail = async (data: OrderEmailData): Promise<boolean> => {
  if (!hasUserOptedIn(data.order.userId, 'order_delivered')) return false;

  const { subject, html } = generateOrderDeliveredEmail(data);

  const options: EmailOptions = {
    to: { email: data.customerEmail, name: data.customerName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send order cancelled email
export const sendOrderCancelledEmail = async (data: OrderEmailData): Promise<boolean> => {
  if (!hasUserOptedIn(data.order.userId, 'order_cancelled')) return false;

  const { subject, html } = generateOrderCancelledEmail(data);

  const options: EmailOptions = {
    to: { email: data.customerEmail, name: data.customerName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send order receipt email
export const sendOrderReceiptEmail = async (data: ReceiptEmailData): Promise<boolean> => {
  if (!hasUserOptedIn(data.order.userId, 'order_receipt')) return false;

  const { subject, html } = generateReceiptEmail(data);

  const options: EmailOptions = {
    to: { email: data.customerEmail, name: data.customerName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send welcome email
export const sendWelcomeEmail = async (data: WelcomeEmailData, email: string): Promise<boolean> => {
  const { subject, html } = generateWelcomeEmail(data);

  const options: EmailOptions = {
    to: { email, name: data.userName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send password reset email
export const sendPasswordResetEmail = async (data: PasswordResetEmailData, email: string): Promise<boolean> => {
  const { subject, html } = generatePasswordResetEmail(data);

  const options: EmailOptions = {
    to: { email, name: data.userName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send account update email
export const sendAccountUpdateEmail = async (data: AccountUpdateEmailData, email: string): Promise<boolean> => {
  // data.userId is optional in AccountUpdateEmailData, but hasUserOptedIn now handles undefined
  if (!hasUserOptedIn(data.userId, 'account_update')) return false;

  const { subject, html } = generateAccountUpdateEmail(data);

  const options: EmailOptions = {
    to: { email, name: data.userName },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send low stock alert to admin
export const sendLowStockAlertEmail = async (data: LowStockAlertData): Promise<boolean> => {
  const { subject, html } = generateLowStockAlertEmail(data);

  const options: EmailOptions = {
    to: { email: ADMIN_EMAIL, name: 'Sabone Admin' },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send new order notification to admin
export const sendNewOrderAdminEmail = async (data: NewOrderAdminData): Promise<boolean> => {
  const { subject, html } = generateNewOrderAdminEmail(data);

  const options: EmailOptions = {
    to: { email: ADMIN_EMAIL, name: 'Sabone Admin' },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send sales summary to admin
export const sendSalesSummaryEmail = async (data: SalesSummaryData): Promise<boolean> => {
  const { subject, html } = generateSalesSummaryEmail(data);

  const options: EmailOptions = {
    to: { email: ADMIN_EMAIL, name: 'Sabone Admin' },
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send newsletter to subscribers
export const sendNewsletterEmail = async (data: NewsletterData, subscribers: string[]): Promise<boolean> => {
  if (subscribers.length === 0) return false;

  const { subject, html } = generateNewsletterEmail(data);

  const options: EmailOptions = {
    to: subscribers.map(email => ({ email })),
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};

// Send promotional email to subscribers
export const sendPromotionalEmail = async (data: PromotionalEmailData, subscribers: string[]): Promise<boolean> => {
  if (subscribers.length === 0) return false;

  const { subject, html } = generatePromotionalEmail(data);

  const options: EmailOptions = {
    to: subscribers.map(email => ({ email })),
    from: DEFAULT_SENDER,
    subject,
    html
  };

  return await sendEmail(options);
};
