import { Review, ReviewStatus, ReviewSummary, ReviewFilterOptions } from "@/types/review";

const REVIEWS_STORAGE_KEY = 'sabone-reviews';
const REVIEW_SUMMARIES_KEY = 'sabone-review-summaries';

// Get all reviews for a product
export const getProductReviews = (productId: string, status: ReviewStatus | 'all' = 'approved'): Review[] => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    return reviews.filter(review => 
      review.productId === productId && 
      (status === 'all' || review.status === status)
    );
  } catch (error) {
    console.error('Error getting product reviews:', error);
    return [];
  }
};

// Get filtered reviews for a product
export const getFilteredReviews = (
  productId: string, 
  options: ReviewFilterOptions
): Review[] => {
  try {
    let reviews = getProductReviews(productId, 'approved');
    
    // Apply filters
    if (options.minRating !== undefined) {
      reviews = reviews.filter(review => review.rating >= options.minRating!);
    }
    
    if (options.maxRating !== undefined) {
      reviews = reviews.filter(review => review.rating <= options.maxRating!);
    }
    
    if (options.verifiedOnly) {
      reviews = reviews.filter(review => review.isVerifiedPurchase);
    }
    
    if (options.withImages) {
      reviews = reviews.filter(review => review.images && review.images.length > 0);
    }
    
    // Apply sorting
    switch (options.sortBy) {
      case 'newest':
        reviews.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        reviews.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'highest-rating':
        reviews.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest-rating':
        reviews.sort((a, b) => a.rating - b.rating);
        break;
    }
    
    return reviews;
  } catch (error) {
    console.error('Error getting filtered reviews:', error);
    return [];
  }
};

// Get all reviews by a user
export const getUserReviews = (userId: string): Review[] => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    return reviews.filter(review => review.userId === userId);
  } catch (error) {
    console.error('Error getting user reviews:', error);
    return [];
  }
};

// Add a new review
export const addReview = (review: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Review => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    
    const newReview: Review = {
      ...review,
      id: `review_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    reviews.push(newReview);
    localStorage.setItem(REVIEWS_STORAGE_KEY, JSON.stringify(reviews));
    
    // Update review summary
    updateReviewSummary(review.productId);
    
    return newReview;
  } catch (error) {
    console.error('Error adding review:', error);
    throw new Error('Failed to add review');
  }
};

// Update a review
export const updateReview = (reviewId: string, updates: Partial<Review>): Review => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    
    const reviewIndex = reviews.findIndex(r => r.id === reviewId);
    if (reviewIndex === -1) {
      throw new Error('Review not found');
    }
    
    const updatedReview: Review = {
      ...reviews[reviewIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    reviews[reviewIndex] = updatedReview;
    localStorage.setItem(REVIEWS_STORAGE_KEY, JSON.stringify(reviews));
    
    // Update review summary if rating changed
    if (updates.rating !== undefined || updates.status !== undefined) {
      updateReviewSummary(updatedReview.productId);
    }
    
    return updatedReview;
  } catch (error) {
    console.error('Error updating review:', error);
    throw new Error('Failed to update review');
  }
};

// Delete a review
export const deleteReview = (reviewId: string): boolean => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    
    const reviewIndex = reviews.findIndex(r => r.id === reviewId);
    if (reviewIndex === -1) {
      throw new Error('Review not found');
    }
    
    const productId = reviews[reviewIndex].productId;
    reviews.splice(reviewIndex, 1);
    localStorage.setItem(REVIEWS_STORAGE_KEY, JSON.stringify(reviews));
    
    // Update review summary
    updateReviewSummary(productId);
    
    return true;
  } catch (error) {
    console.error('Error deleting review:', error);
    return false;
  }
};

// Get review summary for a product
export const getReviewSummary = (productId: string): ReviewSummary => {
  try {
    const summariesJson = localStorage.getItem(REVIEW_SUMMARIES_KEY);
    const summaries: Record<string, ReviewSummary> = summariesJson ? JSON.parse(summariesJson) : {};
    
    return summaries[productId] || {
      productId,
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    };
  } catch (error) {
    console.error('Error getting review summary:', error);
    return {
      productId,
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    };
  }
};

// Update review summary for a product
const updateReviewSummary = (productId: string): void => {
  try {
    const reviews = getProductReviews(productId, 'approved');
    
    if (reviews.length === 0) {
      const summariesJson = localStorage.getItem(REVIEW_SUMMARIES_KEY);
      const summaries: Record<string, ReviewSummary> = summariesJson ? JSON.parse(summariesJson) : {};
      
      summaries[productId] = {
        productId,
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      };
      
      localStorage.setItem(REVIEW_SUMMARIES_KEY, JSON.stringify(summaries));
      return;
    }
    
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;
    
    const ratingDistribution = {
      1: reviews.filter(r => r.rating === 1).length,
      2: reviews.filter(r => r.rating === 2).length,
      3: reviews.filter(r => r.rating === 3).length,
      4: reviews.filter(r => r.rating === 4).length,
      5: reviews.filter(r => r.rating === 5).length
    };
    
    const summary: ReviewSummary = {
      productId,
      averageRating,
      totalReviews: reviews.length,
      ratingDistribution
    };
    
    const summariesJson = localStorage.getItem(REVIEW_SUMMARIES_KEY);
    const summaries: Record<string, ReviewSummary> = summariesJson ? JSON.parse(summariesJson) : {};
    
    summaries[productId] = summary;
    localStorage.setItem(REVIEW_SUMMARIES_KEY, JSON.stringify(summaries));
  } catch (error) {
    console.error('Error updating review summary:', error);
  }
};

// Check if user has purchased product (for verified purchase badge)
export const hasUserPurchasedProduct = (userId: string, productId: string): boolean => {
  // In a real application, this would check the order history
  // For now, we'll simulate this with a simple check
  try {
    const ordersJson = localStorage.getItem('sabone-orders');
    const orders = ordersJson ? JSON.parse(ordersJson) : [];
    
    return orders.some((order: any) => 
      order.userId === userId && 
      order.items.some((item: any) => item.productId === productId)
    );
  } catch (error) {
    console.error('Error checking purchase history:', error);
    return false;
  }
};

// Get all pending reviews (for admin moderation)
export const getPendingReviews = (): Review[] => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    return reviews.filter(review => review.status === 'pending');
  } catch (error) {
    console.error('Error getting pending reviews:', error);
    return [];
  }
};

// Add admin response to a review
export const addAdminResponse = (
  reviewId: string, 
  response: string, 
  adminId: string
): Review => {
  try {
    const reviewsJson = localStorage.getItem(REVIEWS_STORAGE_KEY);
    const reviews: Review[] = reviewsJson ? JSON.parse(reviewsJson) : [];
    
    const reviewIndex = reviews.findIndex(r => r.id === reviewId);
    if (reviewIndex === -1) {
      throw new Error('Review not found');
    }
    
    const updatedReview: Review = {
      ...reviews[reviewIndex],
      adminResponse: {
        content: response,
        respondedBy: adminId,
        respondedAt: new Date().toISOString()
      },
      updatedAt: new Date().toISOString()
    };
    
    reviews[reviewIndex] = updatedReview;
    localStorage.setItem(REVIEWS_STORAGE_KEY, JSON.stringify(reviews));
    
    return updatedReview;
  } catch (error) {
    console.error('Error adding admin response:', error);
    throw new Error('Failed to add admin response');
  }
};
