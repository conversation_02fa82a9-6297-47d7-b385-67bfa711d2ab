import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import {
  BarChart3,
  TrendingUp,
  ShoppingCart,
  Eye,
  RefreshCw,
  Download
} from 'lucide-react';
import { useRecommendations } from '@/contexts/RecommendationContext';
import { logger } from '@/utils/logger';

interface AnalyticsData {
  totalRecommendations: number;
  clickThroughRate: number;
  conversionRate: number;
  topPerformingProducts: Array<{
    productId: string;
    productName: string;
    clicks: number;
    conversions: number;
    ctr: number;
  }>;
  recommendationTypes: Array<{
    type: string;
    impressions: number;
    clicks: number;
    conversions: number;
    ctr: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    impressions: number;
    clicks: number;
    conversions: number;
  }>;
}

const RecommendationAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState('7d');
  const { clearRecommendationCache } = useRecommendations();

  // Mock analytics data - in a real app, this would come from your analytics service
  const generateMockAnalytics = (): AnalyticsData => {
    return {
      totalRecommendations: 1247,
      clickThroughRate: 12.5,
      conversionRate: 3.2,
      topPerformingProducts: [
        {
          productId: 'lemon-verbena',
          productName: 'Lemon Verbena Fresh Bar',
          clicks: 89,
          conversions: 12,
          ctr: 13.5
        },
        {
          productId: 'rose-clay',
          productName: 'Rose Clay Glow Bar',
          clicks: 76,
          conversions: 8,
          ctr: 10.5
        },
        {
          productId: 'royal-oud',
          productName: 'Royal Oud Bar',
          clicks: 65,
          conversions: 11,
          ctr: 16.9
        }
      ],
      recommendationTypes: [
        {
          type: 'Customers Also Bought',
          impressions: 2341,
          clicks: 312,
          conversions: 45,
          ctr: 13.3
        },
        {
          type: 'Recommended for You',
          impressions: 1876,
          clicks: 198,
          conversions: 28,
          ctr: 10.6
        },
        {
          type: 'Recently Viewed',
          impressions: 1543,
          clicks: 156,
          conversions: 19,
          ctr: 10.1
        },
        {
          type: 'Trending Products',
          impressions: 987,
          clicks: 87,
          conversions: 12,
          ctr: 8.8
        }
      ],
      timeSeriesData: [
        { date: '2024-01-01', impressions: 234, clicks: 28, conversions: 4 },
        { date: '2024-01-02', impressions: 267, clicks: 31, conversions: 6 },
        { date: '2024-01-03', impressions: 198, clicks: 22, conversions: 3 },
        { date: '2024-01-04', impressions: 312, clicks: 41, conversions: 8 },
        { date: '2024-01-05', impressions: 289, clicks: 35, conversions: 5 },
        { date: '2024-01-06', impressions: 345, clicks: 43, conversions: 7 },
        { date: '2024-01-07', impressions: 298, clicks: 38, conversions: 6 }
      ]
    };
  };

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateMockAnalytics();
      setAnalyticsData(data);
      
      logger.userAction('analytics_viewed', {
        section: 'recommendation_analytics',
        dateRange
      });
    } catch (error) {
      logger.error('Failed to fetch recommendation analytics', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleRefresh = () => {
    clearRecommendationCache();
    fetchAnalytics();
  };

  const handleExport = () => {
    if (!analyticsData) return;
    
    const csvData = [
      ['Metric', 'Value'],
      ['Total Recommendations', analyticsData.totalRecommendations.toString()],
      ['Click Through Rate', `${analyticsData.clickThroughRate}%`],
      ['Conversion Rate', `${analyticsData.conversionRate}%`],
      [''],
      ['Recommendation Type', 'Impressions', 'Clicks', 'Conversions', 'CTR'],
      ...analyticsData.recommendationTypes.map(type => [
        type.type,
        type.impressions.toString(),
        type.clicks.toString(),
        type.conversions.toString(),
        `${type.ctr}%`
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `recommendation-analytics-${dateRange}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    logger.userAction('analytics_exported', {
      section: 'recommendation_analytics',
      dateRange
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-playfair font-bold text-sabone-gold">
            Recommendation Analytics
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
              <div className="animate-pulse">
                <div className="h-4 bg-sabone-gold/20 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-sabone-gold/20 rounded w-1/2"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-8">
        <p className="text-sabone-cream/60">Failed to load analytics data</p>
        <Button onClick={fetchAnalytics} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-playfair font-bold text-sabone-gold">
          Recommendation Analytics
        </h2>
        <div className="flex gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="bg-sabone-dark-olive/60 border border-sabone-gold/20 text-sabone-cream rounded px-3 py-2"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Total Impressions</p>
              <p className="text-2xl font-bold text-sabone-gold">
                {analyticsData.totalRecommendations.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Click Through Rate</p>
              <p className="text-2xl font-bold text-sabone-gold">
                {analyticsData.clickThroughRate}%
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <ShoppingCart className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Conversion Rate</p>
              <p className="text-2xl font-bold text-sabone-gold">
                {analyticsData.conversionRate}%
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-sabone-gold mr-3" />
            <div>
              <p className="text-sm text-sabone-cream/80">Performance</p>
              <Badge variant="outline" className="bg-green-500/20 text-green-500 border-green-500/30">
                +12.5%
              </Badge>
            </div>
          </div>
        </Card>
      </div>

      {/* Recommendation Types Performance */}
      <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
        <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">
          Performance by Recommendation Type
        </h3>
        <div className="space-y-4">
          {analyticsData.recommendationTypes.map((type, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-sabone-charcoal/30 rounded">
              <div>
                <p className="font-medium text-sabone-cream">{type.type}</p>
                <p className="text-sm text-sabone-cream/60">
                  {type.impressions.toLocaleString()} impressions
                </p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-sabone-gold">{type.ctr}%</p>
                <p className="text-sm text-sabone-cream/60">
                  {type.clicks} clicks, {type.conversions} conversions
                </p>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Top Performing Products */}
      <Card className="p-6 bg-sabone-dark-olive/60 border-sabone-gold/20">
        <h3 className="text-lg font-playfair font-semibold text-sabone-gold mb-4">
          Top Performing Products
        </h3>
        <div className="space-y-3">
          {analyticsData.topPerformingProducts.map((product, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-sabone-charcoal/30 rounded">
              <div>
                <p className="font-medium text-sabone-cream">{product.productName}</p>
                <p className="text-sm text-sabone-cream/60">
                  {product.clicks} clicks, {product.conversions} conversions
                </p>
              </div>
              <Badge variant="outline" className="bg-sabone-gold/20 text-sabone-gold border-sabone-gold/30">
                {product.ctr}% CTR
              </Badge>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default RecommendationAnalytics;
