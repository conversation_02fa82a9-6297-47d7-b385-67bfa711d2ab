import React, { useEffect } from 'react';
import { logger } from './logger';

// Type definitions for performance APIs
interface LayoutShift extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
}

interface PerformanceMetrics {
  FCP?: number; // First Contentful Paint
  LCP?: number; // Largest Contentful Paint
  FID?: number; // First Input Delay
  CLS?: number; // Cumulative Layout Shift
  TTFB?: number; // Time to First Byte
  TTI?: number; // Time to Interactive
}

interface ResourceTiming {
  name: string;
  duration: number;
  transferSize: number;
  initiatorType: string;
}

/**
 * Performance monitoring utility for tracking Core Web Vitals and custom metrics
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics = {};
  private customMarks: Map<string, number> = new Map();
  private resourceTimings: ResourceTiming[] = [];

  private constructor() {
    this.initializeObservers();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Initialize performance observers for Core Web Vitals
   */
  private initializeObservers(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    // Observe FCP
    try {
      const fcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntriesByName('first-contentful-paint');
        if (entries.length > 0 && entries[0]) {
          this.metrics.FCP = entries[0].startTime;
          logger.info('FCP measured', { FCP: this.metrics.FCP });
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
    } catch (e: unknown) {
      logger.error('Failed to observe FCP', e instanceof Error ? e : new Error(String(e)));
    }

    // Observe LCP
    try {
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        if (entries.length > 0) {
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            this.metrics.LCP = lastEntry.startTime;
            logger.info('LCP measured', { LCP: this.metrics.LCP });
          }
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e: unknown) {
      logger.error('Failed to observe LCP', e instanceof Error ? e : new Error(String(e)));
    }

    // Observe FID
    try {
      const fidObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        if (entries.length > 0) {
          const firstInput = entries[0] as PerformanceEventTiming;
          this.metrics.FID = firstInput.processingStart - firstInput.startTime;
          logger.info('FID measured', { FID: this.metrics.FID });
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e: unknown) {
      logger.error('Failed to observe FID', e instanceof Error ? e : new Error(String(e)));
    }

    // Observe CLS
    try {
      let clsValue = 0;
      const clsEntries: LayoutShift[] = [];

      const clsObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const layoutShift = entry as LayoutShift;
          if (!layoutShift.hadRecentInput) {
            clsEntries.push(layoutShift);
            clsValue += layoutShift.value;
          }
        }
        this.metrics.CLS = clsValue;
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e: unknown) {
      logger.error('Failed to observe CLS', e instanceof Error ? e : new Error(String(e)));
    }

    // Measure TTFB
    this.measureTTFB();
  }

  /**
   * Measure Time to First Byte
   */
  private measureTTFB(): void {
    try {
      const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationTiming) {
        this.metrics.TTFB = navigationTiming.responseStart - navigationTiming.requestStart;
        logger.info('TTFB measured', { TTFB: this.metrics.TTFB });
      }
    } catch (e: unknown) {
      logger.error('Failed to measure TTFB', e instanceof Error ? e : new Error(String(e)));
    }
  }

  /**
   * Mark a custom performance point
   * @param markName - Name of the performance mark
   */
  mark(markName: string): void {
    try {
      performance.mark(markName);
      this.customMarks.set(markName, performance.now());
    } catch (e: unknown) {
      logger.error('Failed to create performance mark', e instanceof Error ? e : new Error(String(e)), { markName });
    }
  }

  /**
   * Measure between two marks
   * @param measureName - Name for the measure
   * @param startMark - Start mark name
   * @param endMark - End mark name (optional, defaults to now)
   */
  measure(measureName: string, startMark: string, endMark?: string): number | null {
    try {
      if (endMark) {
        performance.measure(measureName, startMark, endMark);
      } else {
        performance.measure(measureName, startMark);
      }

      const measures = performance.getEntriesByName(measureName, 'measure');
      if (measures.length > 0 && measures[0]) {
        const duration = measures[0].duration;
        logger.info('Performance measure', { measureName, duration });
        return duration;
      }
    } catch (e: unknown) {
      logger.error('Failed to measure performance', e instanceof Error ? e : new Error(String(e)), { measureName });
    }
    return null;
  }

  /**
   * Track component render time
   * @param componentName - Name of the component
   * @param renderTime - Time taken to render in ms
   */
  trackComponentRender(componentName: string, renderTime: number): void {
    if (renderTime > 16) { // More than one frame (60fps)
      logger.warn('Slow component render', { componentName, renderTime });
    }
  }

  /**
   * Track API call performance
   * @param endpoint - API endpoint
   * @param duration - Duration in ms
   * @param status - HTTP status code
   */
  trackAPICall(endpoint: string, duration: number, status: number): void {
    const isError = status >= 400;
    const isSlow = duration > 1000; // More than 1 second

    if (isError || isSlow) {
      logger.warn('API performance issue', { endpoint, duration, status, isError, isSlow });
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get resource timings for analysis
   */
  getResourceTimings(): ResourceTiming[] {
    try {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      this.resourceTimings = resources.map(resource => ({
        name: resource.name,
        duration: resource.duration,
        transferSize: resource.transferSize,
        initiatorType: resource.initiatorType,
      }));
      return this.resourceTimings;
    } catch (e: unknown) {
      logger.error('Failed to get resource timings', e instanceof Error ? e : new Error(String(e)));
      return [];
    }
  }

  /**
   * Analyze bundle sizes and identify large resources
   */
  analyzeBundleSizes(): { totalSize: number; largeResources: ResourceTiming[] } {
    const resources = this.getResourceTimings();
    const jsResources = resources.filter(r => r.name.endsWith('.js'));
    const totalSize = jsResources.reduce((sum, r) => sum + r.transferSize, 0);
    const largeResources = jsResources.filter(r => r.transferSize > 100000); // 100KB

    if (largeResources.length > 0) {
      logger.warn('Large JavaScript bundles detected', {
        totalSize,
        largeResources: largeResources.map(r => ({ name: r.name, size: r.transferSize }))
      });
    }

    return { totalSize, largeResources };
  }

  /**
   * Report performance metrics to analytics
   */
  reportMetrics(): void {
    const metrics = this.getMetrics();
    const bundleAnalysis = this.analyzeBundleSizes();

    // Log summary
    logger.info('Performance Report', {
      metrics,
      bundleAnalysis: {
        totalJSSize: bundleAnalysis.totalSize,
        largeResourceCount: bundleAnalysis.largeResources.length,
      },
    });

    // Check against thresholds
    this.checkPerformanceThresholds(metrics);
  }

  /**
   * Check if metrics meet performance budgets
   */
  private checkPerformanceThresholds(metrics: PerformanceMetrics): void {
    const thresholds = {
      FCP: 1800, // 1.8s
      LCP: 2500, // 2.5s
      FID: 100, // 100ms
      CLS: 0.1, // 0.1
      TTFB: 800, // 800ms
    };

    Object.entries(thresholds).forEach(([metric, threshold]) => {
      const value = metrics[metric as keyof PerformanceMetrics];
      if (value && value > threshold) {
        logger.error(`Performance budget exceeded for ${metric}`, undefined, {
          metric,
          value,
          threshold,
          exceeded: value - threshold,
        });
      }
    });
  }

  /**
   * Start monitoring a specific operation
   * @param operationName - Name of the operation
   * @returns Function to call when operation completes
   */
  startOperation(operationName: string): () => void {
    const startTime = performance.now();
    this.mark(`${operationName}-start`);

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.mark(`${operationName}-end`);
      this.measure(operationName, `${operationName}-start`, `${operationName}-end`);

      if (duration > 100) {
        logger.warn('Slow operation detected', { operationName, duration });
      }
    };
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * React hook for tracking component performance
 */
export const usePerformanceTracking = (componentName: string) => {
  const startTime = performance.now();

  return {
    trackRender: () => {
      const renderTime = performance.now() - startTime;
      performanceMonitor.trackComponentRender(componentName, renderTime);
    },
  };
};

/**
 * Higher-order component for performance tracking
 */
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
): React.ComponentType<P> {
  return (props: P) => {
    const { trackRender } = usePerformanceTracking(componentName);

    useEffect(() => {
      trackRender();
    });

    return React.createElement(Component, props);
  };
}
