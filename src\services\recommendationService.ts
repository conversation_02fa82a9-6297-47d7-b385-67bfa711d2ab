import { Product } from '@/data/products';
import {
  RecommendationResult,
  RecommendationScore,
  getContentBasedRecommendations,
  getRecentlyViewedRecommendations,
  getPopularityRecommendations,
  getUserBehavior,
  getSessionId,
  _STORAGE_KEYS,
  RECOMMENDATION_CONFIG
} from '@/utils/recommendationUtils';

/**
 * Recommendation Service
 * Handles all recommendation logic and caching
 */
class RecommendationService {
  private cache = new Map<string, { data: RecommendationResult; timestamp: number }>();

  /**
   * Get "Customers Also Bought" recommendations
   * Based on products frequently bought together
   */
  async getCustomersAlsoBought(
    productId: string,
    allProducts: Product[],
    maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECOMMENDATIONS
  ): Promise<RecommendationResult> {
    const cacheKey = `customers-also-bought-${productId}`;
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const targetProduct = allProducts.find(p => p.id === productId);
    if (!targetProduct) {
      return this.createEmptyResult('customers-also-bought');
    }

    // For now, use content-based similarity as a proxy for "customers also bought"
    // In a real app, this would be based on actual purchase correlation data
    const recommendations = getContentBasedRecommendations(
      targetProduct,
      allProducts,
      maxRecommendations
    ).map(rec => ({
      ...rec,
      reason: 'Customers who bought this item also bought',
      algorithm: 'collaborative' as const
    }));

    const result: RecommendationResult = {
      recommendations,
      metadata: {
        algorithm: 'customers-also-bought',
        timestamp: Date.now(),
        sessionId: getSessionId()
      }
    };

    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Get "Recommended for You" personalized recommendations
   * Based on user behavior and preferences
   */
  async getRecommendedForYou(
    allProducts: Product[],
    maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECOMMENDATIONS
  ): Promise<RecommendationResult> {
    const sessionId = getSessionId();
    const cacheKey = `recommended-for-you-${sessionId}`;
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const userBehavior = getUserBehavior(sessionId);
    const recommendations: RecommendationScore[] = [];

    // If user has viewed products, use content-based recommendations
    if (userBehavior.productViews.length > 0) {
      const recentViews = userBehavior.productViews
        .slice(-5) // Last 5 viewed products
        .map(view => allProducts.find(p => p.id === view.productId))
        .filter(Boolean) as Product[];

      // Get recommendations based on recently viewed products
      for (const viewedProduct of recentViews) {
        const contentRecs = getContentBasedRecommendations(
          viewedProduct,
          allProducts,
          3 // Fewer per product to allow variety
        );
        
        // Boost scores based on how recently the product was viewed
        const viewTime = userBehavior.productViews.find(v => v.productId === viewedProduct.id)?.timestamp || 0;
        const recencyBoost = Math.max(0, 1 - (Date.now() - viewTime) / (24 * 60 * 60 * 1000));
        
        contentRecs.forEach(rec => {
          rec.score *= (1 + recencyBoost * 0.5);
          rec.reason = `Based on your interest in ${viewedProduct.name}`;
        });

        recommendations.push(...contentRecs);
      }
    }

    // If user has added items to cart, boost similar products
    if (userBehavior.cartAdditions.length > 0) {
      const cartProducts = userBehavior.cartAdditions
        .map(addition => allProducts.find(p => p.id === addition.productId))
        .filter(Boolean) as Product[];

      for (const cartProduct of cartProducts) {
        const cartRecs = getContentBasedRecommendations(cartProduct, allProducts, 2);
        cartRecs.forEach(rec => {
          rec.score *= 1.3; // Boost cart-based recommendations
          rec.reason = `Complements items in your cart`;
        });
        recommendations.push(...cartRecs);
      }
    }

    // If no personalized data, fall back to popularity
    if (recommendations.length === 0) {
      recommendations.push(...getPopularityRecommendations(allProducts, maxRecommendations));
    }

    // Remove duplicates and sort by score
    const uniqueRecommendations = this.removeDuplicateRecommendations(recommendations)
      .slice(0, maxRecommendations);

    const result: RecommendationResult = {
      recommendations: uniqueRecommendations,
      metadata: {
        algorithm: 'recommended-for-you',
        timestamp: Date.now(),
        sessionId
      }
    };

    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Get "Recently Viewed" recommendations
   */
  async getRecentlyViewed(
    allProducts: Product[],
    maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECENTLY_VIEWED
  ): Promise<RecommendationResult> {
    const sessionId = getSessionId();
    const recommendations = getRecentlyViewedRecommendations(sessionId, allProducts, maxRecommendations);

    return {
      recommendations,
      metadata: {
        algorithm: 'recently-viewed',
        timestamp: Date.now(),
        sessionId
      }
    };
  }

  /**
   * Get "Frequently Bought Together" recommendations for cart/checkout
   */
  async getFrequentlyBoughtTogether(
    cartProductIds: string[],
    allProducts: Product[],
    maxRecommendations: number = 4
  ): Promise<RecommendationResult> {
    const cacheKey = `frequently-bought-together-${cartProductIds.sort().join('-')}`;
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const cartProducts = allProducts.filter(p => cartProductIds.includes(p.id));
    const recommendations: RecommendationScore[] = [];

    // For each product in cart, find similar products
    for (const cartProduct of cartProducts) {
      const similarProducts = getContentBasedRecommendations(cartProduct, allProducts, 3)
        .filter(rec => !cartProductIds.includes(rec.productId)) // Exclude items already in cart
        .map(rec => ({
          ...rec,
          reason: `Frequently bought with ${cartProduct.name}`,
          algorithm: 'collaborative' as const
        }));

      recommendations.push(...similarProducts);
    }

    // Remove duplicates and get top recommendations
    const uniqueRecommendations = this.removeDuplicateRecommendations(recommendations)
      .slice(0, maxRecommendations);

    const result: RecommendationResult = {
      recommendations: uniqueRecommendations,
      metadata: {
        algorithm: 'frequently-bought-together',
        timestamp: Date.now(),
        sessionId: getSessionId()
      }
    };

    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Get popular/trending products
   */
  async getTrendingProducts(
    allProducts: Product[],
    maxRecommendations: number = RECOMMENDATION_CONFIG.MAX_RECOMMENDATIONS
  ): Promise<RecommendationResult> {
    const cacheKey = 'trending-products';
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const recommendations = getPopularityRecommendations(allProducts, maxRecommendations)
      .map(rec => ({
        ...rec,
        reason: 'Trending now',
        algorithm: 'popularity' as const
      }));

    const result: RecommendationResult = {
      recommendations,
      metadata: {
        algorithm: 'trending',
        timestamp: Date.now(),
        sessionId: getSessionId()
      }
    };

    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Remove duplicate recommendations and merge scores
   */
  private removeDuplicateRecommendations(recommendations: RecommendationScore[]): RecommendationScore[] {
    const productMap = new Map<string, RecommendationScore>();

    recommendations.forEach(rec => {
      const existing = productMap.get(rec.productId);
      if (existing) {
        // Merge scores (take the higher one and add a small boost for multiple sources)
        existing.score = Math.max(existing.score, rec.score) + 0.1;
        existing.reason = existing.reason.includes('multiple') 
          ? existing.reason 
          : `${existing.reason} (multiple factors)`;
      } else {
        productMap.set(rec.productId, { ...rec });
      }
    });

    return Array.from(productMap.values()).sort((a, b) => b.score - a.score);
  }

  /**
   * Get cached result if valid
   */
  private getCachedResult(key: string): RecommendationResult | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < RECOMMENDATION_CONFIG.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  /**
   * Set cached result
   */
  private setCachedResult(key: string, result: RecommendationResult): void {
    this.cache.set(key, {
      data: result,
      timestamp: Date.now()
    });

    // Clean up old cache entries
    if (this.cache.size > 50) {
      const oldestKey = Array.from(this.cache.keys())[0];
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Create empty result
   */
  private createEmptyResult(algorithm: string): RecommendationResult {
    return {
      recommendations: [],
      metadata: {
        algorithm,
        timestamp: Date.now(),
        sessionId: getSessionId()
      }
    };
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const recommendationService = new RecommendationService();
export default recommendationService;
