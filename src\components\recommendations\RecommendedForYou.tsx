import React, { useState, useEffect } from 'react';
import { Product } from '@/data/products';
import { useRecommendations } from '@/contexts/RecommendationContext';
import { useAuth } from '@/contexts/AuthContext';
import RecommendationSection from './RecommendationSection';

interface RecommendedForYouProps {
  className?: string;
  maxVisible?: number;
  showForGuests?: boolean;
}

const RecommendedForYou: React.FC<RecommendedForYouProps> = ({
  className = '',
  maxVisible = 6,
  showForGuests = true
}) => {
  const [recommendations, setRecommendations] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { getRecommendedForYou } = useRecommendations();
  const { isAuthenticated } = useAuth();

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);
      const products = await getRecommendedForYou();
      setRecommendations(products);
    } catch (err) {
      setError('Failed to load personalized recommendations');
      console.error('Error fetching recommended for you:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only show recommendations if user is authenticated or if we show for guests
    if (isAuthenticated || showForGuests) {
      fetchRecommendations();
    }
  }, [isAuthenticated, showForGuests, getRecommendedForYou]);

  // Don't render if user is not authenticated and we don't show for guests
  if (!isAuthenticated && !showForGuests) {
    return null;
  }

  const title = isAuthenticated ? "Recommended for You" : "You Might Like";
  const subtitle = isAuthenticated 
    ? "Personalized picks based on your preferences"
    : "Popular products you might enjoy";

  return (
    <RecommendationSection
      title={title}
      subtitle={subtitle}
      products={recommendations}
      loading={loading}
      error={error}
      onRefresh={fetchRecommendations}
      maxVisible={maxVisible}
      showRefreshButton={true}
      className={className}
      trackingType="recommended_for_you"
    />
  );
};

export default RecommendedForYou;
