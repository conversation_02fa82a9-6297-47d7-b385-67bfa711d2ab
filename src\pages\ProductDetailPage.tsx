import { useEffect, useState, Suspense } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { HelmetProvider } from 'react-helmet-async';
import { useProducts } from "@/contexts/ProductContext";
import { useCart } from "@/contexts/CartContext";
import { useInventory } from "@/contexts/InventoryContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { useRecommendations } from "@/contexts/RecommendationContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ProductBreadcrumb from "@/components/product/ProductBreadcrumb";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { LazyReviewSection, LazyCustomersAlsoBought, LazyRecentlyViewed } from "@/components/LazyComponents";
import { Heart, ArrowLef<PERSON>, Minus, Plus, AlertTriangle as _AlertTriangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import OptimizedImage from "@/components/OptimizedImage";
import SEO from "@/components/seo/SEO";
import ProductSchema from "@/components/seo/ProductSchema";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";

const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addItem } = useCart();
  const { getProductStock, isProductInStock, isLowStock } = useInventory();
  const { products, getProductById } = useProducts();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { trackProductView, trackCartAddition } = useRecommendations();
  const [quantity, setQuantity] = useState(1);
  const [_imageLoaded, setImageLoaded] = useState(false);

  // Find the product by ID
  const product = getProductById(id);

  // Get related products (same type)
  const _relatedProducts = product
    ? products.filter(p => p.type === product.type && p.id !== product.id).slice(0, 3)
    : [];

  useEffect(() => {
    document.body.classList.add("bg-sabone-charcoal");
    window.scrollTo(0, 0);

    // Track product view when component mounts
    if (id) {
      trackProductView(id);
    }

    return () => {
      document.body.classList.remove("bg-sabone-charcoal");
    };
  }, [id, trackProductView]);

  // Get inventory information
  const stockQuantity = product ? getProductStock(product.id) : 0;
  const inStock = product ? isProductInStock(product.id) : false;
  const lowStock = product ? isLowStock(product.id) : false;

  const handleAddToCart = () => {
    if (product && inStock) {
      // Limit quantity to available stock
      const qtyToAdd = Math.min(quantity, stockQuantity);
      addItem(product, qtyToAdd);

      // Track cart addition for recommendations
      trackCartAddition(product.id, qtyToAdd);
    }
  };

  const handleQuantityChange = (value: number) => {
    setQuantity(Math.max(1, value));
  };

  const toggleWishlist = () => {
    if (!product) return;

    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  if (!product) {
    return (
      <div className="min-h-screen bg-sabone-charcoal flex flex-col">
        <Navbar />
        <main className="flex-grow py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-3xl font-playfair font-bold text-sabone-gold mb-4">
              Product Not Found
            </h1>
            <p className="text-sabone-cream/80 mb-8">
              The product you're looking for doesn't exist or has been removed.
            </p>
            <Button
              onClick={() => navigate('/')}
              className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
            >
              Return to Home
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <HelmetProvider>
      <SEO
        title={product.name}
        description={product.description}
        canonical={`product/${product.id}`}
        type="product"
        image={product.image}
      />

      {/* Enhanced Product Schema */}
      <ProductSchema
        product={product}
      />

      {/* Breadcrumb Schema */}
      <BreadcrumbSchema
        items={[
          { name: 'Home', url: '/', position: 1 },
          { name: product.type === 'bar' ? 'Soap Bars' : 'Shampoos', url: '/', position: 2 },
          { name: product.name, url: `/product/${product.id}`, position: 3 }
        ]}
      />

      <div className="min-h-screen bg-sabone-charcoal flex flex-col">
        <Navbar />

        <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <ProductBreadcrumb product={product} />

            <article className="mt-6 grid md:grid-cols-2 gap-8 lg:gap-12">
              {/* Product Image with ambient lighting effect */}
              <div className="relative bg-sabone-charcoal-warm/50 rounded-lg overflow-hidden gold-border ambient-glow">
                {/* Radial gradient ambient lighting */}
                <div className="absolute inset-0 bg-radial-glow opacity-70 pointer-events-none z-0"></div>

                <OptimizedImage
                  src={product.image}
                  alt={product.name}
                  className="w-full h-auto relative z-10"
                  objectFit="cover"
                  priority={true}
                  onLoad={() => setImageLoaded(true)}
                />

                {/* Subtle texture overlay */}
                <div className="absolute inset-0 bg-texture-overlay opacity-10 mix-blend-overlay pointer-events-none"></div>
              </div>

              {/* Product Details */}
              <div className="flex flex-col">
                <h1 className="text-3xl md:text-4xl font-playfair font-bold bg-gold-gradient-rich text-transparent bg-clip-text">
                  {product.name}
                </h1>

                <div className="mt-4 mb-6">
                  <span className="text-2xl font-semibold text-sabone-gold-accent">${product.price.toFixed(2)}</span>
                </div>

                <p className="text-sabone-cream/90 mb-6 leading-relaxed">
                  {product.description}
                </p>

                {/* Enhanced visual separator */}
                <div className="w-16 h-0.5 bg-sabone-gold/30 mb-6 animate-soft-glow"></div>

                <Separator className="bg-sabone-gold/20 my-6" />

                <div className="mb-6">
                  <h2 className="text-xl font-playfair font-semibold text-sabone-gold mb-3">Ingredients</h2>
                  <ul className="grid grid-cols-2 gap-2">
                    {product.ingredients.map((ingredient, index) => (
                      <li key={index} className="flex items-center">
                        <span className="text-sabone-gold mr-2">•</span>
                        <span className="text-sabone-cream/80">{ingredient}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-8">
                  <h2 className="text-xl font-playfair font-semibold text-sabone-gold mb-3">Benefits</h2>
                  <ul className="space-y-2">
                    {product.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-sabone-gold mr-2">•</span>
                        <span className="text-sabone-cream/80">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <Separator className="bg-sabone-gold/20 my-6" />

                <div className="mt-auto">
                  <div className="flex items-center space-x-4 mb-2">
                    <div className="flex items-center bg-sabone-charcoal-deep/30 backdrop-blur-[3px] px-3 py-1 rounded-full border border-sabone-gold/20">
                      <span className="text-sabone-cream mr-4 font-medium">Quantity</span>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 rounded-full bg-sabone-dark-olive/60 border-sabone-gold/30 hover:bg-sabone-dark-olive transition-all duration-200 hover:border-sabone-gold/50"
                        onClick={() => handleQuantityChange(quantity - 1)}
                        disabled={!inStock || quantity <= 1}
                        aria-label="Decrease quantity"
                      >
                        <Minus className="h-3 w-3 text-sabone-gold" />
                      </Button>
                      <span className="w-12 text-center text-sabone-gold-light font-medium">{quantity}</span>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 rounded-full bg-sabone-dark-olive/60 border-sabone-gold/30 hover:bg-sabone-dark-olive transition-all duration-200 hover:border-sabone-gold/50"
                        onClick={() => handleQuantityChange(Math.min(stockQuantity, quantity + 1))}
                        disabled={!inStock || quantity >= stockQuantity}
                        aria-label="Increase quantity"
                      >
                        <Plus className="h-3 w-3 text-sabone-gold" />
                      </Button>
                    </div>
                  </div>

                  <div className="mb-4">
                    {!inStock ? (
                      <Badge variant="destructive" className="bg-red-500/20 text-red-500 hover:bg-red-500/30">
                        Out of Stock
                      </Badge>
                    ) : lowStock ? (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30 border-yellow-500/30">
                          Low Stock
                        </Badge>
                        <span className="text-yellow-500 text-sm">Only {stockQuantity} left</span>
                      </div>
                    ) : (
                      <Badge variant="outline" className="bg-green-500/20 text-green-500 hover:bg-green-500/30 border-green-500/30">
                        In Stock
                      </Badge>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1 flex gap-2">
                      <Button
                        className="flex-1 bg-sabone-gold hover:bg-sabone-gold-rich text-sabone-charcoal-deep font-medium py-6 transition-all duration-300 hover:shadow-[0_0_15px_rgba(198,168,112,0.3)] relative overflow-hidden group"
                        onClick={handleAddToCart}
                        disabled={!inStock}
                      >
                        <span className="relative z-10">{inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                        <span className="absolute inset-0 bg-sabone-gold-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </Button>

                      <Button
                        variant="outline"
                        className={`border-sabone-gold/30 py-6 min-w-[60px] ${
                          isInWishlist(product.id)
                            ? 'bg-sabone-gold/10 text-sabone-gold'
                            : 'text-sabone-gold hover:bg-sabone-gold/10'
                        }`}
                        onClick={toggleWishlist}
                        aria-label={isInWishlist(product.id) ? `Remove from wishlist` : `Add to wishlist`}
                      >
                        <Heart className={isInWishlist(product.id) ? 'fill-sabone-gold' : ''} size={20} />
                      </Button>
                    </div>

                    <Link to="/">
                      <Button
                        variant="outline"
                        className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-dark-olive/80 hover:text-sabone-gold-accent py-6 w-full sm:w-auto backdrop-blur-[2px] transition-all duration-300 hover:border-sabone-gold/50"
                      >
                        <ArrowLeft className="mr-2 h-4 w-4 group-hover:animate-gentle-bounce" />
                        <span>Back to Shop</span>
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </article>

            {/* Reviews Section - Lazy Loaded */}
            <Suspense fallback={
              <div className="mt-16">
                <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-8">
                  Customer Reviews
                </h2>
                <Skeleton className="h-[400px] w-full rounded-md bg-sabone-dark-olive/30" />
              </div>
            }>
              <LazyReviewSection product={product} />
            </Suspense>

            {/* Customers Also Bought - Lazy Loaded */}
            <Suspense fallback={
              <div className="mt-16">
                <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-8">
                  Customers Also Bought
                </h2>
                <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
              </div>
            }>
              <LazyCustomersAlsoBought productId={product.id} className="mt-16" />
            </Suspense>

            {/* Recently Viewed - Lazy Loaded */}
            <Suspense fallback={
              <div className="mt-16">
                <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-8">
                  Recently Viewed
                </h2>
                <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
              </div>
            }>
              <LazyRecentlyViewed excludeCurrentProduct={product.id} className="mt-16" />
            </Suspense>
          </div>
        </main>

        <Footer />
      </div>
    </HelmetProvider>
  );
};

export default ProductDetailPage;
