import { useState, useEffect, useCallback } from 'react';
import { useProducts } from '@/contexts/ProductContext';
import { useReviews } from '@/contexts/ReviewContext';
import { 
  businessAnalyticsService, 
  BusinessAnalytics,
  SalesMetrics,
  UserBehaviorMetrics,
  ConversionFunnelMetrics,
  KPIMetrics
} from '@/services/businessAnalyticsService';
import { logger } from '@/utils/logger';

interface UseBusinessAnalyticsResult {
  analytics: BusinessAnalytics | null;
  loading: boolean;
  error: string | null;
  refreshAnalytics: () => Promise<void>;
  clearCache: () => void;
  exportToCSV: () => string | null;
  recordSale: (productId: string, revenue: number, quantity: number) => void;
  trackUserSession: (sessionId: string, data: any) => void;
  recordConversionEvent: (stage: string, sessionId: string, data?: any) => void;
}

export const useBusinessAnalytics = (): UseBusinessAnalyticsResult => {
  const { products } = useProducts();
  const { reviews } = useReviews();
  const [analytics, setAnalytics] = useState<BusinessAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load analytics data
   */
  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const analyticsData = businessAnalyticsService.getCachedBusinessAnalytics(products, reviews);
      setAnalytics(analyticsData);
      
      logger.performance('business_analytics_loaded', Date.now(), {
        hasData: !!analyticsData,
        productsCount: products.length,
        reviewsCount: reviews.length
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics';
      setError(errorMessage);
      logger.error('Failed to load business analytics', err);
    } finally {
      setLoading(false);
    }
  }, [products, reviews]);

  /**
   * Refresh analytics data (force recalculation)
   */
  const refreshAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      businessAnalyticsService.clearAnalyticsCache();
      const analyticsData = businessAnalyticsService.getBusinessAnalytics(products, reviews);
      setAnalytics(analyticsData);
      
      logger.userAction('analytics_refreshed', {
        timestamp: Date.now(),
        productsCount: products.length,
        reviewsCount: reviews.length
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh analytics';
      setError(errorMessage);
      logger.error('Failed to refresh business analytics', err);
    } finally {
      setLoading(false);
    }
  }, [products, reviews]);

  /**
   * Clear analytics cache
   */
  const clearCache = useCallback(() => {
    try {
      businessAnalyticsService.clearAnalyticsCache();
      setAnalytics(null);
      logger.userAction('analytics_cache_cleared');
    } catch (err) {
      logger.error('Failed to clear analytics cache', err);
    }
  }, []);

  /**
   * Export analytics to CSV
   */
  const exportToCSV = useCallback((): string | null => {
    if (!analytics) return null;
    
    try {
      const csvData = businessAnalyticsService.exportAnalyticsToCSV(analytics);
      logger.userAction('analytics_exported', {
        timestamp: Date.now(),
        dataSize: csvData.length
      });
      return csvData;
    } catch (err) {
      logger.error('Failed to export analytics to CSV', err);
      return null;
    }
  }, [analytics]);

  /**
   * Record a sale for analytics tracking
   */
  const recordSale = useCallback((productId: string, revenue: number, quantity: number) => {
    try {
      businessAnalyticsService.recordSale(productId, revenue, quantity);
      logger.userAction('sale_recorded_via_hook', {
        productId,
        revenue,
        quantity
      });
    } catch (err) {
      logger.error('Failed to record sale', err);
    }
  }, []);

  /**
   * Track user session data
   */
  const trackUserSession = useCallback((sessionId: string, data: any) => {
    try {
      businessAnalyticsService.trackUserSession(sessionId, data);
      logger.userAction('user_session_tracked', {
        sessionId,
        dataKeys: Object.keys(data)
      });
    } catch (err) {
      logger.error('Failed to track user session', err);
    }
  }, []);

  /**
   * Record conversion event
   */
  const recordConversionEvent = useCallback((stage: string, sessionId: string, data?: any) => {
    try {
      businessAnalyticsService.recordConversionEvent(stage, sessionId, data);
      logger.userAction('conversion_event_recorded', {
        stage,
        sessionId,
        hasData: !!data
      });
    } catch (err) {
      logger.error('Failed to record conversion event', err);
    }
  }, []);

  // Load analytics on mount and when dependencies change
  useEffect(() => {
    if (products.length > 0) {
      loadAnalytics();
    }
  }, [loadAnalytics, products.length]);

  return {
    analytics,
    loading,
    error,
    refreshAnalytics,
    clearCache,
    exportToCSV,
    recordSale,
    trackUserSession,
    recordConversionEvent
  };
};

/**
 * Hook for specific analytics metrics
 */
export const useSalesMetrics = (): {
  salesMetrics: SalesMetrics | null;
  loading: boolean;
  error: string | null;
} => {
  const { analytics, loading, error } = useBusinessAnalytics();
  
  return {
    salesMetrics: analytics?.salesMetrics || null,
    loading,
    error
  };
};

/**
 * Hook for user behavior metrics
 */
export const useUserBehaviorMetrics = (): {
  userBehaviorMetrics: UserBehaviorMetrics | null;
  loading: boolean;
  error: string | null;
} => {
  const { analytics, loading, error } = useBusinessAnalytics();
  
  return {
    userBehaviorMetrics: analytics?.userBehaviorMetrics || null,
    loading,
    error
  };
};

/**
 * Hook for conversion funnel metrics
 */
export const useConversionFunnelMetrics = (): {
  conversionFunnelMetrics: ConversionFunnelMetrics | null;
  loading: boolean;
  error: string | null;
} => {
  const { analytics, loading, error } = useBusinessAnalytics();
  
  return {
    conversionFunnelMetrics: analytics?.conversionFunnelMetrics || null,
    loading,
    error
  };
};

/**
 * Hook for KPI metrics
 */
export const useKPIMetrics = (): {
  kpiMetrics: KPIMetrics | null;
  loading: boolean;
  error: string | null;
} => {
  const { analytics, loading, error } = useBusinessAnalytics();
  
  return {
    kpiMetrics: analytics?.kpis || null,
    loading,
    error
  };
};

/**
 * Hook for real-time analytics tracking
 */
export const useAnalyticsTracking = () => {
  const { recordSale, trackUserSession, recordConversionEvent } = useBusinessAnalytics();
  
  return {
    recordSale,
    trackUserSession,
    recordConversionEvent
  };
};

export default useBusinessAnalytics;
