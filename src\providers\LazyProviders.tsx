import React from 'react';
import { BundleProvider } from "@/contexts/BundleContext";
import { EmailProvider } from "@/contexts/EmailContext";
import { InventoryProvider } from "@/contexts/InventoryContext";
import { OrderProvider } from "@/contexts/OrderContext";
import { ReviewProvider } from "@/contexts/ReviewContext";
import { WishlistProvider } from "@/contexts/WishlistContext";
import { SearchProvider } from "@/contexts/SearchContext";
import { RecommendationProvider } from "@/contexts/RecommendationContext";

interface LazyProvidersProps {
  children: React.ReactNode;
}

/**
 * LazyProviders component that wraps non-critical context providers
 * These providers are lazy loaded to improve initial page load performance
 */
const LazyProviders: React.FC<LazyProvidersProps> = ({ children }) => {
  return (
    <BundleProvider>
      <EmailProvider>
        <InventoryProvider>
          <OrderProvider>
            <ReviewProvider>
              <WishlistProvider>
                <SearchProvider>
                  <RecommendationProvider>
                    {children}
                  </RecommendationProvider>
                </SearchProvider>
              </WishlistProvider>
            </ReviewProvider>
          </OrderProvider>
        </InventoryProvider>
      </EmailProvider>
    </BundleProvider>
  );
};

export default LazyProviders;
