import { Product, products } from './products';

export interface Bundle {
  id: string;
  name: string;
  description: string;
  productIds: string[];
  products: Product[];
  originalPrice: number;
  discountedPrice: number;
  image?: string; // Optional custom image for the bundle
  discountPercentage: number;
}

// Helper function to create bundles
const createBundle = (
  id: string,
  name: string,
  description: string,
  productIds: string[],
  discountPercentage: number,
  image?: string
): Bundle => {
  // Find the products in the bundle
  const bundleProducts = products.filter(product => productIds.includes(product.id));

  // Calculate original price (sum of all product prices)
  const originalPrice = bundleProducts.reduce((sum, product) => sum + product.price, 0);

  // Calculate discounted price
  const discountedPrice = Number((originalPrice * (1 - discountPercentage / 100)).toFixed(2));

  return {
    id,
    name,
    description,
    productIds,
    products: bundleProducts,
    originalPrice,
    discountedPrice,
    discountPercentage,
    image
  };
};

// Define bundles
export const bundles: Bundle[] = [
  createBundle(
    'morning-glow',
    'Morning Glow Set',
    'Start your day radiant with this refreshing ritual.',
    ['lemon-verbena', 'rose-clay', 'shine-silk'],
    15, // 15% discount
    '/lovable-uploads/Morning glow set.png' // Ensure this matches the actual filename
  ),
  createBundle(
    'scalp-healing',
    'Scalp Healing Trio',
    'Complete care for sensitive and troubled scalps.',
    ['scalp-rescue', 'sensitive-scalp', 'hair-growth'],
    12, // 12% discount
    '/lovable-uploads/Scalp Healing Trio.png' // Ensure this matches the actual filename
  ),
  createBundle(
    'ritual-senses',
    'Ritual of the Senses',
    'Immerse yourself in aromatic luxury for mind and body.',
    ['royal-oud', 'white-misk', 'herbal-hammam'],
    18, // 18% discount
    '/lovable-uploads/Ritual of the Senses.png' // Ensure this matches the actual filename
  )
];
