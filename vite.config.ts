import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { visualizer } from "rollup-plugin-visualizer";

// For debugging
console.log('Loading Vite config...');

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    mcpServers: {
      browsermcp: {
        command: "npx",
        args: ["@browsermcp/mcp@latest"]
      }
    },
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }
  },
  publicDir: 'public', // Explicitly define public directory
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
    visualizer({
      filename: 'stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Add explicit React alias for troubleshooting
      'react': path.resolve(__dirname, 'node_modules/react'),
      'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
    },
  },
  // Add optimizeDeps to ensure React is pre-bundled
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@auth0/auth0-react',
      '@tanstack/react-query',
      'lucide-react',
      'recharts',
      'zod',
      'react-hook-form',
      '@hookform/resolvers',
      'sonner'
    ],
    force: true,
  },
  // CSS optimization
  css: {
    modules: {
      localsConvention: 'camelCase',
    },
    devSourcemap: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
      },
    },
    copyPublicDir: true, // Explicitly copy public directory contents to outDir
    // Image optimization settings
    assetsInlineLimit: 4096, // Inline assets smaller than 4kb
    assetsDir: 'assets', // Directory for static assets
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            // React ecosystem
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react-vendor';
            }

            // Auth0
            if (id.includes('@auth0')) {
              return 'auth';
            }

            // Data fetching
            if (id.includes('@tanstack/react-query')) {
              return 'data-fetching';
            }

            // UI libraries
            if (id.includes('lucide-react')) {
              return 'icons';
            }

            if (id.includes('recharts')) {
              return 'charts';
            }

            // Form libraries
            if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('zod')) {
              return 'forms';
            }

            // Radix UI components
            if (id.includes('@radix-ui')) {
              return 'radix-ui';
            }

            // Other large libraries
            if (id.includes('framer-motion')) {
              return 'animations';
            }

            // Default vendor chunk for other node_modules
            return 'vendor';
          }

          // App chunks
          if (id.includes('/src/')) {
            // Admin pages
            if (id.includes('/pages/admin/') || id.includes('/components/admin/')) {
              return 'admin';
            }

            // Auth components
            if (id.includes('/components/auth/') || id.includes('/contexts/AuthContext')) {
              return 'auth-components';
            }

            // Product-related components
            if (id.includes('/components/product/') || id.includes('/contexts/ProductContext')) {
              return 'product-components';
            }

            // Cart and checkout
            if (id.includes('/components/checkout/') || id.includes('/contexts/CartContext')) {
              return 'cart-components';
            }

            // UI components
            if (id.includes('/components/ui/')) {
              return 'ui-components';
            }

            // Contexts (except already handled ones)
            if (id.includes('/contexts/')) {
              return 'contexts';
            }

            // Utils and services
            if (id.includes('/utils/') || id.includes('/services/')) {
              return 'utils';
            }
          }

          // Default chunk
          return 'main';
        },
      },
    },
  },
}));
