import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface UseTokenManagementResult {
  tokenRefreshAttempts: number;
  lastTokenRefresh: number;
  refreshAuth: (getAccessTokenSilently: () => Promise<string>, logoutCallback: () => void) => Promise<void>;
  resetTokenState: () => void;
  getAccessToken: (
    isAuthenticated: boolean, 
    getAccessTokenSilently: () => Promise<string>,
    isDevelopmentMode: boolean,
    logoutCallback: () => void
  ) => Promise<string | null>;
}

const MAX_TOKEN_REFRESH_ATTEMPTS = 3;
const TOKEN_REFRESH_COOLDOWN = 5 * 60 * 1000; // 5 minutes

export const useTokenManagement = (): UseTokenManagementResult => {
  const [tokenRefreshAttempts, setTokenRefreshAttempts] = useState(0);
  const [lastTokenRefresh, setLastTokenRefresh] = useState<number>(0);

  const resetTokenState = useCallback(() => {
    setTokenRefreshAttempts(0);
    setLastTokenRefresh(0);
  }, []);

    const refreshAuth = useCallback(async (    getAccessTokenSilently: () => Promise<string>,    logoutCallback: () => void  ): Promise<void> => {    try {      await getAccessTokenSilently();      setTokenRefreshAttempts(0);      setLastTokenRefresh(Date.now());      toast.success('Authentication refreshed successfully');    } catch (error) {      console.error('Failed to refresh authentication:', error);            setTokenRefreshAttempts(prev => {        const newAttempts = prev + 1;        if (newAttempts >= MAX_TOKEN_REFRESH_ATTEMPTS) {          toast.error('Authentication refresh failed. Please log in again.');          logoutCallback();        } else {          toast.error('Failed to refresh authentication. Retrying...');        }        return newAttempts;      });            throw error;    }  }, []);

  const getAccessToken = useCallback(async (
    isAuthenticated: boolean,
    getAccessTokenSilently: () => Promise<string>,
    isDevelopmentMode: boolean,
    logoutCallback: () => void
  ): Promise<string | null> => {
    if (!isAuthenticated) return null;

    if (isDevelopmentMode) {
      return 'dev-token-123456';
    }

    try {
      // Check if we should attempt token refresh
      const now = Date.now();
      const _shouldRefresh = tokenRefreshAttempts < MAX_TOKEN_REFRESH_ATTEMPTS && 
                           (now - lastTokenRefresh) > TOKEN_REFRESH_COOLDOWN;

      const accessToken = await getAccessTokenSilently();
      
      // Reset refresh attempts on successful token retrieval
      if (tokenRefreshAttempts > 0) {
        setTokenRefreshAttempts(0);
      }
      
      return accessToken;
    } catch (error) {
      console.error('Error getting access token:', error);
      
      // Handle specific token errors
      if ((error as Error).message?.includes('Login required') || 
          (error as Error).message?.includes('login_required')) {
        toast.error('Session expired. Please log in again.');
        logoutCallback();
      } else {
        toast.error('Failed to get access token. Please try refreshing the page.');
      }
      
      return null;
    }
  }, [tokenRefreshAttempts, lastTokenRefresh]);

  return {
    tokenRefreshAttempts,
    lastTokenRefresh,
    refreshAuth,
    resetTokenState,
    getAccessToken
  };
};

export default useTokenManagement;
