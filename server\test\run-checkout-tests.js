/**
 * Sabone.store Checkout Test Runner
 * 
 * This script runs the checkout flow tests using Puppeteer to automate browser interactions.
 * It tests both Stripe and PayPal payment methods.
 */

import puppeteer from 'puppeteer';
import chalk from 'chalk';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name using ES modules approach
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const config = {
  baseUrl: 'http://localhost:3000',
  testServerPort: 3001,
  headless: false, // Set to true for CI environments
  slowMo: 50, // Slow down operations by 50ms for visibility
  timeout: 30000, // 30 seconds timeout
  viewportWidth: 1280,
  viewportHeight: 800,
  testCards: {
    valid: '4242 4242 4242 4242',
    requires3DS: '4000 0000 0000 3220',
    declined: '4000 0000 0000 0002'
  },
  testUser: {
    email: '<EMAIL>',
    password: 'Test123!',
    name: 'Test User',
    phone: '************'
  },
  testAddress: {
    fullName: 'Test User',
    addressLine1: '123 Test St',
    addressLine2: 'Apt 4B',
    city: 'Testville',
    state: 'TS',
    zipCode: '12345',
    country: 'United States',
    phone: '************'
  }
};

// Test server process
let testServer;

/**
 * Start the test server
 */
function startTestServer() {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue('🚀 Starting checkout test server...'));
    
    const serverPath = path.join(__dirname, 'checkout-test-server.js');
    testServer = spawn('node', [serverPath], {
      env: {
        ...process.env,
        TEST_PORT: config.testServerPort.toString()
      }
    });
    
    testServer.stdout.on('data', (data) => {
      console.log(chalk.gray(`[Test Server] ${data.toString().trim()}`));
      
      // Resolve when the server is ready
      if (data.toString().includes('Checkout test server running on port')) {
        resolve();
      }
    });
    
    testServer.stderr.on('data', (data) => {
      console.error(chalk.red(`[Test Server Error] ${data.toString().trim()}`));
    });
    
    testServer.on('error', (error) => {
      console.error(chalk.red(`Failed to start test server: ${error.message}`));
      reject(error);
    });
    
    // Resolve after a timeout in case we miss the ready message
    setTimeout(resolve, 3000);
  });
}

/**
 * Stop the test server
 */
function stopTestServer() {
  return new Promise((resolve) => {
    if (testServer) {
      console.log(chalk.blue('🛑 Stopping checkout test server...'));
      testServer.kill();
      testServer = null;
    }
    resolve();
  });
}

/**
 * Run the checkout flow tests
 */
async function runCheckoutTests() {
  let browser;
  
  try {
    // Start the test server
    await startTestServer();
    
    console.log(chalk.blue('🧪 Starting checkout flow tests...'));
    
    // Launch the browser
    browser = await puppeteer.launch({
      headless: config.headless,
      slowMo: config.slowMo,
      defaultViewport: {
        width: config.viewportWidth,
        height: config.viewportHeight
      },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    // Create a new page
    const page = await browser.newPage();
    
    // Set default timeout
    page.setDefaultTimeout(config.timeout);
    
    // Log console messages from the page
    page.on('console', (msg) => {
      const type = msg.type();
      const text = msg.text();
      
      if (type === 'error') {
        console.error(chalk.red(`[Browser Console Error] ${text}`));
      } else if (type === 'warning') {
        console.warn(chalk.yellow(`[Browser Console Warning] ${text}`));
      } else if (text.includes('🧪')) {
        console.log(chalk.cyan(`[Browser Console] ${text}`));
      }
    });
    
    // Run the Stripe checkout test
    await testStripeCheckout(page);
    
    // Run the PayPal checkout test
    await testPayPalCheckout(page);
    
    console.log(chalk.green('✅ All checkout flow tests completed successfully!'));
  } catch (error) {
    console.error(chalk.red(`❌ Checkout flow tests failed: ${error.message}`));
    console.error(error);
  } finally {
    // Close the browser
    if (browser) {
      await browser.close();
    }
    
    // Stop the test server
    await stopTestServer();
  }
}

/**
 * Test the Stripe checkout flow
 */
async function testStripeCheckout(page) {
  console.log(chalk.blue('🧪 Testing Stripe checkout flow...'));
  
  try {
    // Navigate to the home page
    await page.goto(config.baseUrl);
    console.log(chalk.green('✅ Navigated to home page'));
    
    // Add products to cart
    await addProductsToCart(page);
    console.log(chalk.green('✅ Added products to cart'));
    
    // Navigate to checkout
    await page.goto(`${config.baseUrl}/checkout`);
    console.log(chalk.green('✅ Navigated to checkout page'));
    
    // Wait for the checkout page to load
    await page.waitForSelector('form');
    
    // Fill customer information form
    await fillCustomerForm(page);
    console.log(chalk.green('✅ Filled customer information form'));
    
    // Select Stripe payment method
    await selectStripePayment(page);
    console.log(chalk.green('✅ Selected Stripe payment method'));
    
    // Wait for Stripe elements to load
    await page.waitForSelector('iframe[title="Secure card payment input frame"]');
    
    // Fill Stripe card details
    await fillStripeCardDetails(page);
    console.log(chalk.green('✅ Filled Stripe card details'));
    
    // Submit the payment
    await submitPayment(page);
    console.log(chalk.green('✅ Submitted payment'));
    
    // Wait for order confirmation
    await page.waitForFunction(
      () => window.location.pathname.includes('/order-confirmation'),
      { timeout: config.timeout }
    );
    
    console.log(chalk.green('✅ Order confirmed successfully'));
    
    return true;
  } catch (error) {
    console.error(chalk.red(`❌ Stripe checkout test failed: ${error.message}`));
    throw error;
  }
}

/**
 * Test the PayPal checkout flow
 */
async function testPayPalCheckout(page) {
  console.log(chalk.blue('🧪 Testing PayPal checkout flow...'));
  
  try {
    // Navigate to the home page
    await page.goto(config.baseUrl);
    console.log(chalk.green('✅ Navigated to home page'));
    
    // Add products to cart
    await addProductsToCart(page);
    console.log(chalk.green('✅ Added products to cart'));
    
    // Navigate to checkout
    await page.goto(`${config.baseUrl}/checkout`);
    console.log(chalk.green('✅ Navigated to checkout page'));
    
    // Wait for the checkout page to load
    await page.waitForSelector('form');
    
    // Fill customer information form
    await fillCustomerForm(page);
    console.log(chalk.green('✅ Filled customer information form'));
    
    // Select PayPal payment method
    await selectPayPalPayment(page);
    console.log(chalk.green('✅ Selected PayPal payment method'));
    
    // Wait for PayPal button to load
    await page.waitForSelector('.paypal-button');
    
    // Click the PayPal button
    await page.click('.paypal-button');
    console.log(chalk.green('✅ Clicked PayPal button'));
    
    // Note: In a real test, we would need to handle the PayPal popup window
    // This is challenging with Puppeteer and would require more complex code
    // For now, we'll just simulate a successful PayPal payment
    
    console.log(chalk.yellow('⚠️ PayPal popup handling is not implemented in this test'));
    console.log(chalk.yellow('⚠️ In a real test, we would need to handle the PayPal authentication flow'));
    
    // Simulate a successful PayPal payment by directly calling the success callback
    await page.evaluate(() => {
      // This assumes the PayPal component exposes a success callback
      if (window.__PAYPAL_SUCCESS_CALLBACK__) {
        window.__PAYPAL_SUCCESS_CALLBACK__({
          orderID: 'SIMULATED-PAYPAL-ORDER',
          payerID: 'SIMULATED-PAYER-ID'
        });
      }
    });
    
    // Wait for order confirmation
    await page.waitForFunction(
      () => window.location.pathname.includes('/order-confirmation'),
      { timeout: config.timeout }
    );
    
    console.log(chalk.green('✅ Order confirmed successfully'));
    
    return true;
  } catch (error) {
    console.error(chalk.red(`❌ PayPal checkout test failed: ${error.message}`));
    throw error;
  }
}

// Helper functions for the tests
async function addProductsToCart(page) {
  // Navigate to the products page
  await page.goto(`${config.baseUrl}/#products`);
  
  // Wait for product cards to load
  await page.waitForSelector('.product-card');
  
  // Get all product cards
  const productCards = await page.$$('.product-card');
  
  // Add 2-3 products to the cart
  for (let i = 0; i < Math.min(3, productCards.length); i++) {
    // Click the "Add to Cart" button
    await productCards[i].click();
    
    // Wait for the add to cart animation
    await page.waitForTimeout(500);
  }
}

async function fillCustomerForm(page) {
  // Fill the form with test data
  await page.type('input[name="fullName"]', config.testAddress.fullName);
  await page.type('input[name="email"]', config.testUser.email);
  await page.type('input[name="phone"]', config.testAddress.phone);
  await page.type('input[name="addressLine1"]', config.testAddress.addressLine1);
  await page.type('input[name="addressLine2"]', config.testAddress.addressLine2);
  await page.type('input[name="city"]', config.testAddress.city);
  await page.type('input[name="state"]', config.testAddress.state);
  await page.type('input[name="zipCode"]', config.testAddress.zipCode);
  await page.type('input[name="country"]', config.testAddress.country);
}

async function selectStripePayment(page) {
  // Click the credit card payment option
  await page.click('input[value="credit_card"]');
}

async function selectPayPalPayment(page) {
  // Click the PayPal payment option
  await page.click('input[value="paypal"]');
}

async function fillStripeCardDetails(page) {
  // This is challenging because Stripe elements are in an iframe
  // For a real test, we would need to handle the iframe interaction
  // For now, we'll just simulate filling the card details
  
  console.log(chalk.yellow('⚠️ Stripe iframe handling is not fully implemented in this test'));
  console.log(chalk.yellow('⚠️ In a real test, we would need to interact with the Stripe iframe'));
  
  // Simulate filling the card details by injecting test card data
  await page.evaluate((testCard) => {
    // This assumes we have a way to access the Stripe elements
    if (window.__STRIPE_TEST_CARD__) {
      window.__STRIPE_TEST_CARD__ = testCard;
    }
  }, config.testCards.valid);
}

async function submitPayment(page) {
  // Click the submit button
  await page.click('button[type="submit"]');
}

// Run the tests
runCheckoutTests().catch(console.error);
