<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sabone Test Page</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #1c1c1c;
        color: #e5dcc5;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 800px;
        margin: 50px auto;
        padding: 20px;
        background-color: #2a2a1f;
        border-radius: 8px;
        border: 1px solid #c6a870;
      }
      h1, h2 {
        color: #c6a870;
      }
      .section {
        margin-top: 20px;
        padding: 15px;
        background-color: #333328;
        border-radius: 4px;
      }
      button {
        background-color: #c6a870;
        color: #1c1c1c;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    </style>
  </head>
  <body>
    <!-- This is the root element that <PERSON><PERSON> will render into -->
    <div id="root">
      <div class="container">
        <h1>Sabone Test Page</h1>
        <p>This is a static HTML page to test if your browser can render basic HTML correctly.</p>
        
        <div class="section">
          <h2>Root Element Test</h2>
          <p>This content is inside the <code>div id="root"</code> element, which is where React would normally render.</p>
          <p>If you can see this content, your browser is rendering HTML correctly.</p>
        </div>
        
        <div class="section">
          <h2>JavaScript Test</h2>
          <p>Click the button below to test if JavaScript is working:</p>
          <button id="test-button">Test JavaScript</button>
          <div id="js-result"></div>
        </div>
        
        <div class="section">
          <h2>Troubleshooting Steps</h2>
          <ol>
            <li>Check browser console for errors</li>
            <li>Verify that all required JavaScript files are loading</li>
            <li>Check if there are any network errors</li>
            <li>Try clearing browser cache and cookies</li>
            <li>Test in a different browser</li>
          </ol>
        </div>
      </div>
    </div>
    
    <script>
      // Simple JavaScript test
      document.getElementById('test-button').addEventListener('click', function() {
        const resultElement = document.getElementById('js-result');
        resultElement.innerHTML = '<p style="color: #6bff6b;">JavaScript is working correctly!</p>';
        
        // Test localStorage
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          resultElement.innerHTML += '<p style="color: #6bff6b;">localStorage is working correctly!</p>';
        } catch (e) {
          resultElement.innerHTML += `<p style="color: #ff6b6b;">localStorage error: ${e.message}</p>`;
        }
        
        // Display browser info
        const browserInfo = `
          <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
          <p><strong>Platform:</strong> ${navigator.platform}</p>
          <p><strong>Screen Size:</strong> ${window.innerWidth}x${window.innerHeight}</p>
        `;
        resultElement.innerHTML += browserInfo;
      });
      
      // Log to console
      console.log('Test page loaded successfully');
    </script>
  </body>
</html>
