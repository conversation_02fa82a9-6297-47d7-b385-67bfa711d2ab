import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { businessAnalyticsService } from '@/services/businessAnalyticsService';
import { Product } from '@/data/products';
import { Review } from '@/types/review';

// Mock data for testing
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Test Product 1',
    price: 29.99,
    originalPrice: 39.99,
    image: '/test-image-1.jpg',
    category: 'Skincare',
    rating: 4.5,
    reviews: 25,
    description: 'Test product description',
    ingredients: ['Test ingredient'],
    benefits: ['Test benefit'],
    howToUse: 'Test usage instructions',
    inStock: true,
    stockQuantity: 100,
    featured: true,
    new: false,
    sale: true
  },
  {
    id: '2',
    name: 'Test Product 2',
    price: 49.99,
    originalPrice: 59.99,
    image: '/test-image-2.jpg',
    category: 'Haircare',
    rating: 4.2,
    reviews: 18,
    description: 'Test product description 2',
    ingredients: ['Test ingredient 2'],
    benefits: ['Test benefit 2'],
    howToUse: 'Test usage instructions 2',
    inStock: true,
    stockQuantity: 50,
    featured: false,
    new: true,
    sale: false
  }
];

const mockReviews: Review[] = [
  {
    id: '1',
    productId: '1',
    userId: 'user1',
    userName: 'Test User 1',
    rating: 5,
    title: 'Great product!',
    comment: 'This product is amazing and works perfectly.',
    date: new Date().toISOString(),
    verified: true,
    helpful: 10,
    notHelpful: 1,
    photos: []
  },
  {
    id: '2',
    productId: '1',
    userId: 'user2',
    userName: 'Test User 2',
    rating: 4,
    title: 'Good quality',
    comment: 'Good product, would recommend.',
    date: new Date().toISOString(),
    verified: true,
    helpful: 5,
    notHelpful: 0,
    photos: []
  }
];

describe('BusinessAnalyticsService', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  afterEach(() => {
    // Clean up after each test
    localStorage.clear();
  });

  describe('Sales Analytics', () => {
    it('should calculate sales metrics correctly', () => {
      const salesMetrics = businessAnalyticsService.calculateSalesMetrics(mockProducts);
      
      expect(salesMetrics).toBeDefined();
      expect(salesMetrics.totalRevenue).toBeGreaterThanOrEqual(0);
      expect(salesMetrics.totalOrders).toBeGreaterThanOrEqual(0);
      expect(salesMetrics.averageOrderValue).toBeGreaterThanOrEqual(0);
      expect(salesMetrics.topSellingProducts).toHaveLength(2);
      expect(salesMetrics.salesByCategory).toHaveLength(4);
      expect(salesMetrics.salesByTimeframe).toBeDefined();
      expect(salesMetrics.lastUpdated).toBeDefined();
    });

    it('should record sales correctly', () => {
      const productId = 'test-product';
      const revenue = 100;
      const quantity = 2;

      businessAnalyticsService.recordSale(productId, revenue, quantity);

      // Check if sale was recorded in localStorage
      const salesData = localStorage.getItem('sabone-sales-data');
      expect(salesData).toBeDefined();
      
      if (salesData) {
        const parsed = JSON.parse(salesData);
        expect(parsed).toBeInstanceOf(Array);
      }
    });

    it('should handle empty products array', () => {
      const salesMetrics = businessAnalyticsService.calculateSalesMetrics([]);
      
      expect(salesMetrics).toBeDefined();
      expect(salesMetrics.totalRevenue).toBeGreaterThanOrEqual(0);
      expect(salesMetrics.topSellingProducts).toHaveLength(0);
    });
  });

  describe('User Behavior Analytics', () => {
    it('should calculate user behavior metrics correctly', () => {
      const userBehaviorMetrics = businessAnalyticsService.calculateUserBehaviorMetrics();
      
      expect(userBehaviorMetrics).toBeDefined();
      expect(userBehaviorMetrics.totalSessions).toBeGreaterThanOrEqual(0);
      expect(userBehaviorMetrics.averageSessionDuration).toBeGreaterThanOrEqual(0);
      expect(userBehaviorMetrics.bounceRate).toBeGreaterThanOrEqual(0);
      expect(userBehaviorMetrics.bounceRate).toBeLessThanOrEqual(100);
      expect(userBehaviorMetrics.pageViewsPerSession).toBeGreaterThanOrEqual(0);
      expect(userBehaviorMetrics.topPages).toBeDefined();
      expect(userBehaviorMetrics.userFlow).toBeDefined();
      expect(userBehaviorMetrics.deviceBreakdown).toBeDefined();
      expect(userBehaviorMetrics.trafficSources).toBeDefined();
      expect(userBehaviorMetrics.lastUpdated).toBeDefined();
    });

    it('should track user sessions correctly', () => {
      const sessionId = 'test-session-123';
      const sessionData = {
        startTime: Date.now(),
        pageViews: 5,
        deviceType: 'desktop',
        source: 'organic'
      };

      businessAnalyticsService.trackUserSession(sessionId, sessionData);

      // Check if session was recorded in localStorage
      const behaviorData = localStorage.getItem('sabone-user-behavior-analytics');
      expect(behaviorData).toBeDefined();
      
      if (behaviorData) {
        const parsed = JSON.parse(behaviorData);
        expect(parsed).toBeInstanceOf(Array);
      }
    });
  });

  describe('Conversion Funnel Analytics', () => {
    it('should calculate conversion funnel metrics correctly', () => {
      const conversionMetrics = businessAnalyticsService.calculateConversionFunnelMetrics();
      
      expect(conversionMetrics).toBeDefined();
      expect(conversionMetrics.stages).toHaveLength(5);
      expect(conversionMetrics.overallConversionRate).toBeGreaterThanOrEqual(0);
      expect(conversionMetrics.overallConversionRate).toBeLessThanOrEqual(100);
      expect(conversionMetrics.dropOffPoints).toBeDefined();
      expect(conversionMetrics.averageTimeToConvert).toBeGreaterThan(0);
      expect(conversionMetrics.lastUpdated).toBeDefined();

      // Check funnel stage structure
      conversionMetrics.stages.forEach(stage => {
        expect(stage.stage).toBeDefined();
        expect(stage.users).toBeGreaterThanOrEqual(0);
        expect(stage.conversionRate).toBeGreaterThanOrEqual(0);
        expect(stage.conversionRate).toBeLessThanOrEqual(100);
        expect(stage.dropOffRate).toBeGreaterThanOrEqual(0);
        expect(stage.dropOffRate).toBeLessThanOrEqual(100);
      });
    });

    it('should record conversion events correctly', () => {
      const stage = 'Add to Cart';
      const sessionId = 'test-session-456';
      const eventData = { productId: 'test-product', timestamp: Date.now() };

      businessAnalyticsService.recordConversionEvent(stage, sessionId, eventData);

      // Check if conversion event was recorded in localStorage
      const conversionData = localStorage.getItem('sabone-conversion-funnel');
      expect(conversionData).toBeDefined();
      
      if (conversionData) {
        const parsed = JSON.parse(conversionData);
        expect(parsed).toBeInstanceOf(Array);
        expect(parsed.length).toBeGreaterThan(0);
        
        const lastEvent = parsed[parsed.length - 1];
        expect(lastEvent.stage).toBe(stage);
        expect(lastEvent.sessionId).toBe(sessionId);
        expect(lastEvent.data).toEqual(eventData);
      }
    });
  });

  describe('KPI Metrics', () => {
    it('should calculate KPI metrics correctly', () => {
      const kpiMetrics = businessAnalyticsService.calculateKPIMetrics();
      
      expect(kpiMetrics).toBeDefined();
      expect(kpiMetrics.customerLifetimeValue).toBeGreaterThan(0);
      expect(kpiMetrics.customerAcquisitionCost).toBeGreaterThan(0);
      expect(kpiMetrics.returnCustomerRate).toBeGreaterThanOrEqual(0);
      expect(kpiMetrics.returnCustomerRate).toBeLessThanOrEqual(100);
      expect(kpiMetrics.averageOrderFrequency).toBeGreaterThan(0);
      expect(kpiMetrics.inventoryTurnover).toBeGreaterThan(0);
      expect(kpiMetrics.grossMargin).toBeGreaterThanOrEqual(0);
      expect(kpiMetrics.grossMargin).toBeLessThanOrEqual(100);
      expect(kpiMetrics.netPromoterScore).toBeGreaterThanOrEqual(-100);
      expect(kpiMetrics.netPromoterScore).toBeLessThanOrEqual(100);
      expect(kpiMetrics.customerSatisfactionScore).toBeGreaterThanOrEqual(1);
      expect(kpiMetrics.customerSatisfactionScore).toBeLessThanOrEqual(5);
    });
  });

  describe('Comprehensive Business Analytics', () => {
    it('should get comprehensive business analytics', () => {
      const analytics = businessAnalyticsService.getBusinessAnalytics(mockProducts, mockReviews);
      
      expect(analytics).toBeDefined();
      expect(analytics.salesMetrics).toBeDefined();
      expect(analytics.userBehaviorMetrics).toBeDefined();
      expect(analytics.conversionFunnelMetrics).toBeDefined();
      expect(analytics.inventoryMetrics).toBeDefined();
      expect(analytics.reviewMetrics).toBeDefined();
      expect(analytics.kpis).toBeDefined();
      expect(analytics.lastUpdated).toBeDefined();
    });

    it('should cache analytics data', () => {
      const analytics1 = businessAnalyticsService.getBusinessAnalytics(mockProducts, mockReviews);
      const analytics2 = businessAnalyticsService.getCachedBusinessAnalytics(mockProducts, mockReviews);
      
      expect(analytics1).toBeDefined();
      expect(analytics2).toBeDefined();
      expect(analytics1.lastUpdated).toBe(analytics2?.lastUpdated);
    });

    it('should clear analytics cache', () => {
      // First, create some analytics data
      businessAnalyticsService.getBusinessAnalytics(mockProducts, mockReviews);
      
      // Verify data exists
      const cachedData = localStorage.getItem('sabone-business-analytics');
      expect(cachedData).toBeDefined();
      
      // Clear cache
      businessAnalyticsService.clearAnalyticsCache();
      
      // Verify cache is cleared
      const clearedData = localStorage.getItem('sabone-business-analytics');
      expect(clearedData).toBeNull();
    });

    it('should export analytics to CSV', () => {
      const analytics = businessAnalyticsService.getBusinessAnalytics(mockProducts, mockReviews);
      const csvData = businessAnalyticsService.exportAnalyticsToCSV(analytics);
      
      expect(csvData).toBeDefined();
      expect(csvData.length).toBeGreaterThan(0);
      expect(csvData).toContain('Metric,Value,Category');
      expect(csvData).toContain('Total Revenue');
      expect(csvData).toContain('Total Sessions');
      expect(csvData).toContain('Conversion Rate');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid data gracefully', () => {
      expect(() => {
        businessAnalyticsService.recordSale('', -100, -5);
      }).not.toThrow();

      expect(() => {
        businessAnalyticsService.trackUserSession('', null);
      }).not.toThrow();

      expect(() => {
        businessAnalyticsService.recordConversionEvent('', '', undefined);
      }).not.toThrow();
    });

    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw errors
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = () => {
        throw new Error('Storage quota exceeded');
      };

      expect(() => {
        businessAnalyticsService.recordSale('test', 100, 1);
      }).not.toThrow();

      // Restore original localStorage
      localStorage.setItem = originalSetItem;
    });
  });
});
