import { Helmet } from 'react-helmet-async';

interface BreadcrumbItem {
  name: string;
  url: string;
  position: number;
}

interface BreadcrumbSchemaProps {
  items: BreadcrumbItem[];
}

/**
 * Component to add breadcrumb structured data
 * 
 * @param items Array of breadcrumb items with name, url, and position
 * @returns Helmet component with breadcrumb schema
 */
const BreadcrumbSchema = ({ items }: BreadcrumbSchemaProps) => {
  // Create the breadcrumb schema
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    'itemListElement': items.map(item => ({
      '@type': 'ListItem',
      'position': item.position,
      'name': item.name,
      'item': item.url.startsWith('http') ? item.url : `https://sabone.store${item.url}`
    }))
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </Helmet>
  );
};

export default BreadcrumbSchema;
