import { ReactNode, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import {
  BarChart,
  Package,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  AlertTriangle,
  MessageSquare
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";
import SignInModal from "@/components/auth/SignInModal";

interface AdminLayoutProps {
  children: ReactNode;
  title: string;
}

const AdminLayout = ({ children, title }: AdminLayoutProps) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);

  // Check if user is admin
  const isAdmin = user?.role === 'admin';

  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-6">
        <AlertTriangle className="h-16 w-16 text-yellow-500 mb-4" />
        <h1 className="text-2xl font-playfair font-semibold text-sabone-gold mb-2">
          Access Restricted
        </h1>
        <p className="text-sabone-cream/70 text-center max-w-md mb-6">
          You don't have permission to access the admin dashboard. Please contact an administrator if you believe this is an error.
        </p>
        <Button
          onClick={() => navigate('/')}
          className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
        >
          Return to Home
        </Button>
      </div>
    );
  }

  const navItems = [
    {
      label: "Dashboard",
      icon: <BarChart className="h-5 w-5 mr-3" />,
      path: "/account/admin",
    },
    {
      label: "Inventory",
      icon: <Package className="h-5 w-5 mr-3" />,
      path: "/account/admin/inventory",
    },
    {
      label: "Orders",
      icon: <Package className="h-5 w-5 mr-3" />,
      path: "/account/admin/orders",
    },
    {
      label: "Reviews",
      icon: <MessageSquare className="h-5 w-5 mr-3" />,
      path: "/account/admin/reviews",
    },
    {
      label: "Customers",
      icon: <Users className="h-5 w-5 mr-3" />,
      path: "/account/admin/customers",
    },
    {
      label: "Settings",
      icon: <Settings className="h-5 w-5 mr-3" />,
      path: "/account/admin/settings",
    },
  ];

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  const handleLogout = () => {
    logout(() => {
      navigate("/");
      toast.success("Logged out successfully");
      setTimeout(() => setIsSignInModalOpen(true), 300);
    });
  };

  return (
    <div className="flex h-full min-h-screen bg-sabone-charcoal">
      {/* Mobile sidebar toggle */}
      {isMobile && (
        <button
          className="fixed top-4 left-4 z-50 bg-sabone-dark-olive p-2 rounded-md text-sabone-gold"
          onClick={toggleSidebar}
          aria-label={isSidebarOpen ? "Close sidebar" : "Open sidebar"}
        >
          {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed inset-y-0 left-0 z-40 w-64 bg-sabone-dark-olive/90 backdrop-blur-sm transform transition-transform duration-300 ease-in-out
          ${isMobile ? (isSidebarOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
          flex flex-col border-r border-sabone-gold/20
        `}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Admin Panel</h2>
            {isMobile && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className="text-sabone-cream hover:text-sabone-gold"
              >
                <X size={20} />
              </Button>
            )}
          </div>

          <Separator className="bg-sabone-gold/20 mb-6" />

          <nav className="space-y-1">
            {navItems.map((item) => (
              <button
                key={item.path}
                onClick={() => handleNavigation(item.path)}
                className="flex items-center w-full px-3 py-3 text-sabone-cream hover:bg-sabone-gold/10 hover:text-sabone-gold rounded-md transition-colors"
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="mt-auto p-6">
          <Separator className="bg-sabone-gold/20 mb-6" />

          <div className="flex flex-col space-y-4">
            <button
              onClick={() => navigate('/')}
              className="flex items-center px-3 py-3 text-sabone-cream hover:bg-sabone-gold/10 hover:text-sabone-gold rounded-md transition-colors"
            >
              <Home className="h-5 w-5 mr-3" />
              <span>Back to Store</span>
            </button>

            <button
              onClick={handleLogout}
              className="flex items-center px-3 py-3 text-sabone-cream hover:bg-sabone-gold/10 hover:text-sabone-gold rounded-md transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className={`flex-1 ${!isMobile ? 'ml-64' : ''} transition-all duration-300`}>
        <main className="p-6">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-playfair font-semibold text-sabone-gold">{title}</h1>
            </div>

            <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* Sign-in Modal */}
      <SignInModal
        open={isSignInModalOpen}
        onOpenChange={setIsSignInModalOpen}
      />
    </div>
  );
};

export default AdminLayout;
