import * as React from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: "sm" | "md" | "lg";
  interactive?: boolean;
  onChange?: (rating: number) => void;
  className?: string;
}

const StarRating = React.forwardRef<HTMLDivElement, StarRatingProps>(
  ({ rating, maxRating = 5, size = "md", interactive = false, onChange, className }, ref) => {
    const [hoverRating, setHoverRating] = React.useState(0);
    
    const sizeClasses = {
      sm: "h-3 w-3",
      md: "h-4 w-4",
      lg: "h-5 w-5"
    };
    
    const handleClick = (index: number) => {
      if (interactive && onChange) {
        onChange(index);
      }
    };
    
    return (
      <div 
        ref={ref}
        className={cn("flex items-center", className)}
        onMouseLeave={() => interactive && setHoverRating(0)}
      >
        {Array.from({ length: maxRating }).map((_, index) => {
          const starValue = index + 1;
          const isFilled = interactive 
            ? starValue <= (hoverRating || rating)
            : starValue <= rating;
          
          return (
            <span
              key={index}
              className={cn(
                "inline-block mr-0.5 cursor-default transition-colors",
                interactive && "cursor-pointer"
              )}
              onMouseEnter={() => interactive && setHoverRating(starValue)}
              onClick={() => handleClick(starValue)}
            >
              <Star
                className={cn(
                  sizeClasses[size],
                  isFilled 
                    ? "fill-sabone-gold text-sabone-gold" 
                    : "text-sabone-gold/30"
                )}
              />
            </span>
          );
        })}
      </div>
    );
  }
);

StarRating.displayName = "StarRating";

export { StarRating };
