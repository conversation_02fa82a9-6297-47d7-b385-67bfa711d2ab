/**
 * Image Middleware
 *
 * This middleware intercepts image requests and serves them from localStorage if available.
 * It's used to handle images that are stored as base64 strings in localStorage.
 */

// Storage keys
const PRODUCT_IMAGES_KEY = 'sabone-product-images';
const USER_IMAGES_KEY = 'sabone-user-images';

/**
 * Initialize the image middleware
 * This function sets up a fetch interceptor to handle image requests
 */
export const initImageMiddleware = (): void => {
  // Only run in browser environment
  if (typeof window === 'undefined') {
    return;
  }

  // Store the original fetch function
  const originalFetch = window.fetch;

  // Override the fetch function to intercept image requests
  window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    // Only intercept GET requests
    if (init && init.method && init.method !== 'GET') {
      return originalFetch(input, init);
    }

    // Convert input to URL string
    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;

    // Skip if no URL
    if (!url) {
      return originalFetch(input, init);
    }

    // Check if this is an image request
    const isImageRequest = url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i) ||
                          url.includes('/lovable-uploads/') ||
                          url.startsWith('lovable-uploads/');

    if (isImageRequest) {
      console.log(`Image middleware: Processing image request for ${url}`);

      // Normalize the path
      const normalizedPath = url.startsWith('/') ? url : `/${url}`;
      const pathWithoutSlash = url.startsWith('/') ? url.substring(1) : url;

      // Try to get the image from localStorage
      try {
        // Create an array of possible paths to check
        const possiblePaths = [
          url,                  // Original path
          normalizedPath,       // Path with leading slash
          pathWithoutSlash,     // Path without leading slash
          // Special case for lovable-uploads
          url.includes('lovable-uploads') ? url : `lovable-uploads/${url.split('/').pop()}`,
          normalizedPath.includes('lovable-uploads') ? normalizedPath : `/lovable-uploads/${normalizedPath.split('/').pop()}`,
          // Additional paths for handling spaces in filenames
          decodeURIComponent(url),
          decodeURIComponent(normalizedPath),
          decodeURIComponent(pathWithoutSlash)
        ];

        // Check product images
        const productImagesJson = localStorage.getItem(PRODUCT_IMAGES_KEY);
        if (productImagesJson) {
          const productImages = JSON.parse(productImagesJson);

          // Try each possible path
          for (const path of possiblePaths) {
            if (productImages[path]) {
              // Create a blob from the base64 data
              const base64Data = productImages[path];
              if (base64Data && base64Data.startsWith('data:')) {
                console.log(`Middleware: Serving image from localStorage: ${path}`);
                const response = await createBlobResponse(base64Data);
                if (response) {
                  return response;
                }
              }
            }
          }
        }

        // Check user images
        const userImagesJson = localStorage.getItem(USER_IMAGES_KEY);
        if (userImagesJson) {
          const userImages = JSON.parse(userImagesJson);

          // Try each possible path
          for (const path of possiblePaths) {
            if (userImages[path]) {
              // Create a blob from the base64 data
              const base64Data = userImages[path];
              if (base64Data && base64Data.startsWith('data:')) {
                console.log(`Middleware: Serving image from user images: ${path}`);
                const response = await createBlobResponse(base64Data);
                if (response) {
                  return response;
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error retrieving image from localStorage:', error);
      }
    }

    // If we get here, proceed with the original fetch
    return originalFetch(input, init);
  };

  console.log('Image middleware initialized');
};

/**
 * Create a Response object from a base64 data URL
 *
 * @param base64Data The base64 data URL
 * @returns A Response object or null if creation fails
 */
const createBlobResponse = async (base64Data: string): Promise<Response | null> => {
  try {
    // Extract the MIME type and base64 data
    const matches = base64Data.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
      return null;
    }

    const contentType = matches[1];
    const base64 = matches[2];

    // Convert base64 to binary
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Create a blob and return a Response
    const blob = new Blob([bytes], { type: contentType });
    return new Response(blob, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': blob.size.toString(),
      },
    });
  } catch (error) {
    console.error('Error creating blob response:', error);
    return null;
  }
};
