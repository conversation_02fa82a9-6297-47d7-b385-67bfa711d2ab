// Performance monitoring utility for Core Web Vitals and app performance
console.log('Performance Monitor: Initializing...');

// Simple performance metrics interface
interface PerformanceMetrics {
  route: string;
  timestamp: number;
  LCP?: number;
  FID?: number;
  CLS?: number;
  FCP?: number;
  TTFB?: number;
  renderTime?: number;
  memoryUsage?: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  metric: string;
  value: number;
  threshold: number;
  message: string;
  timestamp: number;
}

// Performance thresholds based on Google's Core Web Vitals recommendations
const THRESHOLDS = {
  LCP: { good: 2500, poor: 4000 },
  FID: { good: 100, poor: 300 },
  CLS: { good: 0.1, poor: 0.25 },
  FCP: { good: 1800, poor: 3000 },
  TTFB: { good: 800, poor: 1800 },
  renderTime: { good: 16, poor: 100 },
  memoryUsage: { good: 50, poor: 80 }
};

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private isEnabled = true;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
      console.log('Performance Monitor: Initialized successfully');
    }
  }

  private initializeObservers() {
    // Observe Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.recordMetric('LCP', lastEntry.startTime);
        });
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
      } catch {
        console.warn('LCP observer not supported');
      }

      // Observe First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            const fid = entry.processingStart - entry.startTime;
            this.recordMetric('FID', fid);
          });
        });
        fidObserver.observe({ type: 'first-input', buffered: true });
      } catch {
        console.warn('FID observer not supported');
      }

      // Observe Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.recordMetric('CLS', clsValue);
        });
        clsObserver.observe({ type: 'layout-shift', buffered: true });
      } catch {
        console.warn('CLS observer not supported');
      }

      // Observe First Contentful Paint (FCP)
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              this.recordMetric('FCP', entry.startTime);
            }
          });
        });
        fcpObserver.observe({ type: 'paint', buffered: true });
      } catch {
        console.warn('FCP observer not supported');
      }
    }

    // Monitor memory usage periodically
    this.startMemoryMonitoring();
  }

  private startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        try {
          const memory = (performance as any).memory;
          if (memory) {
            const percentage = Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100);
            this.recordMetric('memoryUsage', percentage);
          }
        } catch (_error) {
          console.warn('Memory monitoring failed:', _error);
        }
      }, 30000);
    }
  }

  private recordMetric(name: string, value: number) {
    if (!this.isEnabled) return;
    
    const _currentRoute = window.location.pathname;
    const _timestamp = Date.now();
    
    // Find or create metrics entry
    let metrics = this.metrics.find(m => 
      m.route === _currentRoute && Math.abs(m.timestamp - _timestamp) < 5000
    );
    
    if (!metrics) {
      metrics = {
        route: _currentRoute,
        timestamp: _timestamp
      };
      this.metrics.push(metrics);
    }

    // Type-safe assignment
    switch (name) {
      case 'LCP':
        metrics.LCP = value;
        break;
      case 'FID':
        metrics.FID = value;
        break;
      case 'CLS':
        metrics.CLS = value;
        break;
      case 'FCP':
        metrics.FCP = value;
        break;
      case 'TTFB':
        metrics.TTFB = value;
        break;
      case 'renderTime':
        metrics.renderTime = value;
        break;
      case 'memoryUsage':
        metrics.memoryUsage = value;
        break;
    }

    // Check thresholds
    this.checkThreshold(name, value);

    // Keep only recent metrics
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    console.log(`Performance metric recorded: ${name} = ${value}`);
  }

  private checkThreshold(metric: string, value: number) {
    const threshold = THRESHOLDS[metric as keyof typeof THRESHOLDS];
    if (!threshold) return;

    let alertType: 'warning' | 'error' | 'info' = 'info';
    let message = '';

    if (value > threshold.poor) {
      alertType = 'error';
      message = `Poor ${metric}: ${value.toFixed(2)} exceeds ${threshold.poor}`;
    } else if (value > threshold.good) {
      alertType = 'warning';
      message = `${metric} needs improvement: ${value.toFixed(2)} exceeds ${threshold.good}`;
    }

    if (alertType !== 'info') {
      this.createAlert(alertType, metric, value, threshold.poor, message);
    }
  }

  private createAlert(type: PerformanceAlert['type'], metric: string, value: number, threshold: number, message: string) {
    const alert: PerformanceAlert = {
      type,
      metric,
      value,
      threshold,
      message,
      timestamp: Date.now()
    };

    this.alerts.push(alert);
    // Log alert with appropriate console method
    switch (type) {
      case 'error':
        console.error(`Performance Alert: ${message}`);
        break;
      case 'warning':
        console.warn(`Performance Alert: ${message}`);
        break;
      case 'info':
        console.info(`Performance Alert: ${message}`);
        break;
    }

    // Keep only recent alerts
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50);
    }

    // Dispatch event for UI components
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('performance-alert', { detail: alert }));
    }
  }

  // Public API
  public startRenderTracking(componentName: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const renderTime = performance.now() - startTime;
      console.log(`Render time for ${componentName}: ${renderTime.toFixed(2)}ms`);
      
      if (renderTime > THRESHOLDS.renderTime.poor) {
        this.createAlert('warning', 'renderTime', renderTime, THRESHOLDS.renderTime.poor,
          `Slow render: ${componentName} took ${renderTime.toFixed(2)}ms`);
      }
      
      return renderTime;
    };
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  public getSummary() {
    if (this.metrics.length === 0) return null;

    const latest = this.metrics[this.metrics.length - 1];
    return {
      current: latest,
      alertsCount: {
        error: this.alerts.filter(a => a.type === 'error').length,
        warning: this.alerts.filter(a => a.type === 'warning').length,
        info: this.alerts.filter(a => a.type === 'info').length,
      }
    };
  }

  public clearMetrics() {
    this.metrics = [];
    this.alerts = [];
  }

  public enable() {
    this.isEnabled = true;
  }

  public disable() {
    this.isEnabled = false;
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Export convenient functions
export const startRenderTracking = (componentName: string) => 
  performanceMonitor.startRenderTracking(componentName);

export const getPerformanceMetrics = () => 
  performanceMonitor.getMetrics();

export const getPerformanceAlerts = () => 
  performanceMonitor.getAlerts();

export const getPerformanceSummary = () => 
  performanceMonitor.getSummary();

export const clearPerformanceMetrics = () => 
  performanceMonitor.clearMetrics();

export default performanceMonitor;
