import {
  sanitizeString,
  containsXssPatterns,
  containsSqlInjection,
  validateRateLimit
} from '../inputSanitization';
import {
  escapeHTML,
  validateInput,
  generateCSRFToken,
  validateCSRFToken
} from '../securityUtils';

describe('Security Validation Tests', () => {
  describe('Input Sanitization', () => {
    it('should detect XSS patterns', () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        'javascript' + ':' + 'alert("XSS")',
        '<img src="x" onerror="alert(1)">',
        '<svg onload="alert(1)">',
        'onclick="alert(1)"',
        'onmouseover="alert(1)"'
      ];

      xssPayloads.forEach((payload, index) => {
        const result = containsXssPatterns(payload);
        if (!result) {
          console.log(`Failed to detect XSS in payload ${index}: "${payload}"`);
        }
        expect(result).toBe(true);
      });
    });

    it('should detect SQL injection patterns', () => {
      const sqlPayloads = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "UNION SELECT * FROM users",
        "'; INSERT INTO users VALUES ('hacker', 'password'); --",
        "admin'--",
        "' OR 1=1--"
      ];

      sqlPayloads.forEach(payload => {
        expect(containsSqlInjection(payload)).toBe(true);
      });
    });

    it('should sanitize malicious input', () => {
      const maliciousInput = '<script>alert("XSS")</script>Hello World';
      const sanitized = sanitizeString(maliciousInput, {
        level: 'STRICT',
        allowHtml: false,
        maxLength: 1000,
        trimWhitespace: true,
        removeControlChars: true,
        normalizeUnicode: true
      });

      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toContain('Hello World');
    });

    it('should handle safe input correctly', () => {
      const safeInputs = [
        'Hello World',
        '<EMAIL>',
        '************',
        'https://example.com',
        'Normal text with spaces and punctuation!'
      ];

      safeInputs.forEach(input => {
        expect(containsXssPatterns(input)).toBe(false);
        expect(containsSqlInjection(input)).toBe(false);

        const sanitized = sanitizeString(input);
        expect(sanitized).toBe(input.trim());
      });
    });
  });

  describe('Security Utils', () => {
    it('should escape HTML correctly', () => {
      const htmlInput = '<div>Hello & "World"</div>';
      const escaped = escapeHTML(htmlInput);

      expect(escaped).toContain('&lt;');
      expect(escaped).toContain('&gt;');
      expect(escaped).toContain('&amp;');
      expect(escaped).toContain('&quot;');
    });

    it('should validate different input types', () => {
      // Email validation
      expect(validateInput('<EMAIL>', 'email')).toBe('<EMAIL>');
      expect(validateInput('invalid-email', 'email')).toBe(null);

      // Phone validation
      expect(validateInput('************', 'phone')).toBe('1234567890');
      expect(validateInput('invalid-phone', 'phone')).toBe(null);

      // URL validation
      expect(validateInput('https://example.com', 'url')).toBe('https://example.com/');
      expect(validateInput('invalid-url', 'url')).toBe(null);

      // Number validation
      expect(validateInput('123', 'number')).toBe(123);
      expect(validateInput('abc', 'number')).toBe(null);
    });

    it('should generate and validate CSRF tokens', () => {
      const token1 = generateCSRFToken();
      const token2 = generateCSRFToken();

      // Tokens should be different
      expect(token1).not.toBe(token2);

      // Tokens should be valid format (base64-like)
      expect(token1).toMatch(/^[A-Za-z0-9+/]+=*$/);
      expect(token2).toMatch(/^[A-Za-z0-9+/]+=*$/);

      // Token validation should work
      expect(validateCSRFToken(token1, token1)).toBe(true);
      expect(validateCSRFToken(token1, token2)).toBe(false);
      expect(validateCSRFToken(token1, 'invalid')).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    beforeEach(() => {
      // Clear any existing rate limit data
      localStorage.clear();
      sessionStorage.clear();
    });

    it('should allow requests within rate limit', () => {
      const identifier = 'test-user';
      const limit = 5;
      const windowMs = 60000; // 1 minute

      // Should allow first few requests
      for (let i = 0; i < limit; i++) {
        expect(validateRateLimit(identifier, limit, windowMs)).toBe(true);
      }
    });

    it('should block requests exceeding rate limit', () => {
      const identifier = 'test-user-2';
      const limit = 3;
      const windowMs = 60000; // 1 minute

      // Allow up to limit
      for (let i = 0; i < limit; i++) {
        expect(validateRateLimit(identifier, limit, windowMs)).toBe(true);
      }

      // Should block subsequent requests
      expect(validateRateLimit(identifier, limit, windowMs)).toBe(false);
      expect(validateRateLimit(identifier, limit, windowMs)).toBe(false);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null and undefined inputs safely', () => {
      expect(sanitizeString(null)).toBe('');
      expect(sanitizeString(undefined)).toBe('');
      expect(containsXssPatterns(null as any)).toBe(false);
      expect(containsSqlInjection(undefined as any)).toBe(false);
    });

    it('should handle empty strings', () => {
      expect(sanitizeString('')).toBe('');
      expect(containsXssPatterns('')).toBe(false);
      expect(containsSqlInjection('')).toBe(false);
    });

    it('should handle very long inputs', () => {
      const longInput = 'a'.repeat(10000);
      const sanitized = sanitizeString(longInput, { maxLength: 1000 });
      expect(sanitized.length).toBeLessThanOrEqual(1000);
    });

    it('should handle special characters and unicode', () => {
      const unicodeInput = '🚀 Hello 世界 مرحبا';
      const sanitized = sanitizeString(unicodeInput, { normalizeUnicode: true });
      expect(sanitized).toContain('🚀');
      expect(sanitized).toContain('Hello');
      expect(sanitized).toContain('世界');
      expect(sanitized).toContain('مرحبا');
    });
  });
});
