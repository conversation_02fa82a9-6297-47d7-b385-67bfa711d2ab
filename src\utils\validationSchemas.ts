import { z } from 'zod';

/**
 * Zod validation schemas for use throughout the application
 * These provide runtime type validation for user inputs and API data
 */

/**
 * Common validation patterns
 */
const patterns = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^\+?[0-9]{10,15}$/,
  zipCode: /^[0-9]{5}(-[0-9]{4})?$/,
  creditCard: /^[0-9]{13,19}$/,
  cvv: /^[0-9]{3,4}$/,
  expiry: /^(0[1-9]|1[0-2])\/[0-9]{2}$/,
  url: /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/,
};

/**
 * User registration schema
 */
export const userRegistrationSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters').max(50),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').max(50),
  email: z.string().email('Please enter a valid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

/**
 * User login schema
 */
export const userLoginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

/**
 * Customer information schema for checkout
 */
export const customerInformationSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  firstName: z.string().min(2, 'First name must be at least 2 characters').max(50),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').max(50),
  phone: z.string().regex(patterns.phone, 'Please enter a valid phone number'),
  shippingAddress: z.object({
    address1: z.string().min(5, 'Address must be at least 5 characters'),
    address2: z.string().optional(),
    city: z.string().min(2, 'City must be at least 2 characters'),
    state: z.string().min(2, 'Please select a state'),
    postalCode: z.string().regex(patterns.zipCode, 'Please enter a valid ZIP code'),
    country: z.string().min(2, 'Please select a country'),
  }),
  billingSameAsShipping: z.boolean(),
  billingAddress: z.object({
    address1: z.string().min(5, 'Address must be at least 5 characters'),
    address2: z.string().optional(),
    city: z.string().min(2, 'City must be at least 2 characters'),
    state: z.string().min(2, 'Please select a state'),
    postalCode: z.string().regex(patterns.zipCode, 'Please enter a valid ZIP code'),
    country: z.string().min(2, 'Please select a country'),
  }).optional(),
  specialInstructions: z.string().optional(),
}).refine(
  data => data.billingSameAsShipping || data.billingAddress,
  {
    message: 'Billing address is required when different from shipping',
    path: ['billingAddress'],
  }
);

/**
 * Payment information schema
 */
export const paymentInformationSchema = z.object({
  paymentMethod: z.enum(['credit_card', 'paypal']),
  creditCard: z.object({
    cardNumber: z.string().regex(patterns.creditCard, 'Please enter a valid card number'),
    nameOnCard: z.string().min(3, 'Name must be at least 3 characters'),
    expiryDate: z.string().regex(patterns.expiry, 'Please enter a valid expiration date (MM/YY)'),
    cvv: z.string().regex(patterns.cvv, 'Please enter a valid CVV'),
  }).optional(),
}).refine(
  data => data.paymentMethod !== 'credit_card' || data.creditCard,
  {
    message: 'Credit card information is required',
    path: ['creditCard'],
  }
);

/**
 * Product schema
 */
export const productSchema = z.object({
  id: z.string().uuid('Invalid product ID'),
  name: z.string().min(3, 'Product name must be at least 3 characters'),
  description: z.string().min(10, 'Product description must be at least 10 characters'),
  price: z.number().positive('Price must be positive'),
  compareAtPrice: z.number().optional(),
  images: z.array(z.string().url('Invalid image URL')).min(1, 'At least one image is required'),
  inventory: z.number().int().nonnegative('Inventory must be non-negative'),
  category: z.string(),
  tags: z.array(z.string()).optional(),
  rating: z.number().min(0).max(5).optional(),
  reviewCount: z.number().int().nonnegative().optional(),
  featured: z.boolean().optional(),
  new: z.boolean().optional(),
  bestSeller: z.boolean().optional(),
  variants: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      price: z.number().positive(),
      inventory: z.number().int().nonnegative(),
    })
  ).optional(),
});

/**
 * Review submission schema
 */
export const reviewSubmissionSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
  rating: z.number().min(1, 'Please select a rating').max(5),
  title: z.string().min(3, 'Title must be at least 3 characters').max(100),
  content: z.string().min(10, 'Review must be at least 10 characters'),
  recommend: z.boolean().optional(),
  images: z.array(z.any()).optional(), // File objects - validated separately
  name: z.string().min(2, 'Name must be at least 2 characters').max(50),
  email: z.string().email('Please enter a valid email address'),
  consent: z.boolean().refine(val => val === true, {
    message: 'You must consent to the review terms',
  }),
});

/**
 * Order schema
 */
export const orderSchema = z.object({
  id: z.string(),
  userId: z.string(),
  items: z.array(
    z.object({
      productId: z.string(),
      name: z.string(),
      price: z.number().positive(),
      quantity: z.number().int().positive(),
      image: z.string(),
      variantId: z.string().optional(),
      variantName: z.string().optional(),
    })
  ),
  shippingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    address1: z.string(),
    address2: z.string().optional(),
    city: z.string(),
    state: z.string(),
    postalCode: z.string(),
    country: z.string(),
    phone: z.string(),
  }),
  billingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    address1: z.string(),
    address2: z.string().optional(),
    city: z.string(),
    state: z.string(),
    postalCode: z.string(),
    country: z.string(),
    phone: z.string(),
  }).optional(),
  subtotal: z.number().positive(),
  shipping: z.number().nonnegative(),
  tax: z.number().nonnegative(),
  total: z.number().positive(),
  status: z.enum([
    'pending',
    'processing',
    'shipped',
    'delivered',
    'cancelled',
    'refunded',
  ]),
  paymentStatus: z.enum([
    'pending',
    'paid',
    'failed',
    'refunded',
    'partially_refunded',
  ]),
  paymentMethod: z.enum(['credit_card', 'paypal']),
  paymentId: z.string(),
  specialInstructions: z.string().optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

/**
 * Contact form schema
 */
export const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(50),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(3, 'Subject must be at least 3 characters').max(100),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  consent: z.boolean().refine(val => val === true, {
    message: 'You must consent to the privacy policy',
  }),
}); 