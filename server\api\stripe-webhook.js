// Import required modules
import express from 'express';
import Stripe from 'stripe';
import dotenv from 'dotenv';
import { buffer } from 'micro';
import orderRepository from '../db/repositories/orderRepository.js';

// Initialize environment variables
dotenv.config();

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.VITE_STRIPE_SECRET_KEY);
const webhookSecret = process.env.VITE_STRIPE_WEBHOOK_SECRET;

const router = express.Router();

/**
 * Handle Stripe webhook events
 * 
 * This endpoint receives webhook events from Stripe and processes them accordingly.
 * It verifies the webhook signature to ensure it's coming from <PERSON>e.
 * 
 * @route POST /api/stripe-webhook
 * @returns {Object} Status of the webhook processing
 */
router.post('/stripe-webhook', async (req, res) => {
  if (req.method !== 'POST') {
    res.setHeader('Allow', 'POST');
    return res.status(405).end('Method Not Allowed');
  }

  // Get the raw body for signature verification
  const rawBody = await buffer(req);
  const signature = req.headers['stripe-signature'];

  let event;

  try {
    // Verify the webhook signature
    if (webhookSecret) {
      event = stripe.webhooks.constructEvent(
        rawBody,
        signature,
        webhookSecret
      );
    } else {
      // For development without a webhook secret
      event = JSON.parse(rawBody.toString());
    }
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle different event types
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        console.log(`PaymentIntent for ${paymentIntent.amount} was successful!`);
        
        // Extract order ID from metadata
        const { orderId } = paymentIntent.metadata || {};
        
        if (orderId) {
          // Update order status in database
          const updatedOrder = await orderRepository.updateOrderStatus(
            orderId,
            'processing',
            'paid'
          );
          
          if (updatedOrder) {
            console.log(`Order ${orderId} updated to paid status`);
          } else {
            console.warn(`Order ${orderId} not found when updating payment status`);
          }
        } else {
          console.warn('No order ID found in payment intent metadata');
        }
        
        break;
        
      case 'payment_intent.payment_failed':
        const failedPaymentIntent = event.data.object;
        console.log(`Payment failed: ${failedPaymentIntent.last_payment_error?.message}`);
        
        // Extract order ID from metadata
        const failedOrderId = failedPaymentIntent.metadata?.orderId;
        
        if (failedOrderId) {
          // Update order status in database
          const updatedOrder = await orderRepository.updateOrderStatus(
            failedOrderId,
            'pending',
            'failed'
          );
          
          if (updatedOrder) {
            console.log(`Order ${failedOrderId} marked as payment failed`);
          } else {
            console.warn(`Order ${failedOrderId} not found when updating payment status`);
          }
        } else {
          console.warn('No order ID found in failed payment intent metadata');
        }
        
        break;
        
      case 'charge.refunded':
        const charge = event.data.object;
        console.log(`Charge refunded: ${charge.id}`);
        
        // Get the payment intent ID from the charge
        const paymentIntentId = charge.payment_intent;
        
        if (paymentIntentId) {
          // Get the payment intent to extract metadata
          const refundedIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
          const refundedOrderId = refundedIntent.metadata?.orderId;
          
          if (refundedOrderId) {
            // Determine if it's a full or partial refund
            const refundStatus = charge.amount_refunded === charge.amount
              ? 'refunded'
              : 'partially_refunded';
            
            // Update order status in database
            const updatedOrder = await orderRepository.updateOrderStatus(
              refundedOrderId,
              'refunded',
              refundStatus
            );
            
            if (updatedOrder) {
              console.log(`Order ${refundedOrderId} marked as ${refundStatus}`);
            } else {
              console.warn(`Order ${refundedOrderId} not found when updating refund status`);
            }
          } else {
            console.warn('No order ID found in refunded payment intent metadata');
          }
        } else {
          console.warn('No payment intent ID found in refunded charge');
        }
        
        break;
        
      case 'charge.succeeded':
        const successfulCharge = event.data.object;
        console.log(`Charge succeeded: ${successfulCharge.id}`);
        
        // Process successful charge
        // In a full implementation:
        // 1. Generate receipt
        // 2. Update inventory
        // 3. Send notification emails
        // 4. Trigger fulfillment process
        
        break;
        
      default:
        console.log(`Unhandled event type ${event.type}`);
    }
  } catch (error) {
    console.error(`Error processing webhook event ${event.type}:`, error);
    // Still return 200 to acknowledge receipt
  }

  // Return a 200 response to acknowledge receipt of the event
  res.json({ received: true });
});

export default router;
