import { useState } from 'react';
import { Order } from '@/types/order';
import { formatDate } from '@/utils/formatDate';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { downloadReceipt, emailReceipt } from '@/services/receiptService';
import { useEmail } from '@/contexts/EmailContext';
import { toast } from 'sonner';
import { Download, Mail, Printer, Loader2 } from 'lucide-react';

interface OrderReceiptProps {
  order: Order;
  email?: string;
}

const OrderReceipt = ({ order, email }: OrderReceiptProps) => {
  const { sendOrderReceipt } = useEmail();
  const [isDownloading, setIsDownloading] = useState(false);
  const [isEmailing, setIsEmailing] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      await downloadReceipt(order);
    } catch (error) {
      console.error('Error downloading receipt:', error);
      toast.error('Failed to download receipt');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleEmail = async () => {
    if (!email) {
      toast.error('Email address is required');
      return;
    }

    setIsEmailing(true);
    try {
      // First, generate a receipt URL using the receipt service
      const receipt = await emailReceipt(order, email);

      // Then, send the receipt email using the email service
      const customerName = order.shippingAddress.fullName;
      const receiptUrl = receipt ? `/receipts/order-${order.id}.pdf` : undefined;

      const success = await sendOrderReceipt(order, customerName, receiptUrl);

      if (success) {
        toast.success(`Receipt sent to ${email}`);
      } else {
        toast.error('Failed to send receipt email. Check your email preferences.');
      }
    } catch (error) {
      console.error('Error emailing receipt:', error);
      toast.error('Failed to email receipt');
    } finally {
      setIsEmailing(false);
    }
  };

  const handlePrint = () => {
    setIsPrinting(true);
    try {
      // In a real application, this would open a print dialog
      // For demo purposes, we'll just show a success message
      setTimeout(() => {
        toast.success('Print dialog opened');
        setIsPrinting(false);
      }, 1000);
    } catch (error) {
      console.error('Error printing receipt:', error);
      toast.error('Failed to print receipt');
      setIsPrinting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-sabone-gold">Order Receipt</h3>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={handleDownload}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Download
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={handleEmail}
            disabled={isEmailing || !email}
          >
            {isEmailing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Mail className="h-4 w-4 mr-2" />
            )}
            Email
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={handlePrint}
            disabled={isPrinting}
          >
            {isPrinting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Printer className="h-4 w-4 mr-2" />
            )}
            Print
          </Button>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      <div className="bg-sabone-charcoal/50 p-6 rounded-md border border-sabone-gold/20">
        <div className="text-center mb-6">
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Receipt</h2>
          <p className="text-sabone-cream/70">Order #{order.id} - {formatDate(order.createdAt)}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div>
            <h4 className="font-medium text-sabone-gold mb-2">Billing Information</h4>
            <p className="text-sabone-cream/80">
              {order.billingAddress ? order.billingAddress.fullName : order.shippingAddress.fullName}<br />
              {order.billingAddress ? order.billingAddress.addressLine1 : order.shippingAddress.addressLine1}<br />
              {order.billingAddress && order.billingAddress.addressLine2 ? `${order.billingAddress.addressLine2}<br />` : ''}
              {order.billingAddress ? order.billingAddress.city : order.shippingAddress.city}, {' '}
              {order.billingAddress ? order.billingAddress.state : order.shippingAddress.state} {' '}
              {order.billingAddress ? order.billingAddress.zipCode : order.shippingAddress.zipCode}<br />
              {order.billingAddress ? order.billingAddress.country : order.shippingAddress.country}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-sabone-gold mb-2">Shipping Information</h4>
            <p className="text-sabone-cream/80">
              {order.shippingAddress.fullName}<br />
              {order.shippingAddress.addressLine1}<br />
              {order.shippingAddress.addressLine2 ? `${order.shippingAddress.addressLine2}<br />` : ''}
              {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}<br />
              {order.shippingAddress.country}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-sabone-gold mb-2">Order Details</h4>
            <p className="text-sabone-cream/80">
              <span className="block"><strong>Order Date:</strong> {formatDate(order.createdAt)}</span>
              <span className="block"><strong>Order ID:</strong> {order.id}</span>
              <span className="block"><strong>Payment Method:</strong> {
                order.paymentMethod === 'credit_card' ? 'Credit Card' :
                order.paymentMethod === 'paypal' ? 'PayPal' :
                'Cash on Delivery'
              }</span>
              <span className="block"><strong>Payment Status:</strong> {
                order.paymentStatus === 'paid' ? 'Paid' :
                order.paymentStatus === 'pending' ? 'Pending' :
                order.paymentStatus === 'failed' ? 'Failed' :
                'Refunded'
              }</span>
            </p>
          </div>
        </div>

        <div className="overflow-x-auto mb-6">
          <table className="w-full">
            <thead>
              <tr className="border-b border-sabone-gold/20">
                <th className="text-left py-2 text-sabone-gold">Product</th>
                <th className="text-center py-2 text-sabone-gold">Quantity</th>
                <th className="text-right py-2 text-sabone-gold">Price</th>
                <th className="text-right py-2 text-sabone-gold">Total</th>
              </tr>
            </thead>
            <tbody>
              {order.items.map((item, index) => (
                <tr key={index} className="border-b border-sabone-gold/10">
                  <td className="py-3 text-sabone-cream">{item.name}</td>
                  <td className="py-3 text-center text-sabone-cream">{item.quantity}</td>
                  <td className="py-3 text-right text-sabone-cream">${item.price.toFixed(2)}</td>
                  <td className="py-3 text-right text-sabone-cream">${(item.price * item.quantity).toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex justify-end">
          <div className="w-full max-w-xs">
            <div className="flex justify-between py-2">
              <span className="text-sabone-cream">Subtotal:</span>
              <span className="text-sabone-cream">${order.subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between py-2">
              <span className="text-sabone-cream">Shipping:</span>
              <span className="text-sabone-cream">${order.shipping.toFixed(2)}</span>
            </div>
            <div className="flex justify-between py-2">
              <span className="text-sabone-cream">Tax:</span>
              <span className="text-sabone-cream">${order.tax.toFixed(2)}</span>
            </div>
            <Separator className="my-2 bg-sabone-gold/20" />
            <div className="flex justify-between py-2 font-medium">
              <span className="text-sabone-gold">Total:</span>
              <span className="text-sabone-gold">${order.total.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="text-center text-sabone-cream/60 text-sm">
        <p>Thank you for your purchase!</p>
        <p>Sabone - Crafted in Berlin. Rooted in Aleppo.</p>
      </div>
    </div>
  );
};

export default OrderReceipt;
