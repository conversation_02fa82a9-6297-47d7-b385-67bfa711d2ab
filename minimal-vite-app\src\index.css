:root {
  font-family: Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: #e5dcc5;
  background-color: #1c1c1c;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
  color: #c6a870;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #c6a870;
  color: #1c1c1c;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #e5dcc5;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}
