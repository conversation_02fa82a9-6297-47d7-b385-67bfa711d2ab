# 🧪 Sabone.store Checkout Testing

This directory contains the testing infrastructure for the Sabone.store checkout flow, including both Stripe and PayPal payment methods.

## 📋 Overview

The testing implementation consists of:

1. **Checkout Test Server** - A mock server that simulates the payment processing APIs for both Stripe and PayPal.
2. **Checkout Test Runner** - A script that uses Puppeteer to automate browser interactions and test the checkout flow.
3. **Checkout Test Script** - A JavaScript file that can be loaded in the browser console for manual testing.

## 🚀 Getting Started

### Prerequisites

Make sure you have the following dependencies installed:

```bash
npm install puppeteer chalk uuid
```

These dependencies are already added to the `package.json` file.

### Running the Tests

#### Automated Testing

To run the automated checkout tests:

```bash
npm run test:checkout
```

This will:
1. Start the test server
2. Launch a browser with Puppeteer
3. Run the Stripe checkout test
4. Run the PayPal checkout test
5. Close the browser and stop the test server

#### Manual Testing

To run the test server for manual testing:

```bash
npm run test:server
```

This will start the test server on port 3001 (or the port specified in the `TEST_PORT` environment variable).

Then, you can load the checkout test script in your browser console:

```javascript
// Load the test script
const script = document.createElement('script');
script.src = '/checkout_test_script.js';
document.head.appendChild(script);

// Wait for the script to load
setTimeout(() => {
  // Run the Stripe checkout test
  window.saboneTests.testStripeCheckout();
  
  // Or run the PayPal checkout test
  // window.saboneTests.testPayPalCheckout();
}, 1000);
```

## 📝 Test Server API Endpoints

The test server provides the following API endpoints:

- **POST /api/create-payment-intent** - Creates a mock Stripe payment intent
- **POST /api/confirm-payment** - Confirms a mock Stripe payment
- **POST /api/create-paypal-order** - Creates a mock PayPal order
- **POST /api/capture-paypal-payment** - Captures a mock PayPal payment
- **POST /api/send-email** - Simulates sending an email
- **POST /api/create-order** - Simulates creating an order

## 🧪 Test Data

The test server uses the following test data:

### Stripe Test Cards

- **Valid Card**: `4242 4242 4242 4242`
- **3D Secure Card**: `4000 0000 0000 3220`
- **Declined Card**: `4000 0000 0000 0002`

### Test User

- **Email**: `<EMAIL>`
- **Name**: `Test User`
- **Phone**: `************`

### Test Address

- **Full Name**: `Test User`
- **Address Line 1**: `123 Test St`
- **Address Line 2**: `Apt 4B`
- **City**: `Testville`
- **State**: `TS`
- **Zip Code**: `12345`
- **Country**: `United States`
- **Phone**: `************`

## 📊 Test Results

The test runner will output the test results to the console, including:

- ✅ Successful steps
- ❌ Failed steps
- ℹ️ Informational messages

## 🔍 Troubleshooting

### Common Issues

- **Puppeteer Launch Error**: Make sure you have the required dependencies for Puppeteer installed on your system.
- **Test Server Port Conflict**: If port 3001 is already in use, set the `TEST_PORT` environment variable to a different port.
- **Browser Automation Issues**: If Puppeteer is having trouble interacting with elements, try increasing the `slowMo` value in the configuration.

### Debugging

- Set `headless: false` in the Puppeteer configuration to see the browser interactions.
- Add `console.log` statements to the test scripts for additional debugging information.
- Check the test server logs for API request and response details.

## 🔄 Continuous Integration

These tests can be integrated into a CI/CD pipeline by:

1. Installing the required dependencies
2. Running the test server in the background
3. Running the test runner with `headless: true`
4. Checking the exit code of the test runner

Example CI configuration:

```yaml
test:
  script:
    - npm install
    - npm run test:server &
    - npm run test:checkout
    - kill $(lsof -t -i:3001)
```

## 📚 Further Reading

- [Puppeteer Documentation](https://pptr.dev/)
- [Stripe Testing Documentation](https://stripe.com/docs/testing)
- [PayPal Testing Documentation](https://developer.paypal.com/docs/api/overview/#sandbox)
