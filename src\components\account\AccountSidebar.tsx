import { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { User, Package, MapPin, LogOut, BarChart, Mail, ShieldAlert } from "lucide-react";
import SignInModal from "@/components/auth/SignInModal";

const AccountSidebar = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);

  // Get the current path to highlight the active link
  const currentPath = location.pathname;

  // Function to get initials from user name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Check if user is admin
  const isAdmin = user?.role === 'admin';

  const navItems = [
    {
      label: "Profile",
      path: "/account",
      icon: <User className="h-4 w-4 mr-2" />,
    },
    {
      label: "Orders",
      path: "/account/orders",
      icon: <Package className="h-4 w-4 mr-2" />,
    },
    {
      label: "Addresses",
      path: "/account/addresses",
      icon: <MapPin className="h-4 w-4 mr-2" />,
    },
    {
      label: "Email Preferences",
      path: "/account/email-preferences",
      icon: <Mail className="h-4 w-4 mr-2" />,
    },
    // Only show inventory management for admin users
    ...(isAdmin ? [
      {
        label: "Inventory",
        path: "/account/inventory",
        icon: <BarChart className="h-4 w-4 mr-2" />,
      }
    ] : [])
  ];

  // Admin dashboard link (only for admin users)
  const adminLink = isAdmin ? {
    label: "Admin Dashboard",
    path: "/account/admin",
    icon: <ShieldAlert className="h-4 w-4 mr-2" />,
  } : null;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3 mb-6">
        <Avatar className="h-12 w-12 border-2 border-sabone-gold/30">
          <AvatarImage src={user?.picture} alt={user?.name} />
          <AvatarFallback className="bg-sabone-gold/20 text-sabone-gold">
            {user?.name ? getInitials(user.name) : 'U'}
          </AvatarFallback>
        </Avatar>
        <div>
          <p className="text-sabone-gold font-medium">{user?.name}</p>
          <p className="text-sabone-cream/70 text-sm">{user?.email}</p>
        </div>
      </div>

      <nav className="space-y-2">
        {navItems.map((item) => (
          <Link key={item.path} to={item.path}>
            <Button
              variant="ghost"
              className={`w-full justify-start ${
                (currentPath === item.path ||
                 (item.path === '/account' && currentPath === '/account/'))
                  ? 'bg-sabone-gold/10 text-sabone-gold'
                  : 'text-sabone-cream hover:text-sabone-gold hover:bg-sabone-gold/10'
              }`}
            >
              {item.icon}
              {item.label}
            </Button>
          </Link>
        ))}

        {isAdmin && (
          <>
            <Separator className="my-2 bg-sabone-gold/20" />

            <Link to={adminLink.path}>
              <Button
                variant="ghost"
                className={`w-full justify-start ${
                  currentPath.includes('/account/admin')
                    ? 'bg-sabone-gold/10 text-sabone-gold'
                    : 'text-sabone-cream hover:text-sabone-gold hover:bg-sabone-gold/10'
                }`}
              >
                {adminLink.icon}
                {adminLink.label}
              </Button>
            </Link>
          </>
        )}

        <Separator className="my-2 bg-sabone-gold/20" />

        <Button
          variant="ghost"
          className="w-full justify-start text-sabone-cream hover:text-sabone-gold hover:bg-sabone-gold/10"
          onClick={() => {
            logout(() => {
              navigate('/');
              setTimeout(() => setIsSignInModalOpen(true), 300);
            });
          }}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>

        <SignInModal
          open={isSignInModalOpen}
          onOpenChange={setIsSignInModalOpen}
        />
      </nav>
    </div>
  );
};

export default AccountSidebar;
