
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sabone: {
					'dark-olive': '#2A2A1F',
					'rich-brown': '#3D2C2A',
					'charcoal': '#1C1C1C',
					'charcoal-deep': '#161616', // Deeper charcoal for contrast
					'charcoal-warm': '#1E1C1A', // Warmer charcoal with brown undertones
					'dark-umber': '#2C2420', // Dark brown with warm undertones
					'gold': '#C6A870',
					'gold-light': '#E8D9B5',
					'gold-rich': '#B39355', // Richer, deeper gold
					'gold-accent': '#D4B483', // Slightly different gold for accents
					'cream': '#F5F3E8',
					'dark-green': '#1A2C1A',
					'leaf': '#325A32'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			fontFamily: {
				'playfair': ['Playfair Display', 'serif'],
				'montserrat': ['Montserrat', 'sans-serif'],
				'tajawal': ['Tajawal', 'sans-serif']
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'float': {
					'0%, 100%': {
						transform: 'translateY(0)'
					},
					'50%': {
						transform: 'translateY(-8px)'
					}
				},
				'float-delayed': {
					'0%, 100%': {
						transform: 'translateY(0)'
					},
					'50%': {
						transform: 'translateY(-8px)'
					}
				},
				'subtle-float': {
					'0%, 100%': {
						transform: 'translateY(0) translateX(0)'
					},
					'25%': {
						transform: 'translateY(-4px) translateX(4px)'
					},
					'50%': {
						transform: 'translateY(0) translateX(0)'
					},
					'75%': {
						transform: 'translateY(4px) translateX(-4px)'
					}
				},
				'slow-rotate': {
					'0%': {
						transform: 'rotate(0deg)'
					},
					'100%': {
						transform: 'rotate(360deg)'
					}
				},
				'shimmer': {
					'0%': {
						backgroundPosition: '-200% 0'
					},
					'100%': {
						backgroundPosition: '200% 0'
					}
				},
				'pulse-glow': {
					'0%, 100%': {
						boxShadow: '0 0 10px rgba(198, 168, 112, 0.3)'
					},
					'50%': {
						boxShadow: '0 0 20px rgba(198, 168, 112, 0.6)'
					}
				},
				'slow-pulse': {
					'0%, 100%': {
            transform: 'scale(1)',
            opacity: '1',
					},
					'50%': {
            transform: 'scale(1.03)',
            opacity: '0.85',
					}
				},
				'scale-up': {
					'0%': {
						transform: 'scale(1)'
					},
					'50%': {
						transform: 'scale(1.05)'
					},
					'100%': {
						transform: 'scale(1)'
					}
				},
				'staggered-fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(20px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'subtle-parallax': {
					'0%': {
						transform: 'translateY(0)'
					},
					'50%': {
						transform: 'translateY(-5px)'
					},
					'100%': {
						transform: 'translateY(0)'
					}
				},
				'elegant-scale': {
					'0%': {
						transform: 'scale(1)'
					},
					'100%': {
						transform: 'scale(1.03)'
					}
				},
				'gentle-bounce': {
					'0%, 100%': {
						transform: 'translateY(0)'
					},
					'50%': {
						transform: 'translateY(-3px)'
					}
				},
				'soft-shadow-pulse': {
					'0%, 100%': {
						boxShadow: '0 4px 12px rgba(198, 168, 112, 0.15)'
					},
					'50%': {
						boxShadow: '0 6px 16px rgba(198, 168, 112, 0.25)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.6s ease-out',
				'float': 'float 6s ease-in-out infinite',
				'float-delayed': 'float 6s ease-in-out 2s infinite',
				'subtle-float': 'subtle-float 15s ease-in-out infinite',
				'slow-rotate': 'slow-rotate 120s linear infinite',
				'shimmer': 'shimmer 8s ease-in-out infinite',
				'soft-glow': 'soft-glow 3s ease-in-out infinite',
				'pulse-glow': 'pulse-glow 4s ease-in-out infinite',
				'slow-pulse': 'slow-pulse 8s ease-in-out infinite',
				'scale-up': 'scale-up 10s ease-in-out infinite',
				'staggered-fade-in-1': 'staggered-fade-in 0.8s ease-out forwards',
				'staggered-fade-in-2': 'staggered-fade-in 0.8s ease-out 0.2s forwards',
				'staggered-fade-in-3': 'staggered-fade-in 0.8s ease-out 0.4s forwards',
				'staggered-fade-in-4': 'staggered-fade-in 0.8s ease-out 0.6s forwards',
				'subtle-parallax': 'subtle-parallax 8s ease-in-out infinite',
				'elegant-scale': 'elegant-scale 0.3s ease-out',
				'elegant-scale-reverse': 'elegant-scale 0.3s ease-out reverse',
				'gentle-bounce': 'gentle-bounce 1.5s ease-in-out infinite',
				'soft-shadow-pulse': 'soft-shadow-pulse 3s ease-in-out infinite'
			},
			backgroundImage: {
				'gold-gradient': 'linear-gradient(45deg, #C6A870, #E8D9B5, #C6A870)',
				'gold-gradient-rich': 'linear-gradient(45deg, #B39355, #D4B483, #B39355)',
				'dark-gradient': 'linear-gradient(120deg, #1C1C1C, #2A2A1F, #1C1C1C)',
				'luxury-gradient': 'linear-gradient(to bottom, rgba(28, 28, 28, 1), rgba(42, 42, 31, 0.8), rgba(28, 28, 28, 1))',
				'luxury-gradient-enhanced': 'linear-gradient(to bottom, rgba(22, 22, 22, 1), rgba(44, 36, 32, 0.85), rgba(22, 22, 22, 1))',
				'radial-glow': 'radial-gradient(circle, rgba(198, 168, 112, 0.08) 0%, transparent 70%)',
				'radial-glow-subtle': 'radial-gradient(circle, rgba(198, 168, 112, 0.05) 0%, transparent 60%)',
				'texture-overlay': 'url("/texture-subtle.png")',
				'leaf-pattern': "url('/leaf-pattern.svg')",
				'arabesque': "url('/arabesque.svg')",
				'bg-pattern': "url('/bg-pattern.svg')",
				'smoke-overlay': "url('/smoke-overlay.svg')",
				'olive-leaf-motif': "url('/olive-leaf-motif.svg')"
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
