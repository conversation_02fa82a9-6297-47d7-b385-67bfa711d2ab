import { Helmet } from 'react-helmet-async';

interface CanonicalUrlProps {
  path?: string; // Optional path to append to the base URL
}

/**
 * Component to add canonical URL to pages
 * 
 * @param path Optional path to append to the base URL (without leading slash)
 * @returns Helmet component with canonical link
 */
const CanonicalUrl = ({ path = '' }: CanonicalUrlProps) => {
  // Base URL for the site
  const baseUrl = 'https://sabone.store';
  
  // Construct the canonical URL
  const canonicalUrl = path 
    ? `${baseUrl}/${path.startsWith('/') ? path.substring(1) : path}`
    : baseUrl;

  return (
    <Helmet>
      <link rel="canonical" href={canonicalUrl} />
    </Helmet>
  );
};

export default CanonicalUrl;
