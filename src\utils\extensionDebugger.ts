/**
 * Extension Debugger Utility
 * 
 * This utility helps identify and debug issues with browser extensions
 * that might be interfering with your application.
 */

/**
 * Checks if the error is related to a browser extension
 * @param error The error object, Event object, or any value
 * @returns True if the error is likely from a browser extension
 */
export const isExtensionError = (error: any): boolean => {
  if (!error) return false;

  // Handle different types of error objects
  let errorString = '';
  let stack = '';
  let source = '';

  // Handle Error objects
  if (error instanceof Error) {
    errorString = error.toString();
    stack = error.stack || '';
  }
  // Handle Event objects (common in promise rejections from extensions)
  else if (error instanceof Event) {
    errorString = error.type || '';
    // Check if the event target or source indicates an extension
    if (error.target && typeof error.target === 'object') {
      const target = error.target as any;
      source = target.src || target.href || target.url || '';
    }
  }
  // Handle objects with message property
  else if (typeof error === 'object' && error.message) {
    errorString = error.message.toString();
    stack = error.stack || '';
    source = error.source || error.filename || '';
  }
  // Handle string errors
  else if (typeof error === 'string') {
    errorString = error;
  }
  // Handle other object types
  else if (typeof error === 'object') {
    errorString = JSON.stringify(error);
  }

  // Enhanced patterns for extension-related errors
  const extensionPatterns = [
    'contentscript',
    'content_script',
    'contentscript.bundle.js',
    'extension://',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'message port closed',
    'port closed',
    'extension context invalidated',
    'extension context',
    'chrome.runtime',
    'browser.runtime',
    'webextension',
    'addon',
    'userscript',
    'greasemonkey',
    'tampermonkey'
  ];

  // Check all available strings for extension patterns
  const allStrings = [errorString, stack, source].join(' ').toLowerCase();

  return extensionPatterns.some(pattern =>
    allStrings.includes(pattern.toLowerCase())
  );
};

/**
 * Wraps message passing functions with error handling
 * @param messageFn The message passing function to wrap
 * @returns A wrapped function with error handling
 */
export const wrapMessageFunction = <T, R>(
  messageFn: (message: T) => Promise<R>
): ((message: T) => Promise<R>) => {
  return async (message: T): Promise<R> => {
    try {
      // Set a timeout to detect if the message port closes prematurely
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Message response timeout - port may have closed'));
        }, 5000); // 5 second timeout
      });
      
      // Race the original message function against the timeout
      return await Promise.race([
        messageFn(message),
        timeoutPromise
      ]) as R;
    } catch (error) {
      console.error('Message passing error:', error);
      if (isExtensionError(error as Error)) {
        console.warn('This appears to be a browser extension error. Try disabling extensions or using incognito mode.');
      }
      throw error;
    }
  };
};

/**
 * Enhanced extension error detection for promise rejections
 * @param reason The promise rejection reason
 * @returns True if the rejection is likely from a browser extension
 */
export const isExtensionPromiseRejection = (reason: any): boolean => {
  if (!reason) return false;

  // Check using the main isExtensionError function
  if (isExtensionError(reason)) return true;

  // Additional checks specific to promise rejections

  // Check if it's an Event object from contentscript.bundle.js
  if (reason instanceof Event) {
    return true; // Most Event objects in promise rejections are from extensions
  }

  // Check for specific extension-related promise rejection patterns
  if (typeof reason === 'object') {
    const reasonStr = JSON.stringify(reason).toLowerCase();
    if (reasonStr.includes('contentscript') ||
        reasonStr.includes('extension') ||
        reasonStr.includes('chrome-extension') ||
        reasonStr.includes('moz-extension')) {
      return true;
    }
  }

  // Check the call stack if available
  if (reason && reason.stack) {
    const stack = reason.stack.toString().toLowerCase();
    if (stack.includes('contentscript') ||
        stack.includes('extension://') ||
        stack.includes('chrome-extension://')) {
      return true;
    }
  }

  return false;
};

/**
 * Monitors for extension-related errors in the console
 */
export const monitorExtensionErrors = (): () => void => {
  // Save original console.error
  const originalConsoleError = console.error;

  // Override console.error to detect extension errors
  console.error = function(...args: any[]) {
    // Call original function first
    originalConsoleError.apply(console, args);

    // Check if this is an extension error
    const errorString = args.join(' ').toLowerCase();
    const isExtensionRelated =
      errorString.includes('contentscript.bundle.js') ||
      errorString.includes('contentscript') ||
      errorString.includes('message port closed') ||
      errorString.includes('extension://') ||
      errorString.includes('chrome-extension://') ||
      errorString.includes('moz-extension://') ||
      args.some(arg => isExtensionError(arg));

    if (isExtensionRelated) {
      console.warn(
        '%c Browser Extension Warning ',
        'background: #FFA500; color: white; font-weight: bold; padding: 2px 6px; border-radius: 3px;',
        'An error from a browser extension was detected. This may affect application performance. Try disabling extensions or using incognito mode.'
      );

      // Log additional context for debugging
      console.info(
        '%c Extension Error Context ',
        'background: #2196F3; color: white; font-weight: bold; padding: 2px 6px; border-radius: 3px;',
        {
          detectedPatterns: args.filter(arg => isExtensionError(arg)),
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        }
      );
    }
  };

  // Return a function to restore the original console.error
  return () => {
    console.error = originalConsoleError;
  };
};

/**
 * Creates a safe wrapper for postMessage
 * @param target The target window or frame
 * @returns A wrapped postMessage function with error handling
 */
export const createSafePostMessage = (
  target: Window | MessagePort | ServiceWorker
): (message: any, targetOrigin: string, transfer?: Transferable[]) => void => {
  return (message: any, targetOrigin: string, transfer?: Transferable[]) => {
    try {
      if (target instanceof Window) {
        target.postMessage(message, targetOrigin, transfer);
      } else {
        target.postMessage(message, transfer);
      }
    } catch (error) {
      console.error('Error in postMessage:', error);
      if (isExtensionError(error as Error)) {
        console.warn('This appears to be a browser extension error with postMessage.');
      }
    }
  };
};

/**
 * Initialize all extension debugging tools
 * @returns A cleanup function to remove the debugging tools
 */
export const initExtensionDebugging = (): () => void => {
  const restoreConsole = monitorExtensionErrors();
  
  // Add a global error handler
  const originalOnError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (source?.includes('contentscript.bundle.js') || 
        (error && isExtensionError(error))) {
      console.warn(
        '%c Browser Extension Error Detected ',
        'background: #FFA500; color: white; font-weight: bold',
        'Error from a browser extension was caught. This is not an issue with your application.'
      );
    }
    
    // Call the original handler if it exists
    if (typeof originalOnError === 'function') {
      return originalOnError(message, source, lineno, colno, error);
    }
    return false;
  };
  
  return () => {
    restoreConsole();
    window.onerror = originalOnError;
  };
};
