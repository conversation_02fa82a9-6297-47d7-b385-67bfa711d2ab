// Testing utilities and helpers for comprehensive test coverage
import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProductProvider } from '@/contexts/ProductContext';
import { CartProvider } from '@/contexts/CartContext';
import { WishlistProvider } from '@/contexts/WishlistContext';

// Mock data for testing
export const mockProduct = {
  id: 'test-product-1',
  name: 'Test Soap Bar',
  description: 'A test soap bar for unit testing',
  price: 12.99,
  type: 'bar' as const,
  image: '/test-image.jpg',
  ingredients: ['Test Ingredient 1', 'Test Ingredient 2'],
  benefits: ['Test Benefit 1', 'Test Benefit 2'],
  inStock: true,
  featured: false,
  category: 'soap',
  weight: '100g',
  scent: 'lavender'
};

export const mockUser = {
  sub: 'test-user-123',
  name: 'Test User',
  email: '<EMAIL>',
  picture: '/test-avatar.jpg',
  email_verified: true
};

export const mockCartItem = {
  product: mockProduct,
  quantity: 2
};

// Create a custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  withAuth?: boolean;
  withCart?: boolean;
  withProducts?: boolean;
  withWishlist?: boolean;
}

export function renderWithProviders(
  ui: ReactElement,
  {
    initialEntries: _initialEntries = ['/'],
    withAuth = true,
    withCart = true,
    withProducts = true,
    withWishlist = true,
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  // Create a new QueryClient for each test
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  function Wrapper({ children }: { children: React.ReactNode }) {
    let content = children;

    // Wrap with QueryClient
    content = (
      <QueryClientProvider client={queryClient}>
        {content}
      </QueryClientProvider>
    );

    // Wrap with Router
    content = (
      <BrowserRouter>
        {content}
      </BrowserRouter>
    );

    // Conditionally wrap with providers
    if (withAuth) {
      content = <AuthProvider>{content}</AuthProvider>;
    }

    if (withProducts) {
      content = <ProductProvider>{content}</ProductProvider>;
    }

    if (withCart) {
      content = <CartProvider>{content}</CartProvider>;
    }

    if (withWishlist) {
      content = <WishlistProvider>{content}</WishlistProvider>;
    }

    return <>{content}</>;
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock localStorage for testing
export const mockLocalStorage = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    }
  };
})();

// Mock window.matchMedia for responsive testing
export const mockMatchMedia = (query: string) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(), // deprecated
  removeListener: jest.fn(), // deprecated
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
});

// Setup function to run before each test
export const setupTest = () => {
  // Mock localStorage
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
    writable: true
  });

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(mockMatchMedia),
  });

  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Clear localStorage before each test
  mockLocalStorage.clear();
};

// Cleanup function to run after each test
export const cleanupTest = () => {
  // Clear all mocks
  jest.clearAllMocks();
  
  // Clear localStorage
  mockLocalStorage.clear();
};

// Utility to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

// Utility to simulate user interactions
export const userInteractions = {
  clickButton: async (element: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react');
    fireEvent.click(element);
    await waitForAsync();
  },
  
  typeInInput: async (element: HTMLElement, text: string) => {
    const { fireEvent } = await import('@testing-library/react');
    fireEvent.change(element, { target: { value: text } });
    await waitForAsync();
  },
  
  submitForm: async (element: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react');
    fireEvent.submit(element);
    await waitForAsync();
  }
};

// Mock API responses
export const mockApiResponses = {
  products: [mockProduct],
  user: mockUser,
  cart: [mockCartItem],
  orders: [],
  reviews: []
};

// Performance testing utilities
export const performanceHelpers = {
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    await waitForAsync();
    const end = performance.now();
    return end - start;
  },
  
  measureAsyncOperation: async (operation: () => Promise<any>) => {
    const start = performance.now();
    await operation();
    const end = performance.now();
    return end - start;
  }
};

// Accessibility testing helpers
export const a11yHelpers = {
  checkAriaLabels: (container: HTMLElement) => {
    const elementsNeedingLabels = container.querySelectorAll('button, input, select, textarea');
    const missingLabels: Element[] = [];
    
    elementsNeedingLabels.forEach(element => {
      const hasAriaLabel = element.hasAttribute('aria-label');
      const hasAriaLabelledBy = element.hasAttribute('aria-labelledby');
      const hasAssociatedLabel = element.id && container.querySelector(`label[for="${element.id}"]`);
      
      if (!hasAriaLabel && !hasAriaLabelledBy && !hasAssociatedLabel) {
        missingLabels.push(element);
      }
    });
    
    return missingLabels;
  },
  
  checkKeyboardNavigation: async (container: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react');
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const results = [];
    for (let i = 0; i < focusableElements.length; i++) {
      const element = focusableElements[i] as HTMLElement;
      fireEvent.keyDown(element, { key: 'Tab' });
      results.push({
        element,
        canFocus: document.activeElement === element
      });
    }
    
    return results;
  }
};

// Error boundary testing
export const ErrorBoundaryTest = ({ children, onError }: { 
  children: React.ReactNode; 
  onError?: (error: Error) => void;
}) => {
  try {
    return <>{children}</>;
  } catch (error) {
    if (onError) {
      onError(error as Error);
    }
    return <div data-testid="error-boundary">Error occurred</div>;
  }
};

// Custom matchers for Jest (to be added to setupTests.ts)
export const customMatchers = {
  toBeAccessible: (received: HTMLElement) => {
    const missingLabels = a11yHelpers.checkAriaLabels(received);
    const pass = missingLabels.length === 0;
    
    return {
      message: () => 
        pass 
          ? `Expected element to not be accessible`
          : `Expected element to be accessible, but found ${missingLabels.length} elements without proper labels`,
      pass,
    };
  },
  
  toHavePerformantRender: (received: number, threshold: number = 100) => {
    const pass = received <= threshold;
    
    return {
      message: () =>
        pass
          ? `Expected render time ${received}ms to be slower than ${threshold}ms`
          : `Expected render time ${received}ms to be faster than ${threshold}ms`,
      pass,
    };
  }
};

// Export everything for easy importing
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
