
import { useEffect, useState } from "react";
import FAQ from "@/components/FAQ";

const About = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsVisible(true);
      }
    }, { threshold: 0.1 });

    const element = document.getElementById("about-section");
    if (element) observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, []);

  return (
    <section id="about-section" className="py-20 px-4 sm:px-6 lg:px-8 bg-sabone-rich-brown/30 relative">
      {/* Background elements - faded mosque arch or calligraphy pattern */}
      <div
        className="absolute inset-0 opacity-10 bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/mosque-arch.svg')",
          backgroundSize: "contain",
          backgroundPosition: "center bottom"
        }}
      ></div>

      <div className="max-w-6xl mx-auto relative">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-playfair font-bold text-sabone-gold">About Sabone</h2>
            <div className="arabesque-divider w-24 mx-auto my-6"></div>
            <p className="max-w-2xl mx-auto text-lg text-sabone-cream/90 mb-8">
              Rooted in Arabic traditions of ritual purity, Sabone crafts luxury soaps and shampoos
              using only bio-certified oils and natural ingredients. Each product is handmade with
              reverence for ancient wisdom, bringing the sacred into your daily cleansing ritual.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
              <div className="space-y-6">
                <p className="text-lg text-sabone-cream/90">
                  Sabone was born from a deep appreciation for ancient Arabic bathing traditions and the healing power of natural ingredients.
                </p>
                <p className="text-lg text-sabone-cream/90">
                  Each of our products is handcrafted using time-honored techniques, bringing together the purity of nature with the luxury of self-care rituals.
                </p>
                <p className="text-lg text-sabone-cream/90">
                  We believe in reconnecting with tradition, using only organic, ethically sourced ingredients that nourish both body and soul.
                </p>
              </div>
            </div>

            <div className="space-y-8">
              <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
                <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-4">Our Commitment</h3>
                <ul className="space-y-3 text-sabone-cream/80">
                  <li className="flex items-start">
                    <span className="text-sabone-gold mr-2">•</span>
                    <span>100% natural and organic ingredients</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-sabone-gold mr-2">•</span>
                    <span>Handcrafted in small batches for quality</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-sabone-gold mr-2">•</span>
                    <span>Sustainable and eco-friendly packaging</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-sabone-gold mr-2">•</span>
                    <span>Traditional methods passed down through generations</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-sabone-gold mr-2">•</span>
                    <span>No animal testing, ever</span>
                  </li>
                </ul>
              </div>

              <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
                <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-4">Arabic Heritage</h3>
                <p className="text-sabone-cream/80">
                  Our products draw inspiration from the rich bathing traditions of Arabic culture, where cleansing is not merely physical but a ritual of purification and renewal. From traditional hammam soaps to the precious oils used in ancient beauty rituals, we honor this heritage in every bar we craft.
                </p>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className={`mt-16 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
            <FAQ />
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
