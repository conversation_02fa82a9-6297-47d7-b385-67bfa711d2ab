import * as React from "react"
import { sanitizeString, containsXssPatterns, containsSqlInjection } from "@/utils/inputSanitization"
import { validateInput } from "@/utils/securityUtils"
import { cn } from "@/lib/utils"

interface SecureInputProps extends React.ComponentProps<"input"> {
  enableSanitization?: boolean;
  validationType?: 'email' | 'phone' | 'text' | 'number' | 'url';
  onSecurityViolation?: (violation: string) => void;
}

const Input = React.forwardRef<HTMLInputElement, SecureInputProps>(
  ({ className, type, enableSanitization = true, validationType, onSecurityViolation, onChange, ...props }, ref) => {

    const handleChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;

      if (enableSanitization && value) {
        // Check for security violations
        if (containsXssPatterns(value)) {
          onSecurityViolation?.('XSS patterns detected');
          console.warn('Security: XSS patterns detected in input');
          return; // Block the input
        }

        if (containsSqlInjection(value)) {
          onSecurityViolation?.('SQL injection patterns detected');
          console.warn('Security: SQL injection patterns detected in input');
          return; // Block the input
        }

        // Sanitize the input
        const sanitized = sanitizeString(value, {
          level: 'MODERATE',
          allowHtml: false,
          maxLength: 1000,
          trimWhitespace: true,
          removeControlChars: true,
          normalizeUnicode: true,
        });

        // Validate based on type if specified
        if (validationType) {
          const validated = validateInput(sanitized, validationType);
          if (validated === null && sanitized.length > 0) {
            onSecurityViolation?.(`Invalid ${validationType} format`);
            console.warn(`Security: Invalid ${validationType} format detected`);
            return; // Block invalid input
          }
        }

        // Update the input value with sanitized content
        e.target.value = sanitized;
      }

      // Call the original onChange handler
      onChange?.(e);
    }, [enableSanitization, validationType, onSecurityViolation, onChange]);

    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        ref={ref}
        onChange={handleChange}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
