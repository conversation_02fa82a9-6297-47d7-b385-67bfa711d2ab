import React, { useState, useEffect } from 'react';
import { Product } from '@/data/products';
import { useRecommendations } from '@/contexts/RecommendationContext';
import { useCart } from '@/contexts/CartContext';
import RecommendationSection from './RecommendationSection';

interface FrequentlyBoughtTogetherProps {
  cartProductIds?: string[];
  className?: string;
  maxVisible?: number;
  title?: string;
  subtitle?: string;
}

const FrequentlyBoughtTogether: React.FC<FrequentlyBoughtTogetherProps> = ({
  cartProductIds,
  className = '',
  maxVisible = 4,
  title = "Frequently Bought Together",
  subtitle = "Complete your order with these popular combinations"
}) => {
  const [recommendations, setRecommendations] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { getFrequentlyBoughtTogether } = useRecommendations();
  const { items: cartItems } = useCart();

  // Use provided cart product IDs or get from cart context
  const productIds = cartProductIds || cartItems.map(item => item.product.id);

  const fetchRecommendations = async () => {
    if (productIds.length === 0) {
      setRecommendations([]);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      const products = await getFrequentlyBoughtTogether(productIds);
      setRecommendations(products);
    } catch (err) {
      setError('Failed to load product recommendations');
      console.error('Error fetching frequently bought together:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, [productIds.join(','), getFrequentlyBoughtTogether]);

  // Don't render if no cart items or no recommendations
  if (productIds.length === 0 || (!loading && recommendations.length === 0)) {
    return null;
  }

  return (
    <RecommendationSection
      title={title}
      subtitle={subtitle}
      products={recommendations}
      loading={loading}
      error={error}
      onRefresh={fetchRecommendations}
      maxVisible={maxVisible}
      showRefreshButton={true}
      className={className}
      trackingType="frequently_bought_together"
    />
  );
};

export default FrequentlyBoughtTogether;
