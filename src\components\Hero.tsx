
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { useInView } from "react-intersection-observer";
import { useTranslations } from "next-intl";

const Hero = () => {
  const t = useTranslations('common.hero');
  const [isLoaded, setIsLoaded] = useState(false);
  const { ref: heroRef, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const scrollToProducts = () => {
    document.getElementById("products-section")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex flex-col items-center justify-center overflow-hidden px-4 py-8 pb-16"
    >
      {/* Enhanced background elements with luxury gradient */}
      <div className="absolute inset-0 bg-luxury-gradient-enhanced"></div>

      {/* Arabic geometric pattern background with subtle parallax */}
      <div
        className="absolute inset-0 bg-bg-pattern bg-repeat opacity-10 animate-subtle-float"
        style={{transform: inView ? 'translateY(-5px)' : 'translateY(0)'}}
      ></div>

      {/* Animated smoke overlay with texture */}
      <div className="absolute inset-0 bg-smoke-overlay bg-no-repeat bg-cover opacity-15 mix-blend-overlay"></div>

      {/* Subtle texture overlay */}
      <div className="absolute inset-0 bg-texture-overlay opacity-10 mix-blend-overlay"></div>

      {/* Enhanced background effects with parallax */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-sabone-gold/5 blur-3xl animate-float"
             style={{transform: inView ? 'translateY(-8px)' : 'translateY(0)', transition: 'transform 1.5s ease-out'}}></div>
        <div className="absolute bottom-1/4 left-1/4 w-48 h-48 rounded-full bg-sabone-gold/5 blur-3xl animate-float-delayed"
             style={{transform: inView ? 'translateY(8px)' : 'translateY(0)', transition: 'transform 1.5s ease-out'}}></div>
        <div className="absolute top-1/3 left-1/3 w-72 h-72 rounded-full bg-sabone-gold/5 blur-3xl animate-scale-up"></div>
        <div className="absolute bottom-1/3 right-1/3 w-56 h-56 rounded-full bg-sabone-gold-rich/5 blur-3xl animate-subtle-parallax"></div>
      </div>

      {/* Content container with optimized top padding */}
      <div className={`z-10 pt-10 md:pt-20 transition-all duration-1000 ease-out ${isLoaded ? 'opacity-100 transform-none' : 'opacity-0 translate-y-10'}`}>
        <div className="text-center px-4 space-y-8 md:space-y-10">
          {/* Logo with olive leaf background and shadow */}
          <div className="relative max-w-[200px] md:max-w-[250px] mx-auto mb-8 md:mb-10">
            {/* Olive leaf background */}
            <div className="absolute inset-0 bg-olive-leaf-motif bg-no-repeat bg-center opacity-30 scale-125"></div>

            {/* Logo with shadow */}
            <img
              src="/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png"
              alt="Sabone Logo"
              className="w-full h-auto animate-float drop-shadow-[0_0_8px_rgba(198,168,112,0.3)]"
            />
          </div>

          {/* Staggered animation for text elements */}
          <div className={`space-y-4 md:space-y-6 ${inView ? 'opacity-100' : 'opacity-0'}`}>
            {/* Small uppercase tagline with small-caps */}
            <p className={`text-sabone-gold uppercase tracking-widest text-sm md:text-base font-semibold ${inView ? 'animate-staggered-fade-in-1' : 'opacity-0'}`}>
              {t('tagline')}
            </p>

            {/* Main headline with enhanced styling */}
            <h1 className={`font-playfair text-5xl md:text-7xl font-bold tracking-tight text-white mt-2 mb-4 leading-tight ${inView ? 'animate-staggered-fade-in-2' : 'opacity-0'}`}>
              {t('title')}
            </h1>

            {/* Description text with refined spacing */}
            <p className={`max-w-2xl mx-auto text-lg md:text-xl text-sabone-cream font-montserrat ${inView ? 'animate-staggered-fade-in-3' : 'opacity-0'}`}
              dangerouslySetInnerHTML={{ __html: t('descriptionHtml') }}
            />
          </div>

          {/* CTA section */}
          <div className={`pt-8 md:pt-10 flex flex-col items-center gap-8 ${inView ? 'animate-staggered-fade-in-4' : 'opacity-0'}`}>
            {/* Styled button with refined border and gentle glow effect */}
            <Button
              onClick={scrollToProducts}
              className="relative overflow-hidden bg-transparent border border-sabone-gold text-sabone-gold hover:bg-sabone-gold-rich hover:text-sabone-charcoal-deep transition-all duration-500 px-8 py-4 rounded-full text-lg font-medium group"
            >
              <span className="relative z-10 transition-transform duration-300 group-hover:translate-x-1">
                {t('ctaButton')}
              </span>
              <span className="absolute inset-0 bg-gold-gradient-rich opacity-0 group-hover:opacity-100 transition-opacity duration-500"></span>
              <span className="absolute inset-0 shadow-[0_0_20px_rgba(198,168,112,0.3)] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></span>
            </Button>

            {/* Enhanced scroll indicator with backdrop blur */}
            <div className="mt-6 md:mt-8 flex flex-col items-center">
              <p className="text-sabone-cream/70 text-xs mb-2 font-light tracking-wider uppercase">{t('scrollIndicator')}</p>
              <button
                onClick={scrollToProducts}
                className="group relative p-2 rounded-full bg-sabone-charcoal-deep/30 backdrop-blur-[2px] border border-sabone-gold/10 hover:border-sabone-gold/30 transition-all duration-300"
                aria-label="Scroll to products"
              >
                <ChevronDown size={20} className="text-sabone-gold/70 group-hover:text-sabone-gold transition-colors duration-300 animate-gentle-bounce" />
                <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom gradient fade */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-sabone-charcoal to-transparent"></div>
    </section>
  );
};

export default Hero;
