import { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ider } from 'react-helmet-async';
import { Navigate, Route, Routes } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Profile from "@/components/account/Profile";
import Orders from "@/components/account/Orders";
import Addresses from "@/components/account/Addresses";
import EmailPreferences from "@/components/account/EmailPreferences";
import Inventory from "@/components/account/Inventory";
import AccountSidebar from "@/components/account/AccountSidebar";

import SEO from "@/components/seo/SEO";

const Account = () => {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Safely add class to body
    try {
      if (document.body && !document.body.classList.contains("bg-sabone-charcoal")) {
        document.body.classList.add("bg-sabone-charcoal");
      }
    } catch (error) {
      console.error("Error adding class to body:", error);
    }

    return () => {
      // Safely remove class from body
      try {
        if (document.body && document.body.classList.contains("bg-sabone-charcoal")) {
          document.body.classList.remove("bg-sabone-charcoal");
        }
      } catch (error) {
        console.error("Error removing class from body:", error);
      }
    };
  }, []);

  // If the user is not authenticated and not loading, redirect to home
  if (!isLoading && !isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-sabone-charcoal flex items-center justify-center">
        <div className="text-sabone-gold">Loading...</div>
      </div>
    );
  }

  return (
    <HelmetProvider>
      <SEO
        title="My Account"
        description="Manage your Sabone account, view orders, and update your profile."
        canonical="account"
        noIndex={true}
      />

      <div className="min-h-screen bg-sabone-charcoal flex flex-col">
        <Navbar />

        <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 mt-16">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl sm:text-4xl font-playfair font-bold mb-8 text-sabone-gold text-center">
              My Account
            </h1>

            <div className="grid lg:grid-cols-4 gap-8">
              <div className="lg:col-span-1">
                <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border sticky top-24">
                  <AccountSidebar />
                </div>
              </div>

              <div className="lg:col-span-3">
                <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
                  <Routes>
                    <Route path="/" element={<Profile />} />
                    <Route path="/orders" element={<Orders />} />
                    <Route path="/addresses" element={<Addresses />} />
                    <Route path="/email-preferences" element={<EmailPreferences />} />
                    <Route path="/inventory" element={<Inventory />} />
                  </Routes>
                </div>
              </div>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </HelmetProvider>
  );
};

export default Account;
