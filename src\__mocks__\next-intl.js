// Mock for next-intl library
export const useTranslations = (namespace) => {
  return (key) => {
    // Return the key itself for testing, or provide specific translations
    const translations = {
      'common.add_to_cart': 'Add to Cart',
      'common.remove_from_cart': 'Remove from Cart',
      'common.cart': 'Cart',
      'common.wishlist': 'Wishlist',
      'common.checkout': 'Checkout',
      'common.total': 'Total',
      'common.subtotal': 'Subtotal',
      'common.shipping': 'Shipping',
      'common.loading': 'Loading...',
      'common.error': 'Error',
      'common.success': 'Success',
      'common.cancel': 'Cancel',
      'common.save': 'Save',
      'common.edit': 'Edit',
      'common.delete': 'Delete',
      'common.confirm': 'Confirm',
      'common.search': 'Search',
      'common.filter': 'Filter',
      'common.sort': 'Sort',
      'common.price': 'Price',
      'common.name': 'Name',
      'common.description': 'Description',
      'common.category': 'Category',
      'common.in_stock': 'In Stock',
      'common.out_of_stock': 'Out of Stock',
      'common.quantity': 'Quantity',
      'common.email': 'Email',
      'common.password': 'Password',
      'common.login': 'Login',
      'common.register': 'Register',
      'common.logout': 'Logout',
      'common.profile': 'Profile',
      'common.settings': 'Settings',
      'common.home': 'Home',
      'common.about': 'About',
      'common.contact': 'Contact',
      'common.privacy': 'Privacy',
      'common.terms': 'Terms',
      'common.faq': 'FAQ',
      'common.support': 'Support',
      'common.language': 'Language',
      'common.currency': 'Currency',
      'common.theme': 'Theme',
      'common.dark': 'Dark',
      'common.light': 'Light',
      'common.auto': 'Auto',
      'common.yes': 'Yes',
      'common.no': 'No',
      'common.ok': 'OK',
      'common.close': 'Close',
      'common.open': 'Open',
      'common.show': 'Show',
      'common.hide': 'Hide',
      'common.more': 'More',
      'common.less': 'Less',
      'common.all': 'All',
      'common.none': 'None',
      'common.select': 'Select',
      'common.clear': 'Clear',
      'common.reset': 'Reset',
      'common.refresh': 'Refresh',
      'common.update': 'Update',
      'common.create': 'Create',
      'common.new': 'New',
      'common.old': 'Old',
      'common.recent': 'Recent',
      'common.popular': 'Popular',
      'common.featured': 'Featured',
      'common.recommended': 'Recommended',
      'common.best_seller': 'Best Seller',
      'common.on_sale': 'On Sale',
      'common.free_shipping': 'Free Shipping',
      'common.fast_delivery': 'Fast Delivery',
      'common.secure_payment': 'Secure Payment',
      'common.money_back_guarantee': 'Money Back Guarantee',
      'common.customer_service': 'Customer Service',
      'common.track_order': 'Track Order',
      'common.order_history': 'Order History',
      'common.shipping_address': 'Shipping Address',
      'common.billing_address': 'Billing Address',
      'common.payment_method': 'Payment Method',
      'common.credit_card': 'Credit Card',
      'common.paypal': 'PayPal',
      'common.apple_pay': 'Apple Pay',
      'common.google_pay': 'Google Pay',
      'common.gift_card': 'Gift Card',
      'common.promo_code': 'Promo Code',
      'common.discount': 'Discount',
      'common.tax': 'Tax',
      'common.order_summary': 'Order Summary',
      'common.order_confirmation': 'Order Confirmation',
      'common.thank_you': 'Thank You',
      'common.order_placed': 'Order Placed',
      'common.processing': 'Processing',
      'common.shipped': 'Shipped',
      'common.delivered': 'Delivered',
      'common.cancelled': 'Cancelled',
      'common.refunded': 'Refunded',
      'common.return': 'Return',
      'common.exchange': 'Exchange',
      'common.review': 'Review',
      'common.rating': 'Rating',
      'common.stars': 'Stars',
      'common.write_review': 'Write Review',
      'common.helpful': 'Helpful',
      'common.not_helpful': 'Not Helpful',
      'common.verified_purchase': 'Verified Purchase',
      'common.size': 'Size',
      'common.color': 'Color',
      'common.material': 'Material',
      'common.brand': 'Brand',
      'common.model': 'Model',
      'common.weight': 'Weight',
      'common.dimensions': 'Dimensions',
      'common.ingredients': 'Ingredients',
      'common.nutrition': 'Nutrition',
      'common.allergens': 'Allergens',
      'common.warnings': 'Warnings',
      'common.instructions': 'Instructions',
      'common.care': 'Care',
      'common.warranty': 'Warranty',
      'common.availability': 'Availability',
      'common.stock_level': 'Stock Level',
      'common.low_stock': 'Low Stock',
      'common.back_in_stock': 'Back in Stock',
      'common.notify_me': 'Notify Me',
      'common.wishlist_added': 'Added to Wishlist',
      'common.wishlist_removed': 'Removed from Wishlist',
      'common.cart_added': 'Added to Cart',
      'common.cart_removed': 'Removed from Cart',
      'common.cart_updated': 'Cart Updated',
      'common.cart_empty': 'Cart is Empty',
      'common.continue_shopping': 'Continue Shopping',
      'common.proceed_to_checkout': 'Proceed to Checkout',
      'common.secure_checkout': 'Secure Checkout',
      'common.guest_checkout': 'Guest Checkout',
      'common.create_account': 'Create Account',
      'common.sign_in': 'Sign In',
      'common.sign_up': 'Sign Up',
      'common.forgot_password': 'Forgot Password',
      'common.reset_password': 'Reset Password',
      'common.change_password': 'Change Password',
      'common.account_created': 'Account Created',
      'common.welcome_back': 'Welcome Back',
      'common.goodbye': 'Goodbye',
      'common.see_you_soon': 'See You Soon',
    };
    
    const fullKey = namespace ? `${namespace}.${key}` : key;
    return translations[fullKey] || key;
  };
};

export const useLocale = () => 'en';

export const useFormatter = () => ({
  number: (value, options) => {
    return new Intl.NumberFormat('en-US', options).format(value);
  },
  dateTime: (value, options) => {
    return new Intl.DateTimeFormat('en-US', options).format(value);
  },
});

export const NextIntlProvider = ({ children }) => children;

export default {
  useTranslations,
  useLocale,
  useFormatter,
  NextIntlProvider,
};
