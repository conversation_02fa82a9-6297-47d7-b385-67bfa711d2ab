import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { LogIn } from "lucide-react";

interface DirectLoginButtonProps {
  className?: string;
}

const DirectLoginButton = ({ className }: DirectLoginButtonProps) => {
  const { login } = useAuth();

  const handleLogin = () => {
    console.log("DirectLoginButton: Calling login function directly");
    login();
  };

  return (
    <Button
      onClick={handleLogin}
      className={`bg-sabone-gold hover:bg-sabone-gold-rich text-sabone-charcoal ${className}`}
    >
      <LogIn className="mr-2 h-4 w-4" />
      Direct Login
    </Button>
  );
};

export default DirectLoginButton;
