import { lazyLoad } from '@/utils/lazy-load';
import { Skeleton } from '@/components/ui/skeleton';

// Custom fallbacks for different component types
const PageFallback = () => (
  <div className="min-h-screen bg-sabone-charcoal flex items-center justify-center">
    <div className="space-y-6 w-full max-w-4xl px-4">
      <Skeleton className="h-16 w-3/4 mx-auto bg-sabone-gold/10" />
      <Skeleton className="h-64 w-full bg-sabone-gold/10" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Skeleton className="h-40 w-full bg-sabone-gold/10" />
        <Skeleton className="h-40 w-full bg-sabone-gold/10" />
        <Skeleton className="h-40 w-full bg-sabone-gold/10" />
      </div>
    </div>
  </div>
);

const SectionFallback = () => (
  <div className="py-16 md:py-20 px-4 sm:px-6 lg:px-8">
    <Skeleton className="h-8 w-1/3 mx-auto bg-sabone-gold/10 mb-8" />
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Skeleton className="h-40 w-full bg-sabone-gold/10" />
      <Skeleton className="h-40 w-full bg-sabone-gold/10" />
      <Skeleton className="h-40 w-full bg-sabone-gold/10" />
    </div>
  </div>
);

const ModalFallback = () => (
  <div className="fixed inset-0 z-50 flex items-center justify-center">
    <div className="bg-sabone-dark-olive/90 backdrop-blur-sm p-6 rounded-lg w-full max-w-md mx-auto">
      <Skeleton className="h-8 w-3/4 mx-auto bg-sabone-gold/10 mb-6" />
      <Skeleton className="h-32 w-full bg-sabone-gold/10 mb-4" />
      <Skeleton className="h-10 w-full bg-sabone-gold/10" />
    </div>
  </div>
);

// Lazy load page components with preloading for critical pages
export const LazyIndex = lazyLoad(() => import('@/pages/Index'), {
  fallback: <PageFallback />,
  preload: true // Preload homepage
});

export const LazyCheckout = lazyLoad(() => import('@/pages/Checkout'), {
  fallback: <PageFallback />
});

export const LazyProductDetailPage = lazyLoad(() => import('@/pages/ProductDetailPage'), {
  fallback: <PageFallback />,
  preload: true // Preload product detail page
});

export const LazyAccount = lazyLoad(() => import('@/pages/Account'), {
  fallback: <PageFallback />
});

export const LazyDevAdminAccess = lazyLoad(() => import('@/pages/DevAdminAccess'), {
  fallback: <PageFallback />
});

export const LazyNotFound = lazyLoad(() => import('@/pages/NotFound'), {
  fallback: <PageFallback />
});

// Lazy load admin pages
export const LazyAdminDashboard = lazyLoad(() => import('@/pages/admin/AdminDashboard'), {
  fallback: <PageFallback />
});

export const LazyAdminInventory = lazyLoad(() => import('@/pages/admin/AdminInventory'), {
  fallback: <PageFallback />
});

export const LazyAdminOrders = lazyLoad(() => import('@/pages/admin/AdminOrders'), {
  fallback: <PageFallback />
});

export const LazyAdminReviews = lazyLoad(() => import('@/pages/admin/AdminReviews'), {
  fallback: <PageFallback />
});

export const LazyAdminCustomers = lazyLoad(() => import('@/pages/admin/AdminCustomers'), {
  fallback: <PageFallback />
});

export const LazyAdminSettings = lazyLoad(() => import('@/pages/admin/AdminSettings'), {
  fallback: <PageFallback />
});

// Lazy load heavy components that aren't needed immediately
export const LazyBundlesSection = lazyLoad(() => import('@/components/BundlesSection'), {
  fallback: <SectionFallback />,
  minDelay: 100 // Prevent flickering for fast loads
});

export const LazyAbout = lazyLoad(() => import('@/components/About'), {
  fallback: <SectionFallback />,
  minDelay: 100
});

export const LazyContact = lazyLoad(() => import('@/components/Contact'), {
  fallback: <SectionFallback />,
  minDelay: 100
});

export const LazyReviewSection = lazyLoad(() => import('@/components/review/ReviewSection'), {
  fallback: <SectionFallback />
});

export const LazyRelatedProducts = lazyLoad(() => import('@/components/product/RelatedProducts'), {
  fallback: <SectionFallback />
});

// Lazy load recommendation components
export const LazyCustomersAlsoBought = lazyLoad(() => import('@/components/recommendations/CustomersAlsoBought'), {
  fallback: <SectionFallback />
});

export const LazyRecommendedForYou = lazyLoad(() => import('@/components/recommendations/RecommendedForYou'), {
  fallback: <SectionFallback />
});

export const LazyRecentlyViewed = lazyLoad(() => import('@/components/recommendations/RecentlyViewed'), {
  fallback: <SectionFallback />
});

export const LazyFrequentlyBoughtTogether = lazyLoad(() => import('@/components/recommendations/FrequentlyBoughtTogether'), {
  fallback: <SectionFallback />
});

export const LazyTrendingProducts = lazyLoad(() => import('@/components/recommendations/TrendingProducts'), {
  fallback: <SectionFallback />
});

// Lazy load modal components
export const LazyProductDialog = lazyLoad(() => import('@/components/ProductCard/ProductDialog'), {
  fallback: <ModalFallback />
});

export const LazySignInModal = lazyLoad(() => import('@/components/auth/SignInModal'), {
  fallback: <ModalFallback />
});

export const LazySignUpModal = lazyLoad(() => import('@/components/auth/SignUpModal'), {
  fallback: <ModalFallback />
});

// Lazy load payment components
export const LazyStripePayment = lazyLoad(() => import('@/components/checkout/StripePayment'), {
  fallback: <SectionFallback />
});

export const LazyPayPalPayment = lazyLoad(() => import('@/components/checkout/PayPalPayment'), {
  fallback: <SectionFallback />
});
