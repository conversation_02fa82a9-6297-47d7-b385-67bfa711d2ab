import { useState } from 'react';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import AdminLayout from '@/components/admin/AdminLayout';
import InventoryManagement from '@/components/admin/InventoryManagement';
import ProductManagement from '@/components/admin/ProductManagement';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Package, Package2 } from 'lucide-react';

const AdminInventory = () => {
  const [activeTab, setActiveTab] = useState("inventory");

  return (
    <HelmetProvider>
      <Helmet>
        <title>Sabone | Admin - Inventory & Products</title>
        <meta name="description" content="Admin inventory and product management dashboard for Sabone." />
      </Helmet>

      <AdminLayout title="Inventory & Products">
        <Tabs
          defaultValue="inventory"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-2 w-full max-w-md mb-6 bg-sabone-charcoal border border-sabone-gold/20">
            <TabsTrigger
              value="inventory"
              className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal"
            >
              <Package className="h-4 w-4 mr-2" />
              Inventory
            </TabsTrigger>
            <TabsTrigger
              value="products"
              className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal"
            >
              <Package2 className="h-4 w-4 mr-2" />
              Products
            </TabsTrigger>
          </TabsList>

          <TabsContent value="inventory" className="mt-0">
            <InventoryManagement />
          </TabsContent>

          <TabsContent value="products" className="mt-0">
            <ProductManagement />
          </TabsContent>
        </Tabs>
      </AdminLayout>
    </HelmetProvider>
  );
};

export default AdminInventory;
