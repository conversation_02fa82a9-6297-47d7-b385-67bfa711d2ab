import React, { Suspense, lazy, useState, useEffect } from 'react';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Auth0Provider } from "@auth0/auth0-react";
import { NextIntlClientProvider } from 'next-intl';
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster as Sonner } from "@/components/ui/sonner";
import ErrorBoundary from "@/components/ErrorBoundary";
import PageLoader from "@/components/ui/PageLoader";
import ExtensionErrorBanner from "@/components/ExtensionErrorBanner";
import AuthErrorHandler from "@/components/AuthErrorHandler";
import { defaultLocale, getUserLocale, setUserLocale, getMessages } from '@/i18n/config';
import "@/i18n/ar-text-style.css";

// Core providers that are always needed
import { AuthProvider } from "@/contexts/AuthContext";
import { ProductProvider } from "@/contexts/ProductContext";
import { CartProvider } from "@/contexts/CartContext";

// Lazy load other providers
const LazyProviders = lazy(() => import('./providers/LazyProviders'));

// Lazy load pages
const Index = lazy(() => import("@/pages/Index"));
const Checkout = lazy(() => import("@/pages/Checkout"));
const ProductDetailPage = lazy(() => import("@/pages/ProductDetailPage"));
const Account = lazy(() => import("@/pages/Account"));
const SearchPage = lazy(() => import("@/pages/SearchPage"));
const WishlistPage = lazy(() => import("@/pages/WishlistPage"));
const LocalizationDemo = lazy(() => import("@/pages/LocalizationDemo"));
const DevAdminAccess = lazy(() => import("@/pages/DevAdminAccess"));
const NotFound = lazy(() => import("@/pages/NotFound"));

// Lazy load admin pages
const AdminDashboard = lazy(() => import("@/pages/admin/AdminDashboard"));
const AdminInventory = lazy(() => import("@/pages/admin/AdminInventory"));
const AdminOrders = lazy(() => import("@/pages/admin/AdminOrders"));
const AdminReviews = lazy(() => import("@/pages/admin/AdminReviews"));
const AdminCustomers = lazy(() => import("@/pages/admin/AdminCustomers"));
const AdminSettings = lazy(() => import("@/pages/admin/AdminSettings"));

// Check if we should skip Auth0 for development
const isDevelopmentMode = import.meta.env.DEV && import.meta.env.VITE_SKIP_AUTH === 'true';

// Create a single QueryClient instance that will be passed from main.tsx
interface CoreAppProps {
  children: React.ReactNode;
  queryClient: QueryClient;
}

// Core app structure with minimal providers
const CoreApp: React.FC<CoreAppProps> = ({ children, queryClient }) => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ProductProvider>
            <CartProvider>
              <TooltipProvider>
                <Sonner />
                <AuthErrorHandler />
                <BrowserRouter>
                  <Suspense fallback={<PageLoader />}>
                    <LazyProviders>
                      {children}
                    </LazyProviders>
                  </Suspense>
                </BrowserRouter>
              </TooltipProvider>
            </CartProvider>
          </ProductProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

// Routes component
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={
        <Suspense fallback={<PageLoader />}>
          <Index />
        </Suspense>
      } />
      <Route path="/checkout" element={
        <Suspense fallback={<PageLoader />}>
          <Checkout />
        </Suspense>
      } />
      <Route path="/search" element={
        <Suspense fallback={<PageLoader />}>
          <SearchPage />
        </Suspense>
      } />
      <Route path="/wishlist" element={
        <Suspense fallback={<PageLoader />}>
          <WishlistPage />
        </Suspense>
      } />
      <Route path="/localization" element={
        <Suspense fallback={<PageLoader />}>
          <LocalizationDemo />
        </Suspense>
      } />
      <Route path="/product/:id" element={
        <Suspense fallback={<PageLoader />}>
          <ProductDetailPage />
        </Suspense>
      } />
      <Route path="/account/*" element={
        <Suspense fallback={<PageLoader />}>
          <Account />
        </Suspense>
      } />

      {/* Development Admin Access */}
      <Route path="/dev-admin" element={
        <Suspense fallback={<PageLoader />}>
          <DevAdminAccess />
        </Suspense>
      } />

      {/* Admin Routes */}
      <Route path="/account/admin" element={
        <Suspense fallback={<PageLoader />}>
          <AdminDashboard />
        </Suspense>
      } />
      <Route path="/account/admin/inventory" element={
        <Suspense fallback={<PageLoader />}>
          <AdminInventory />
        </Suspense>
      } />
      <Route path="/account/admin/orders" element={
        <Suspense fallback={<PageLoader />}>
          <AdminOrders />
        </Suspense>
      } />
      <Route path="/account/admin/reviews" element={
        <Suspense fallback={<PageLoader />}>
          <AdminReviews />
        </Suspense>
      } />
      <Route path="/account/admin/customers" element={
        <Suspense fallback={<PageLoader />}>
          <AdminCustomers />
        </Suspense>
      } />
      <Route path="/account/admin/settings" element={
        <Suspense fallback={<PageLoader />}>
          <AdminSettings />
        </Suspense>
      } />

      {/* Catch-all route */}
      <Route path="*" element={
        <Suspense fallback={<PageLoader />}>
          <NotFound />
        </Suspense>
      } />
    </Routes>
  );
};

const AppOptimized: React.FC = () => {
  const [currentLocale] = useState<string>(getUserLocale());
  const [messages, setMessages] = useState<Record<string, Record<string, unknown>>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    setUserLocale(currentLocale);
    document.documentElement.lang = currentLocale;

    const loadMessages = async () => {
      setLoading(true);
      setError(false);
      try {
        const loadedMessages = await getMessages(currentLocale);
        if (!loadedMessages || Object.keys(loadedMessages).length === 0) {
          const defaultMessages = await getMessages(defaultLocale);
          setMessages(defaultMessages);
        } else {
          setMessages(loadedMessages);
        }
      } catch (err) {
        console.error("Failed to load messages:", err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [currentLocale]);

  if (loading) {
    return <PageLoader />;
  }

  if (error) {
    return (
      <div className="app-error-container">
        Error loading localization.
      </div>
    );
  }

  const appContent = (
    <CoreApp queryClient={queryClient}>
      <AppRoutes />
    </CoreApp>
  );

  return (
    <>
      <ExtensionErrorBanner />
      <NextIntlClientProvider locale={currentLocale} messages={messages}>
        {isDevelopmentMode ? (
          appContent
        ) : (
          <Auth0Provider
            domain={import.meta.env.VITE_AUTH0_DOMAIN || 'dev-placeholder.auth0.com'}
            clientId={import.meta.env.VITE_AUTH0_CLIENT_ID || 'placeholder123456'}
            authorizationParams={{
              redirect_uri: window.location.origin,
              audience: import.meta.env.VITE_AUTH0_AUDIENCE,
              scope: 'openid profile email read:current_user update:current_user_metadata'
            }}
            cacheLocation="memory"
            useRefreshTokens={true}
            useRefreshTokensFallback={false}
          >
            {appContent}
          </Auth0Provider>
        )}
      </NextIntlClientProvider>
    </>
  );
};

export default AppOptimized;
