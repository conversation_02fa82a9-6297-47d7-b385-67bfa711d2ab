import React, { useState } from 'react';
import { Filter, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
  SheetFooter,
} from '@/components/ui/sheet';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import FilterPanel from './FilterPanel';
import SortOptions from './SortOptions';
import { useSearch } from '@/contexts/SearchContext';
import { useMobile } from '@/hooks/useMobile';

interface MobileFilterDrawerProps {
  className?: string;
}

const MobileFilterDrawer: React.FC<MobileFilterDrawerProps> = ({ className: _className }) => {
  const { hasActiveFilters, clearFilters, totalResults } = useSearch();
  const { isMobile } = useMobile();
  const [isOpen, setIsOpen] = useState(false);

  if (!isMobile) {
    return null;
  }

  const handleApplyFilters = () => {
    setIsOpen(false);
  };

  const handleClearAll = () => {
    clearFilters();
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10 relative"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {hasActiveFilters && (
            <Badge
              variant="secondary"
              className="absolute -top-2 -right-2 h-5 w-5 p-0 bg-sabone-gold text-sabone-charcoal text-xs flex items-center justify-center"
            >
              !
            </Badge>
          )}
        </Button>
      </SheetTrigger>

      <SheetContent
        side="bottom"
        className="h-[90vh] bg-sabone-charcoal border-sabone-gold/30"
      >
        <SheetHeader className="pb-4">
          <SheetTitle className="text-sabone-cream flex items-center justify-between">
            <span className="flex items-center">
              <SlidersHorizontal className="h-5 w-5 mr-2" />
              Filters & Sort
            </span>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                className="text-sabone-gold hover:text-sabone-cream h-auto p-1"
              >
                Clear All
              </Button>
            )}
          </SheetTitle>
          <SheetDescription className="text-sabone-cream/60">
            {totalResults > 0 
              ? `${totalResults} ${totalResults === 1 ? 'product' : 'products'} found`
              : 'Refine your search results'
            }
          </SheetDescription>
        </SheetHeader>

        <Tabs defaultValue="filters" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-2 bg-sabone-charcoal-deep">
            <TabsTrigger 
              value="filters" 
              className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {hasActiveFilters && (
                <Badge
                  variant="secondary"
                  className="ml-2 h-4 w-4 p-0 bg-sabone-gold text-sabone-charcoal text-xs flex items-center justify-center"
                >
                  !
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger 
              value="sort"
              className="data-[state=active]:bg-sabone-gold data-[state=active]:text-sabone-charcoal"
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Sort
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden">
            <TabsContent value="filters" className="h-full mt-4">
              <div className="h-full overflow-y-auto pr-2">
                <FilterPanel 
                  showTitle={false} 
                  collapsible={true}
                  className="border-0 bg-transparent shadow-none"
                />
              </div>
            </TabsContent>

            <TabsContent value="sort" className="h-full mt-4">
              <div className="space-y-4">
                <div className="text-sm text-sabone-cream/80 mb-4">
                  Choose how to sort your results:
                </div>
                <SortOptions 
                  variant="buttons" 
                  showLabel={false}
                  className="flex-col items-start gap-3"
                />
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <SheetFooter className="pt-4 border-t border-sabone-gold/20">
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1 border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
            >
              Cancel
            </Button>
            <Button
              onClick={handleApplyFilters}
              className="flex-1 bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/90"
            >
              Apply Filters
              {totalResults > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 bg-sabone-charcoal/20 text-sabone-charcoal"
                >
                  {totalResults}
                </Badge>
              )}
            </Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default MobileFilterDrawer;
