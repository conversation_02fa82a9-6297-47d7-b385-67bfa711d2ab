import { useState, useEffect } from 'react';
import { CardElement, useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { createPaymentIntent, confirmPayment } from '@/services/paymentService';
import { Loader2, CreditCard, Lock } from 'lucide-react';

interface StripePaymentProps {
  amount: number;
  onSuccess: (paymentIntentId: string) => void;
  onError: (error: Error) => void;
  disabled?: boolean;
  clientSecret?: string;
  setClientSecret?: (secret: string) => void;
}

const StripePayment = ({
  amount,
  onSuccess,
  onError,
  disabled = false,
  clientSecret,
  setClientSecret
}: StripePaymentProps) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isInitializing, setIsInitializing] = useState(!clientSecret);
  const [cardError, setCardError] = useState<string | null>(null);
  const [_paymentMethodId, setPaymentMethodId] = useState<string | null>(null);

  // Create a payment intent when the component mounts if we don't have one
  useEffect(() => {
    const initializePayment = async () => {
      if (!clientSecret && setClientSecret) {
        setIsInitializing(true);
        setCardError(null);

        try {
          // Create a payment intent with our payment service
          const result = await createPaymentIntent(amount, 'usd');

          if (result?.clientSecret) {
            setClientSecret(result.clientSecret);
            console.log('Payment intent created successfully');
          } else {
            throw new Error('Failed to create payment intent - no client secret returned');
          }
        } catch (error) {
          console.error('Error initializing payment:', error);
          setCardError('Failed to initialize payment. Please try again.');
          onError(error instanceof Error ? error : new Error('Payment initialization failed'));
        } finally {
          setIsInitializing(false);
        }
      } else {
        setIsInitializing(false);
      }
    };

    initializePayment();
  }, [amount, clientSecret, setClientSecret, onError]);

  // Step 1: Create a payment method
  const createPaymentMethod = async () => {
    if (!stripe || !elements) {
      toast.error('Stripe has not loaded yet');
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      toast.error('Card element not found');
      return;
    }

    setIsProcessing(true);
    setCardError(null);

    try {
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (error) {
        setCardError(error.message || 'An error occurred with your payment');
        onError(error);
        return null;
      } else if (paymentMethod) {
        setPaymentMethodId(paymentMethod.id);
        return paymentMethod.id;
      }
    } catch (error) {
      console.error('Payment method creation error:', error);
      setCardError('An unexpected error occurred');
      onError(error as Error);
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  // Step 2: Process the payment with the payment method
  const processPayment = async (pmId: string) => {
    if (!stripe || !clientSecret) {
      toast.error('Payment cannot be processed at this time');
      return;
    }

    setIsProcessing(true);
    try {
      // Confirm the payment with Stripe using our payment service
      const result = await confirmPayment(stripe, clientSecret, pmId);

      if (result.success) {
        toast.success('Payment processed successfully');
        // Use the actual payment intent ID if available, otherwise fall back to the payment method ID
        onSuccess(result.paymentIntentId || pmId);
      } else {
        setCardError(result.error || 'Payment failed');
        onError(new Error(result.error || 'Payment failed'));
      }
    } catch (error) {
      console.error('Payment confirmation error:', error);
      setCardError('An unexpected error occurred during payment confirmation');
      onError(error instanceof Error ? error : new Error('Payment confirmation failed'));
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle the complete payment flow
  const handleSubmit = async () => {
    const paymentMethodId = await createPaymentMethod();
    if (paymentMethodId) {
      await processPayment(paymentMethodId);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        color: '#E5DCC5', // sabone-cream
        fontFamily: 'Arial, sans-serif',
        fontSize: '16px',
        '::placeholder': {
          color: 'rgba(229, 220, 197, 0.5)', // sabone-cream with opacity
        },
      },
      invalid: {
        color: '#EF4444', // red-500
        iconColor: '#EF4444',
      },
    },
  };

  if (isInitializing) {
    return (
      <div className="flex flex-col items-center justify-center py-6">
        <Loader2 className="h-8 w-8 animate-spin text-sabone-gold" />
        <p className="text-sabone-cream mt-4">Initializing payment...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Secure payment header */}
      <div className="flex items-center justify-center mb-2 text-sabone-gold">
        <Lock size={16} className="mr-2" />
        <span className="text-sm font-medium">Secure Payment</span>
      </div>

      {/* Card input container with enhanced styling */}
      <div className="p-5 bg-sabone-charcoal/70 rounded-md border border-sabone-gold/30 shadow-[0_0_15px_rgba(198,168,112,0.05)]">
        <div className="mb-4 flex items-center">
          <CreditCard size={18} className="text-sabone-gold mr-2" />
          <span className="text-sabone-cream font-medium">Card Information</span>
        </div>

        {clientSecret && elements?.getElement(PaymentElement) ? (
          <PaymentElement />
        ) : (
          <CardElement options={cardElementOptions} />
        )}
      </div>

      {/* Error message with improved styling */}
      {cardError && (
        <div className="bg-red-900/20 border border-red-500/30 rounded p-2 text-red-400 text-sm mt-2 flex items-start">
          <span className="mr-2 mt-0.5">⚠</span>
          <span>{cardError}</span>
        </div>
      )}

      {/* Payment button with enhanced styling */}
      <Button
        type="button"
        onClick={handleSubmit}
        className="w-full bg-gradient-to-r from-sabone-gold-rich to-sabone-gold hover:from-sabone-gold hover:to-sabone-gold-rich text-sabone-charcoal-deep font-medium py-6 transition-all duration-300 shadow-[0_4px_12px_rgba(198,168,112,0.2)] hover:shadow-[0_4px_15px_rgba(198,168,112,0.4)]"
        disabled={!stripe || isProcessing || disabled || isInitializing}
      >
        {isProcessing ? (
          <span className="flex items-center justify-center">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing Payment...
          </span>
        ) : (
          <span className="flex items-center justify-center">
            <span>Pay ${amount.toFixed(2)}</span>
          </span>
        )}
      </Button>

      {/* Security note */}
      <div className="text-center text-sabone-cream/50 text-xs mt-2 flex items-center justify-center">
        <Lock size={12} className="mr-1 text-sabone-gold/50" />
        <span>Your payment information is encrypted and secure</span>
      </div>
    </div>
  );
};

export default StripePayment;
