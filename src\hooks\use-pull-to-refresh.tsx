import { useState, useEffect, useRef } from 'react';

interface PullToRefreshOptions {
  onRefresh: () => Promise<void>;
  pullDownThreshold?: number;
  maxPullDownDistance?: number;
  refreshIndicatorHeight?: number;
  disabled?: boolean;
}

interface PullToRefreshState {
  isPulling: boolean;
  pullDistance: number;
  isRefreshing: boolean;
  containerProps: {
    onTouchStart: (e: React.TouchEvent) => void;
    onTouchMove: (e: React.TouchEvent) => void;
    onTouchEnd: () => void;
  };
}

/**
 * Custom hook for implementing pull-to-refresh functionality
 */
export function usePullToRefresh({
  onRefresh,
  pullDownThreshold = 80,
  maxPullDownDistance = 120,
  refreshIndicatorHeight = 60,
  disabled = false,
}: PullToRefreshOptions): PullToRefreshState {
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const touchStartY = useRef(0);
  const currentTouchY = useRef(0);
  const isAtScrollTop = useRef(false);

  // Reset state when disabled changes
  useEffect(() => {
    if (disabled) {
      setPullDistance(0);
      setIsPulling(false);
      setIsRefreshing(false);
    }
  }, [disabled]);

  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled || isRefreshing) return;
    
    // Store the initial touch position
    touchStartY.current = e.touches[0].clientY;
    currentTouchY.current = e.touches[0].clientY;
    
    // Check if we're at the top of the scroll container
    const target = e.currentTarget;
    isAtScrollTop.current = target.scrollTop <= 0;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (disabled || isRefreshing) return;
    
    currentTouchY.current = e.touches[0].clientY;
    
    // Only activate pull-to-refresh if we're at the top of the scroll container
    if (isAtScrollTop.current) {
      const touchDelta = currentTouchY.current - touchStartY.current;
      
      // Only activate when pulling down
      if (touchDelta > 0) {
        // Calculate pull distance with resistance (gets harder to pull the further you go)
        const newPullDistance = Math.min(
          maxPullDownDistance,
          touchDelta * 0.5 // Add resistance factor
        );
        
        setPullDistance(newPullDistance);
        setIsPulling(true);
        
        // Prevent default scrolling behavior when pulling
        e.preventDefault();
      }
    }
  };

  const handleTouchEnd = async () => {
    if (disabled || isRefreshing) return;
    
    if (isPulling) {
      if (pullDistance >= pullDownThreshold) {
        // Trigger refresh
        setIsRefreshing(true);
        setPullDistance(refreshIndicatorHeight); // Keep indicator visible during refresh
        
        try {
          await onRefresh();
        } catch (error) {
          console.error('Refresh failed:', error);
        } finally {
          // Reset after refresh completes
          setIsRefreshing(false);
          setPullDistance(0);
          setIsPulling(false);
        }
      } else {
        // Not pulled enough, reset
        setPullDistance(0);
        setIsPulling(false);
      }
    }
  };

  return {
    isPulling,
    pullDistance,
    isRefreshing,
    containerProps: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    },
  };
}
