import { useState, useEffect } from "react";
import { useWishlist } from "@/contexts/WishlistContext";

import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Heart, ShoppingCart, Trash2, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";
import OptimizedImage from "@/components/OptimizedImage";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Separator } from "@/components/ui/separator";
import { ProductPrice } from "@/components/ProductCard/ProductPrice";

const WishlistPage = () => {
  const { wishlistItems, wishlistCount, removeFromWishlist, moveToCart } = useWishlist();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Add a small delay to allow for smooth animation
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <Navbar />
      <main className="pt-20 md:pt-24 pb-16 min-h-screen bg-sabone-charcoal">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8 md:py-12">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="text-2xl md:text-3xl font-playfair font-semibold text-sabone-gold mb-2">
                  My Wishlist
                </h1>
                <p className="text-sabone-cream/70">
                  {wishlistCount > 0
                    ? `${wishlistCount} item${wishlistCount !== 1 ? "s" : ""} saved for later`
                    : "Your wishlist is empty"}
                </p>
              </div>
              <Link
                to="/"
                className="mt-4 md:mt-0 inline-flex items-center text-sabone-gold hover:text-sabone-gold-accent transition-colors"
              >
                <ArrowLeft size={16} className="mr-2" />
                Continue Shopping
              </Link>
            </div>

            <Separator className="bg-sabone-gold/20 mb-8" />

            {wishlistCount === 0 ? (
              <div className="text-center py-16 bg-sabone-dark-olive/20 rounded-lg border border-sabone-gold/10">
                <Heart className="w-16 h-16 mx-auto text-sabone-gold/30 mb-4" />
                <h2 className="text-xl font-medium text-sabone-gold mb-2">Your wishlist is empty</h2>
                <p className="text-sabone-cream/70 max-w-md mx-auto mb-6">
                  Add items to your wishlist by clicking the heart icon on products you love.
                </p>
                <Link to="/">
                  <Button className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal">
                    Explore Products
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {wishlistItems.map((product, index) => (
                  <div
                    key={product.id}
                    className={`bg-sabone-dark-olive/40 rounded-md overflow-hidden border border-sabone-gold/20 hover:border-sabone-gold/40 transition-all duration-300 transform ${
                      isLoaded
                        ? "opacity-100 translate-y-0"
                        : "opacity-0 translate-y-4"
                    }`}
                    style={{ transitionDelay: `${index * 100}ms` }}
                  >
                    <div className="relative">
                      <AspectRatio ratio={4 / 3} className="bg-sabone-charcoal-deep/50">
                        <OptimizedImage
                          src={product.image}
                          alt={product.name}
                          className="object-cover w-full h-full"
                          width={400}
                          height={300}
                        />
                      </AspectRatio>
                      <Link
                        to={`/product/${product.id}`}
                        className="absolute inset-0 z-10"
                        aria-label={`View ${product.name} details`}
                      >
                        <span className="sr-only">View product details</span>
                      </Link>
                    </div>

                    <div className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-lg font-medium text-sabone-gold">
                          <Link to={`/product/${product.id}`}>{product.name}</Link>
                        </h3>
                        <ProductPrice price={product.price} className="text-base" />
                      </div>

                      <p className="text-sabone-cream/70 text-sm mb-4 line-clamp-2">
                        {product.description}
                      </p>

                      <div className="flex gap-2 mt-auto">
                        <Button
                          onClick={() => moveToCart(product.id)}
                          className="flex-1 bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                        >
                          <ShoppingCart size={16} className="mr-2" />
                          Add to Cart
                        </Button>
                        <Button
                          onClick={() => removeFromWishlist(product.id)}
                          variant="outline"
                          size="icon"
                          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                        >
                          <Trash2 size={16} />
                          <span className="sr-only">Remove from wishlist</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
};

export default WishlistPage;
