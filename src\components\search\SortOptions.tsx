import React from 'react';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useSearch } from '@/contexts/SearchContext';
import { SortOption } from '@/contexts/SearchContext';
import { cn } from '@/lib/utils';

interface SortOptionsProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'dropdown' | 'buttons';
}

const SORT_OPTIONS: Array<{
  field: SortOption['field'];
  direction: SortOption['direction'];
  label: string;
  description: string;
}> = [
  { field: 'name', direction: 'asc', label: 'Name A-Z', description: 'Alphabetical order' },
  { field: 'name', direction: 'desc', label: 'Name Z-A', description: 'Reverse alphabetical' },
  { field: 'price', direction: 'asc', label: 'Price Low-High', description: 'Lowest price first' },
  { field: 'price', direction: 'desc', label: 'Price High-Low', description: 'Highest price first' },
  { field: 'featured', direction: 'desc', label: 'Featured First', description: 'Featured products first' },
  { field: 'type', direction: 'asc', label: 'Type', description: 'Group by product type' },
];

const SortOptions: React.FC<SortOptionsProps> = ({
  className,
  showLabel = true,
  variant = 'dropdown',
}) => {
  const { sortBy, setSortBy, totalResults } = useSearch();

  const currentSortOption = SORT_OPTIONS.find(
    option => option.field === sortBy.field && option.direction === sortBy.direction
  );

  const handleSortChange = (field: SortOption['field'], direction: SortOption['direction']) => {
    setSortBy({ field, direction });
  };

  const getSortIcon = (field: SortOption['field'], direction: SortOption['direction']) => {
    if (sortBy.field === field && sortBy.direction === direction) {
      return direction === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
    }
    return <ArrowUpDown className="h-4 w-4 opacity-50" />;
  };

  if (variant === 'buttons') {
    return (
      <div className={cn("flex flex-wrap gap-2", className)}>
        {showLabel && (
          <span className="text-sm text-sabone-cream/80 self-center">Sort by:</span>
        )}
        {SORT_OPTIONS.map((option) => {
          const isActive = sortBy.field === option.field && sortBy.direction === option.direction;
          return (
            <Button
              key={`${option.field}-${option.direction}`}
              variant={isActive ? "default" : "outline"}
              size="sm"
              onClick={() => handleSortChange(option.field, option.direction)}
              className={cn(
                "text-xs",
                isActive
                  ? "bg-sabone-gold text-sabone-charcoal"
                  : "border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10"
              )}
            >
              {getSortIcon(option.field, option.direction)}
              <span className="ml-1">{option.label}</span>
            </Button>
          );
        })}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-3", className)}>
      {showLabel && (
        <span className="text-sm text-sabone-cream/80 whitespace-nowrap">
          Sort by:
        </span>
      )}
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10 min-w-[140px] justify-between"
          >
            <span className="flex items-center">
              {getSortIcon(sortBy.field, sortBy.direction)}
              <span className="ml-2">{currentSortOption?.label || 'Sort'}</span>
            </span>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent
          align="end"
          className="w-56 bg-sabone-charcoal border-sabone-gold/30"
        >
          {SORT_OPTIONS.map((option) => {
            const isActive = sortBy.field === option.field && sortBy.direction === option.direction;
            return (
              <DropdownMenuItem
                key={`${option.field}-${option.direction}`}
                onClick={() => handleSortChange(option.field, option.direction)}
                className={cn(
                  "cursor-pointer text-sabone-cream hover:bg-sabone-gold/10",
                  isActive && "bg-sabone-gold/20"
                )}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    {getSortIcon(option.field, option.direction)}
                    <div className="ml-2">
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-sabone-cream/60">{option.description}</div>
                    </div>
                  </div>
                  {isActive && (
                    <Badge variant="secondary" className="bg-sabone-gold/30 text-sabone-charcoal text-xs">
                      Active
                    </Badge>
                  )}
                </div>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Results count */}
      {totalResults > 0 && (
        <span className="text-sm text-sabone-cream/60 whitespace-nowrap">
          {totalResults} {totalResults === 1 ? 'result' : 'results'}
        </span>
      )}
    </div>
  );
};

export default SortOptions;
